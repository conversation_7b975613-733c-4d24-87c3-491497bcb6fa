"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/SettingsModal.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/SettingsModal.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AppearanceContext */ \"(app-pages-browser)/./src/contexts/AppearanceContext.tsx\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SettingsModal(param) {\n    let { isOpen, onClose, userData, onUserDataUpdate } = param;\n    _s();\n    const { logout, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { settings: contextSettings, refreshSettings } = (0,_contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__.useAppearance)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"geral\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Estados para cada aba\n    const [generalData, setGeneralData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: userData.username,\n        profileImage: userData.profileImage || \"\",\n        currentPassword: \"\",\n        newPassword: \"\",\n        confirmPassword: \"\"\n    });\n    const [appearanceSettings, setAppearanceSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        fonte: contextSettings.fonte,\n        tamanhoFonte: contextSettings.tamanhoFonte,\n        palavrasPorSessao: contextSettings.palavrasPorSessao\n    });\n    // Sincronizar estado local com o contexto\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setAppearanceSettings({\n            fonte: contextSettings.fonte,\n            tamanhoFonte: contextSettings.tamanhoFonte,\n            palavrasPorSessao: contextSettings.palavrasPorSessao\n        });\n    }, [\n        contextSettings\n    ]);\n    const [aiEndpoints, setAiEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            nome: \"OpenRouter\",\n            url: \"https://openrouter.ai/api/v1/chat/completions\",\n            apiKey: \"\",\n            modeloPadrao: \"meta-llama/llama-3.1-8b-instruct:free\",\n            ativo: false\n        },\n        {\n            nome: \"DeepSeek\",\n            url: \"https://api.deepseek.com/v1/chat/completions\",\n            apiKey: \"\",\n            modeloPadrao: \"deepseek-chat\",\n            ativo: false\n        }\n    ]);\n    const [memories, setMemories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [memoryCategories, setMemoryCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddMemory, setShowAddMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddCategory, setShowAddCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newMemory, setNewMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        titulo: \"\",\n        conteudo: \"\",\n        cor: \"#3B82F6\",\n        categoria: null,\n        chatId: null,\n        global: true\n    });\n    const [newCategory, setNewCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        nome: \"\",\n        descricao: \"\",\n        cor: \"#3B82F6\"\n    });\n    const [showAddEndpoint, setShowAddEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newEndpoint, setNewEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        nome: \"\",\n        url: \"\",\n        apiKey: \"\",\n        modeloPadrao: \"\",\n        ativo: false\n    });\n    const [editingEndpoint, setEditingEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editEndpointData, setEditEndpointData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        nome: \"\",\n        url: \"\",\n        apiKey: \"\",\n        modeloPadrao: \"\",\n        ativo: false\n    });\n    // Carregar configurações do Firestore\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadConfigurations = async ()=>{\n            if (!userData.username) return;\n            try {\n                console.log(\"Carregando configura\\xe7\\xf5es para:\", userData.username);\n                const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\"));\n                if (configDoc.exists()) {\n                    const config = configDoc.data();\n                    console.log(\"Configura\\xe7\\xf5es carregadas:\", config);\n                    if (config.aparencia) {\n                        setAppearanceSettings(config.aparencia);\n                    }\n                    if (config.endpoints) {\n                        const endpointsArray = Object.values(config.endpoints);\n                        setAiEndpoints(endpointsArray);\n                    } else {\n                        // Manter endpoints padrão se não houver configuração salva\n                        setAiEndpoints([\n                            {\n                                nome: \"OpenRouter\",\n                                url: \"https://openrouter.ai/api/v1/chat/completions\",\n                                apiKey: \"\",\n                                modeloPadrao: \"meta-llama/llama-3.1-8b-instruct:free\",\n                                ativo: false\n                            },\n                            {\n                                nome: \"DeepSeek\",\n                                url: \"https://api.deepseek.com/v1/chat/completions\",\n                                apiKey: \"\",\n                                modeloPadrao: \"deepseek-chat\",\n                                ativo: false\n                            }\n                        ]);\n                    }\n                    if (config.memorias) {\n                        const memoriasArray = Object.values(config.memorias);\n                        setMemories(memoriasArray);\n                    }\n                    if (config.categorias) {\n                        const categoriasArray = Object.values(config.categorias);\n                        setMemoryCategories(categoriasArray);\n                    }\n                } else {\n                    console.log(\"Nenhuma configura\\xe7\\xe3o encontrada, usando padr\\xf5es\");\n                    // Configurações padrão se não existir documento\n                    setAppearanceSettings({\n                        fonte: \"Inter\",\n                        tamanhoFonte: 14,\n                        palavrasPorSessao: 5000\n                    });\n                }\n            } catch (error) {\n                console.error(\"Erro ao carregar configura\\xe7\\xf5es:\", error);\n            }\n        };\n        if (isOpen && userData.username) {\n            // Reset do estado do formulário geral quando abrir o modal\n            setGeneralData({\n                username: userData.username,\n                profileImage: userData.profileImage || \"\",\n                currentPassword: \"\",\n                newPassword: \"\",\n                confirmPassword: \"\"\n            });\n            loadConfigurations();\n            loadChats();\n        }\n    }, [\n        isOpen,\n        userData.username,\n        userData.profileImage\n    ]);\n    // Função para carregar chats do usuário\n    const loadChats = async ()=>{\n        if (!(userData === null || userData === void 0 ? void 0 : userData.username)) return;\n        try {\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", userData.username, \"conversas\");\n            const chatsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)(chatsRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.orderBy)(\"lastUpdatedAt\", \"desc\"));\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(chatsQuery);\n            const loadedChats = [];\n            chatsSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                loadedChats.push({\n                    id: doc.id,\n                    name: data.name || \"Conversa sem nome\",\n                    lastMessage: data.ultimaMensagem || \"Nenhuma mensagem ainda\",\n                    lastMessageTime: data.ultimaMensagemEm || data.createdAt\n                });\n            });\n            setChats(loadedChats);\n        } catch (error) {\n            console.error(\"Erro ao carregar chats:\", error);\n        }\n    };\n    // Função auxiliar para deletar todos os dados do Storage de um usuário\n    const deleteUserStorageData = async (username)=>{\n        try {\n            console.log(\"Iniciando exclus\\xe3o de dados do Storage para:\", username);\n            // Deletar toda a pasta do usuário no Storage\n            const userStorageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.storage, \"usuarios/\".concat(username));\n            const userStorageList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.listAll)(userStorageRef);\n            // Função recursiva para deletar pastas e arquivos\n            const deleteRecursively = async (folderRef)=>{\n                const folderList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.listAll)(folderRef);\n                // Deletar todos os arquivos na pasta atual\n                const fileDeletePromises = folderList.items.map((item)=>(0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.deleteObject)(item));\n                await Promise.all(fileDeletePromises);\n                // Deletar recursivamente todas as subpastas\n                const folderDeletePromises = folderList.prefixes.map((prefix)=>deleteRecursively(prefix));\n                await Promise.all(folderDeletePromises);\n            };\n            await deleteRecursively(userStorageRef);\n            console.log(\"Todos os dados do Storage deletados para:\", username);\n        } catch (error) {\n            console.log(\"Erro ao deletar dados do Storage ou pasta n\\xe3o encontrada:\", error);\n        }\n    };\n    // Função auxiliar para deletar recursivamente todos os documentos de um usuário\n    const deleteUserDocuments = async (username)=>{\n        try {\n            console.log(\"Iniciando exclus\\xe3o de documentos para:\", username);\n            // Deletar subcoleção de configurações\n            try {\n                const configDoc = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n                const configSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(configDoc);\n                if (configSnapshot.exists()) {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.deleteDoc)(configDoc);\n                    console.log(\"Configura\\xe7\\xf5es deletadas\");\n                }\n            } catch (error) {\n                console.log(\"Erro ao deletar configura\\xe7\\xf5es:\", error);\n            }\n            // Deletar outras subcoleções se existirem (chats, histórico, etc.)\n            try {\n                const chatsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", username, \"chats\");\n                const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(chatsCollection);\n                const deletePromises = chatsSnapshot.docs.map((doc)=>(0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.deleteDoc)(doc.ref));\n                await Promise.all(deletePromises);\n                console.log(\"Chats deletados\");\n            } catch (error) {\n                console.log(\"Erro ao deletar chats:\", error);\n            }\n            // Deletar documento principal do usuário\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", username);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.deleteDoc)(userDocRef);\n            console.log(\"Documento principal do usu\\xe1rio deletado\");\n        } catch (error) {\n            console.error(\"Erro ao deletar documentos do usu\\xe1rio:\", error);\n            throw error;\n        }\n    };\n    // Atualizar username no documento principal\n    const updateUsername = async function(newUsername) {\n        let showSuccessAlert = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        if (!userData.username || !newUsername || newUsername === userData.username) {\n            if (showSuccessAlert) alert(\"Nome de usu\\xe1rio inv\\xe1lido ou igual ao atual.\");\n            return false;\n        }\n        if (newUsername.length < 3) {\n            if (showSuccessAlert) alert(\"Nome de usu\\xe1rio deve ter pelo menos 3 caracteres.\");\n            return false;\n        }\n        let newUserCreated = false;\n        try {\n            console.log(\"Atualizando username de\", userData.username, \"para\", newUsername);\n            // Verificar se o novo username já existe\n            const newUserDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", newUsername));\n            if (newUserDoc.exists()) {\n                if (showSuccessAlert) alert(\"Este nome de usu\\xe1rio j\\xe1 est\\xe1 em uso. Escolha outro.\");\n                return false;\n            }\n            // Buscar o documento atual pelo username antigo\n            const oldUserDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", userData.username);\n            const oldUserDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(oldUserDocRef);\n            if (!oldUserDoc.exists()) {\n                if (showSuccessAlert) alert(\"Usu\\xe1rio n\\xe3o encontrado.\");\n                return false;\n            }\n            const currentData = oldUserDoc.data();\n            // Criar novo documento com o novo username\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", newUsername), {\n                ...currentData,\n                username: newUsername,\n                updatedAt: new Date().toISOString()\n            });\n            newUserCreated = true;\n            console.log(\"Novo documento criado para:\", newUsername);\n            // Copiar todas as configurações e subcoleções\n            try {\n                // Copiar configurações principais\n                const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\"));\n                if (configDoc.exists()) {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", newUsername, \"configuracoes\", \"settings\"), configDoc.data());\n                    console.log(\"Configura\\xe7\\xf5es copiadas para novo username\");\n                }\n                // Copiar chats se existirem\n                try {\n                    const chatsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", userData.username, \"chats\");\n                    const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(chatsCollection);\n                    for (const chatDoc of chatsSnapshot.docs){\n                        const chatData = chatDoc.data();\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", newUsername, \"chats\", chatDoc.id), chatData);\n                    }\n                    if (chatsSnapshot.docs.length > 0) {\n                        console.log(\"\".concat(chatsSnapshot.docs.length, \" chats copiados para novo username\"));\n                    }\n                } catch (chatsError) {\n                    console.log(\"Erro ao copiar chats:\", chatsError);\n                }\n            } catch (configError) {\n                console.log(\"Erro ao copiar dados:\", configError);\n            }\n            // Deletar todos os documentos do usuário antigo\n            await deleteUserDocuments(userData.username);\n            console.log(\"Todos os documentos do usu\\xe1rio antigo foram deletados\");\n            // Atualizar estado local\n            onUserDataUpdate({\n                ...userData,\n                username: newUsername\n            });\n            if (showSuccessAlert) alert(\"Nome de usu\\xe1rio atualizado com sucesso!\");\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar username:\", error);\n            // Se houve erro e o novo usuário foi criado, tentar fazer rollback\n            if (newUserCreated) {\n                try {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", newUsername));\n                    console.log(\"Rollback realizado - novo usu\\xe1rio deletado\");\n                } catch (rollbackError) {\n                    console.error(\"Erro no rollback:\", rollbackError);\n                }\n            }\n            if (showSuccessAlert) alert(\"Erro ao atualizar nome de usu\\xe1rio: \".concat(error instanceof Error ? error.message : \"Erro desconhecido\"));\n            return false;\n        }\n    };\n    // Salvar configurações no Firestore\n    const saveConfigurations = async ()=>{\n        if (!userData.username) {\n            alert(\"Erro: usu\\xe1rio n\\xe3o identificado\");\n            return;\n        }\n        try {\n            setLoading(true);\n            // Verificar se o username foi alterado e atualizá-lo primeiro\n            if (generalData.username !== userData.username) {\n                const usernameUpdated = await updateUsername(generalData.username, false);\n                if (!usernameUpdated) {\n                    // Se falhou ao atualizar o username, interromper o processo\n                    return;\n                }\n            }\n            // Determinar qual username usar (o novo se foi alterado)\n            const currentUsername = generalData.username !== userData.username ? generalData.username : userData.username;\n            const configData = {\n                aparencia: {\n                    fonte: appearanceSettings.fonte,\n                    tamanhoFonte: appearanceSettings.tamanhoFonte,\n                    palavrasPorSessao: appearanceSettings.palavrasPorSessao\n                },\n                endpoints: {},\n                memorias: {},\n                categorias: {},\n                updatedAt: new Date().toISOString()\n            };\n            // Converter arrays para objetos\n            aiEndpoints.forEach((endpoint, index)=>{\n                configData.endpoints[endpoint.nome || \"endpoint_\".concat(index)] = endpoint;\n            });\n            memories.forEach((memory, index)=>{\n                configData.memorias[\"memoria_\".concat(index)] = memory;\n            });\n            memoryCategories.forEach((category, index)=>{\n                configData.categorias[category.nome || \"categoria_\".concat(index)] = category;\n            });\n            console.log(\"Salvando configura\\xe7\\xf5es para:\", currentUsername);\n            console.log(\"Dados a serem salvos:\", configData);\n            // Usar setDoc com merge para não sobrescrever outros dados\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", currentUsername, \"configuracoes\", \"settings\");\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.setDoc)(docRef, configData);\n            console.log(\"Configura\\xe7\\xf5es salvas com sucesso no Firestore\");\n            // Atualizar as configurações de aparência no contexto\n            await refreshSettings();\n            alert(\"Configura\\xe7\\xf5es salvas com sucesso!\");\n        } catch (error) {\n            console.error(\"Erro ao salvar configura\\xe7\\xf5es:\", error);\n            alert(\"Erro ao salvar configura\\xe7\\xf5es: \".concat(error instanceof Error ? error.message : \"Erro desconhecido\"));\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    // Funções utilitárias\n    const handleProfileImageUpload = async (file)=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const imageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.storage, \"usuarios/\".concat(userData.username, \"/profile.jpg\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.uploadBytes)(imageRef, file);\n            const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.getDownloadURL)(imageRef);\n            setGeneralData((prev)=>({\n                    ...prev,\n                    profileImage: downloadURL\n                }));\n            // Atualizar no Firestore\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", userData.username);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)(userDocRef, {\n                profileImage: downloadURL\n            });\n            onUserDataUpdate({\n                ...userData,\n                profileImage: downloadURL\n            });\n            alert(\"Foto de perfil atualizada com sucesso!\");\n        } catch (error) {\n            console.error(\"Erro ao fazer upload da imagem:\", error);\n            alert(\"Erro ao atualizar foto de perfil.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePasswordChange = async ()=>{\n        if (!user || !generalData.currentPassword || !generalData.newPassword) {\n            alert(\"Preencha todos os campos de senha.\");\n            return;\n        }\n        if (generalData.newPassword !== generalData.confirmPassword) {\n            alert(\"As senhas n\\xe3o coincidem.\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const credential = firebase_auth__WEBPACK_IMPORTED_MODULE_6__.EmailAuthProvider.credential(user.email, generalData.currentPassword);\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_6__.reauthenticateWithCredential)(user, credential);\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_6__.updatePassword)(user, generalData.newPassword);\n            setGeneralData((prev)=>({\n                    ...prev,\n                    currentPassword: \"\",\n                    newPassword: \"\",\n                    confirmPassword: \"\"\n                }));\n            alert(\"Senha alterada com sucesso!\");\n        } catch (error) {\n            console.error(\"Erro ao alterar senha:\", error);\n            alert(\"Erro ao alterar senha. Verifique a senha atual.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogout = async ()=>{\n        if (confirm(\"Tem certeza que deseja sair?\")) {\n            await logout();\n            onClose();\n        }\n    };\n    // Funções para gerenciar endpoints de IA\n    const handleAddEndpoint = ()=>{\n        if (!newEndpoint.nome || !newEndpoint.url || !newEndpoint.apiKey) {\n            alert(\"Preencha todos os campos obrigat\\xf3rios.\");\n            return;\n        }\n        setAiEndpoints((prev)=>[\n                ...prev,\n                {\n                    ...newEndpoint\n                }\n            ]);\n        setNewEndpoint({\n            nome: \"\",\n            url: \"\",\n            apiKey: \"\",\n            modeloPadrao: \"\",\n            ativo: false\n        });\n        setShowAddEndpoint(false);\n        alert(\"Endpoint adicionado com sucesso!\");\n    };\n    const handleToggleEndpoint = (index)=>{\n        setAiEndpoints((prev)=>prev.map((endpoint, i)=>i === index ? {\n                    ...endpoint,\n                    ativo: !endpoint.ativo\n                } : endpoint));\n    };\n    const handleDeleteEndpoint = (index)=>{\n        if (confirm(\"Tem certeza que deseja deletar este endpoint?\")) {\n            setAiEndpoints((prev)=>prev.filter((_, i)=>i !== index));\n        }\n    };\n    const handleEditEndpoint = (index)=>{\n        const endpoint = aiEndpoints[index];\n        setEditEndpointData({\n            ...endpoint\n        });\n        setEditingEndpoint(index);\n    };\n    const handleSaveEditEndpoint = ()=>{\n        if (editingEndpoint === null) return;\n        if (!editEndpointData.apiKey || !editEndpointData.modeloPadrao) {\n            alert(\"API Key e Modelo Padr\\xe3o s\\xe3o obrigat\\xf3rios.\");\n            return;\n        }\n        setAiEndpoints((prev)=>prev.map((endpoint, i)=>i === editingEndpoint ? {\n                    ...editEndpointData\n                } : endpoint));\n        setEditingEndpoint(null);\n        setEditEndpointData({\n            nome: \"\",\n            url: \"\",\n            apiKey: \"\",\n            modeloPadrao: \"\",\n            ativo: false\n        });\n        alert(\"Endpoint atualizado com sucesso!\");\n    };\n    const handleCancelEditEndpoint = ()=>{\n        setEditingEndpoint(null);\n        setEditEndpointData({\n            nome: \"\",\n            url: \"\",\n            apiKey: \"\",\n            modeloPadrao: \"\",\n            ativo: false\n        });\n    };\n    const handleTestEndpoint = async (endpoint)=>{\n        if (!endpoint.apiKey) {\n            alert(\"API Key \\xe9 necess\\xe1ria para testar o endpoint.\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const response = await fetch(endpoint.url, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(endpoint.apiKey)\n                },\n                body: JSON.stringify({\n                    model: endpoint.modeloPadrao || \"gpt-3.5-turbo\",\n                    messages: [\n                        {\n                            role: \"user\",\n                            content: \"Test message\"\n                        }\n                    ],\n                    max_tokens: 10\n                })\n            });\n            if (response.ok) {\n                alert(\"✅ Endpoint testado com sucesso!\");\n            } else {\n                alert(\"❌ Erro ao testar endpoint. Verifique as configura\\xe7\\xf5es.\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao testar endpoint:\", error);\n            alert(\"❌ Erro ao conectar com o endpoint.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Funções para gerenciar memórias\n    const handleAddCategory = ()=>{\n        if (!newCategory.nome) {\n            alert(\"Nome da categoria \\xe9 obrigat\\xf3rio.\");\n            return;\n        }\n        setMemoryCategories((prev)=>[\n                ...prev,\n                {\n                    ...newCategory\n                }\n            ]);\n        setNewCategory({\n            nome: \"\",\n            descricao: \"\",\n            cor: \"#3B82F6\"\n        });\n        setShowAddCategory(false);\n        alert(\"Categoria criada com sucesso!\");\n    };\n    const handleAddMemory = ()=>{\n        if (!newMemory.titulo || !newMemory.conteudo) {\n            alert(\"T\\xedtulo e conte\\xfado s\\xe3o obrigat\\xf3rios.\");\n            return;\n        }\n        setMemories((prev)=>[\n                ...prev,\n                {\n                    ...newMemory\n                }\n            ]);\n        setNewMemory({\n            titulo: \"\",\n            conteudo: \"\",\n            cor: \"#3B82F6\",\n            categoria: null,\n            chatId: null,\n            global: true\n        });\n        setShowAddMemory(false);\n        alert(\"Mem\\xf3ria criada com sucesso!\");\n    };\n    const handleDeleteMemory = (index)=>{\n        if (confirm(\"Tem certeza que deseja deletar esta mem\\xf3ria?\")) {\n            setMemories((prev)=>prev.filter((_, i)=>i !== index));\n        }\n    };\n    const handleDeleteCategory = (index)=>{\n        if (confirm(\"Tem certeza que deseja deletar esta categoria?\")) {\n            setMemoryCategories((prev)=>prev.filter((_, i)=>i !== index));\n        }\n    };\n    const colors = [\n        \"#3B82F6\",\n        \"#EF4444\",\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#8B5CF6\",\n        \"#EC4899\",\n        \"#06B6D4\",\n        \"#84CC16\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    scale: 0.95,\n                    opacity: 0\n                },\n                animate: {\n                    scale: 1,\n                    opacity: 1\n                },\n                exit: {\n                    scale: 0.95,\n                    opacity: 0\n                },\n                className: \"bg-gradient-to-br from-blue-900/95 to-blue-800/95 backdrop-blur-sm border border-white/20 rounded-2xl w-full max-w-6xl max-h-[95vh] overflow-hidden mx-4 lg:mx-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 lg:p-6 border-b border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl lg:text-2xl font-bold text-white\",\n                                        children: \"Configura\\xe7\\xf5es\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 748,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-sm lg:hidden mt-1\",\n                                        children: [\n                                            activeTab === \"geral\" && \"Informa\\xe7\\xf5es pessoais e senha\",\n                                            activeTab === \"aparencia\" && \"Personaliza\\xe7\\xe3o da interface\",\n                                            activeTab === \"ia\" && \"Endpoints de intelig\\xeancia artificial\",\n                                            activeTab === \"memoria\" && \"Sistema de mem\\xf3rias\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 747,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-white/60 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 756,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                        lineNumber: 746,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full lg:w-64 bg-white/5 border-b lg:border-b-0 lg:border-r border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"p-2 lg:p-4 space-y-1 lg:space-y-2 overflow-x-auto lg:overflow-x-visible\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex lg:flex-col space-x-2 lg:space-x-0 lg:space-y-2 min-w-max lg:min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"geral\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"geral\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"Geral\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"aparencia\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"aparencia\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 795,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"Apar\\xeancia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"ia\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"ia\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 809,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"IA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 801,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"memoria\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"memoria\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 825,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"Mem\\xf3ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 768,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 lg:p-6 overflow-y-auto max-h-[calc(95vh-200px)]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: [\n                                        activeTab === \"geral\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Configura\\xe7\\xf5es Gerais\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Foto de Perfil\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: generalData.profileImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: generalData.profileImage,\n                                                                            alt: \"Profile\",\n                                                                            className: \"w-20 h-20 rounded-full object-cover border-2 border-white/20\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 854,\n                                                                            columnNumber: 33\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-white font-bold text-2xl\",\n                                                                                children: userData.username.charAt(0).toUpperCase()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 861,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 860,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    var _fileInputRef_current;\n                                                                                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                                                },\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                                children: \"Alterar Foto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 868,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/60 text-sm mt-2\",\n                                                                                children: \"JPG, PNG ou GIF. M\\xe1ximo 5MB.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 875,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 867,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 851,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                ref: fileInputRef,\n                                                                type: \"file\",\n                                                                accept: \"image/*\",\n                                                                onChange: (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) handleProfileImageUpload(file);\n                                                                },\n                                                                className: \"hidden\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 880,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 849,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Nome de Usu\\xe1rio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: generalData.username,\n                                                                        onChange: (e)=>setGeneralData((prev)=>({\n                                                                                    ...prev,\n                                                                                    username: e.target.value\n                                                                                })),\n                                                                        className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        placeholder: \"Digite seu nome de usu\\xe1rio\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 896,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    generalData.username !== userData.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-yellow-300 text-sm\",\n                                                                            children: '⚠️ Nome de usu\\xe1rio alterado. Clique em \"Salvar Configura\\xe7\\xf5es\" para aplicar as mudan\\xe7as.'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 907,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 906,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 895,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Alterar Senha\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 917,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Senha Atual\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 920,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: generalData.currentPassword,\n                                                                                onChange: (e)=>setGeneralData((prev)=>({\n                                                                                            ...prev,\n                                                                                            currentPassword: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Digite sua senha atual\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 923,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 919,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Nova Senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 934,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: generalData.newPassword,\n                                                                                onChange: (e)=>setGeneralData((prev)=>({\n                                                                                            ...prev,\n                                                                                            newPassword: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Digite sua nova senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 937,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 933,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Confirmar Nova Senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 948,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: generalData.confirmPassword,\n                                                                                onChange: (e)=>setGeneralData((prev)=>({\n                                                                                            ...prev,\n                                                                                            confirmPassword: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Confirme sua nova senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 951,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 947,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handlePasswordChange,\n                                                                        disabled: loading,\n                                                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: loading ? \"Alterando...\" : \"Alterar Senha\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 961,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 918,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"geral\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 21\n                                        }, this),\n                                        activeTab === \"aparencia\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Configura\\xe7\\xf5es de Apar\\xeancia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Fonte do Chat\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 988,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Fam\\xedlia da Fonte\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 991,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                value: appearanceSettings.fonte,\n                                                                                onChange: (e)=>setAppearanceSettings((prev)=>({\n                                                                                            ...prev,\n                                                                                            fonte: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Inter\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Inter\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1001,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Roboto\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Roboto\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1002,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"JetBrains Mono\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"JetBrains Mono\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1003,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Lato\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Lato\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1004,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Fira Code\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Fira Code\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1005,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Merriweather\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Merriweather\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1006,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Open Sans\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Open Sans\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1007,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Source Sans Pro\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Source Sans Pro\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1008,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Poppins\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Poppins\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1009,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Nunito\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Nunito\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1010,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 994,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 990,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 border border-white/10 rounded-lg p-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/80 text-sm mb-2\",\n                                                                                children: \"Pr\\xe9-visualiza\\xe7\\xe3o:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1015,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-white p-3 bg-white/5 rounded border border-white/10\",\n                                                                                style: {\n                                                                                    fontFamily: appearanceSettings.fonte,\n                                                                                    fontSize: \"\".concat(appearanceSettings.tamanhoFonte, \"px\")\n                                                                                },\n                                                                                children: \"Esta \\xe9 uma mensagem de exemplo para visualizar a fonte selecionada. Lorem ipsum dolor sit amet, consectetur adipiscing elit.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1016,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1014,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 987,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Tamanho da Fonte\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1029,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                            children: [\n                                                                                \"Tamanho: \",\n                                                                                appearanceSettings.tamanhoFonte,\n                                                                                \"px\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1032,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"range\",\n                                                                            min: \"10\",\n                                                                            max: \"24\",\n                                                                            value: appearanceSettings.tamanhoFonte,\n                                                                            onChange: (e)=>setAppearanceSettings((prev)=>({\n                                                                                        ...prev,\n                                                                                        tamanhoFonte: parseInt(e.target.value)\n                                                                                    })),\n                                                                            className: \"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1035,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between text-xs text-white/60 mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"10px\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 1044,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"24px\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 1045,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1043,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                    lineNumber: 1031,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1030,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Sess\\xf5es de Chat\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1053,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                        className: \"text-white font-medium\",\n                                                                                        children: \"Divis\\xe3o Autom\\xe1tica\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1057,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white/60 text-sm\",\n                                                                                        children: \"Dividir chats longos em sess\\xf5es baseadas na contagem de palavras\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1058,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1056,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"bg-blue-600 relative inline-flex h-6 w-11 items-center rounded-full transition-colors\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"translate-x-6 inline-block h-4 w-4 transform rounded-full bg-white transition\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 1065,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1062,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1055,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: [\n                                                                                    \"Palavras por Sess\\xe3o: \",\n                                                                                    appearanceSettings.palavrasPorSessao.toLocaleString()\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1070,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"range\",\n                                                                                min: \"1000\",\n                                                                                max: \"20000\",\n                                                                                step: \"500\",\n                                                                                value: appearanceSettings.palavrasPorSessao,\n                                                                                onChange: (e)=>setAppearanceSettings((prev)=>({\n                                                                                            ...prev,\n                                                                                            palavrasPorSessao: parseInt(e.target.value)\n                                                                                        })),\n                                                                                className: \"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1073,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs text-white/60 mt-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"1.000\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1083,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"20.000\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1084,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1082,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1069,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-blue-300 text-sm\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCA1 \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Dica:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 1090,\n                                                                                    columnNumber: 36\n                                                                                }, this),\n                                                                                \" Sess\\xf5es menores carregam mais r\\xe1pido, mas podem fragmentar conversas longas. Recomendamos entre 3.000-8.000 palavras para melhor experi\\xeancia.\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1089,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1088,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1054,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1052,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 983,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"aparencia\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 976,\n                                            columnNumber: 21\n                                        }, this),\n                                        activeTab === \"ia\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Intelig\\xeancia Artificial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1109,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80\",\n                                                                children: \"Gerencie seus endpoints de IA personalizados\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1113,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowAddEndpoint(!showAddEndpoint),\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M12 4v16m8-8H4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1122,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1121,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Adicionar Endpoint\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1124,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1116,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1112,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    showAddEndpoint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: \"auto\"\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Novo Endpoint\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1136,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Nome do Endpoint *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1139,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newEndpoint.nome,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            nome: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Ex: Meu Endpoint\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1142,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1138,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"URL do Endpoint *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1153,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"url\",\n                                                                                value: newEndpoint.url,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            url: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"https://api.exemplo.com/v1/chat/completions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1156,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1152,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"API Key *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1167,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: newEndpoint.apiKey,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            apiKey: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"sk-...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1170,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1166,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Modelo Padr\\xe3o\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1181,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newEndpoint.modeloPadrao,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            modeloPadrao: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"gpt-3.5-turbo\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1184,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1180,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1137,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3 mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowAddEndpoint(false),\n                                                                        className: \"px-4 py-2 text-white/70 hover:text-white transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1196,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleAddEndpoint,\n                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: \"Adicionar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1202,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1195,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1130,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: aiEndpoints.map((endpoint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-3 h-3 rounded-full \".concat(endpoint.ativo ? \"bg-green-500\" : \"bg-gray-500\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1219,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-lg font-semibold text-white\",\n                                                                                        children: endpoint.nome\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1220,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    (endpoint.nome === \"OpenRouter\" || endpoint.nome === \"DeepSeek\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full\",\n                                                                                        children: \"Pr\\xe9-configurado\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1222,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1218,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleToggleEndpoint(index),\n                                                                                        className: \"px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200 \".concat(endpoint.ativo ? \"bg-green-600 hover:bg-green-700 text-white\" : \"bg-gray-600 hover:bg-gray-700 text-white\"),\n                                                                                        children: endpoint.ativo ? \"Ativo\" : \"Inativo\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1228,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleTestEndpoint(endpoint),\n                                                                                        disabled: loading || !endpoint.apiKey,\n                                                                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Testar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1238,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleEditEndpoint(index),\n                                                                                        className: \"bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Editar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1246,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    endpoint.nome !== \"OpenRouter\" && endpoint.nome !== \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteEndpoint(index),\n                                                                                        className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Deletar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1254,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1227,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1217,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"URL:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1267,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white font-mono text-xs break-all\",\n                                                                                        children: endpoint.url\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1268,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1266,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"Modelo:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1271,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white\",\n                                                                                        children: endpoint.modeloPadrao || \"N\\xe3o especificado\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1272,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1270,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"API Key:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1275,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white font-mono text-xs\",\n                                                                                        children: endpoint.apiKey ? \"••••••••••••\" + endpoint.apiKey.slice(-4) : \"N\\xe3o configurada\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1276,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1274,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"Status:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1281,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium \".concat(endpoint.ativo ? \"text-green-400\" : \"text-gray-400\"),\n                                                                                        children: endpoint.ativo ? \"Ativo\" : \"Inativo\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1282,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1280,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1265,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    editingEndpoint === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                                        initial: {\n                                                                            opacity: 0,\n                                                                            height: 0\n                                                                        },\n                                                                        animate: {\n                                                                            opacity: 1,\n                                                                            height: \"auto\"\n                                                                        },\n                                                                        exit: {\n                                                                            opacity: 0,\n                                                                            height: 0\n                                                                        },\n                                                                        className: \"mt-4 pt-4 border-t border-white/10\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                className: \"text-white font-semibold mb-4\",\n                                                                                children: \"Editar Endpoint\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1296,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                                children: \"API Key *\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1299,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"password\",\n                                                                                                value: editEndpointData.apiKey,\n                                                                                                onChange: (e)=>setEditEndpointData((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            apiKey: e.target.value\n                                                                                                        })),\n                                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n                                                                                                placeholder: \"Cole sua API Key aqui...\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1302,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1298,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                                children: \"Modelo Padr\\xe3o *\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1313,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: editEndpointData.modeloPadrao,\n                                                                                                onChange: (e)=>setEditEndpointData((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            modeloPadrao: e.target.value\n                                                                                                        })),\n                                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n                                                                                                placeholder: \"Ex: gpt-4, claude-3-sonnet, etc.\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1316,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1312,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1297,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-end space-x-2 mt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: handleCancelEditEndpoint,\n                                                                                        className: \"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Cancelar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1328,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: handleSaveEditEndpoint,\n                                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Salvar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1335,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1327,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1290,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    (endpoint.nome === \"OpenRouter\" || endpoint.nome === \"DeepSeek\") && !endpoint.apiKey && editingEndpoint !== index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-4 pt-4 border-t border-white/10\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Configure sua API Key:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1349,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"password\",\n                                                                                        placeholder: \"Cole sua API Key aqui...\",\n                                                                                        className: \"flex-1 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n                                                                                        onChange: (e)=>{\n                                                                                            const newKey = e.target.value;\n                                                                                            setAiEndpoints((prev)=>prev.map((ep, i)=>i === index ? {\n                                                                                                        ...ep,\n                                                                                                        apiKey: newKey\n                                                                                                    } : ep));\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1353,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleToggleEndpoint(index),\n                                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Salvar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1366,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1352,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1348,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1216,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1214,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 1108,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"ia\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 1101,\n                                            columnNumber: 21\n                                        }, this),\n                                        activeTab === \"memoria\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Sistema de Mem\\xf3ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1392,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-3 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowAddCategory(!showAddCategory),\n                                                                className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1402,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1401,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Nova Categoria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1405,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1396,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowAddMemory(!showAddMemory),\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1413,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1412,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Nova Mem\\xf3ria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1416,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1407,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1395,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    showAddCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: \"auto\"\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Nova Categoria\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1428,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Nome da Categoria *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1431,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newCategory.nome,\n                                                                                onChange: (e)=>setNewCategory((prev)=>({\n                                                                                            ...prev,\n                                                                                            nome: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Ex: Trabalho, Pessoal, Projetos...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1434,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1430,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Descri\\xe7\\xe3o\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1445,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                value: newCategory.descricao,\n                                                                                onChange: (e)=>setNewCategory((prev)=>({\n                                                                                            ...prev,\n                                                                                            descricao: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                                                                                rows: 3,\n                                                                                placeholder: \"Descreva o prop\\xf3sito desta categoria...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1448,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1444,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Cor da Categoria\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1459,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: colors.map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>setNewCategory((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    cor: color\n                                                                                                })),\n                                                                                        className: \"w-8 h-8 rounded-full border-2 transition-all duration-200 \".concat(newCategory.cor === color ? \"border-white scale-110\" : \"border-white/30\"),\n                                                                                        style: {\n                                                                                            backgroundColor: color\n                                                                                        }\n                                                                                    }, color, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1464,\n                                                                                        columnNumber: 37\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1462,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1458,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1429,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3 mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowAddCategory(false),\n                                                                        className: \"px-4 py-2 text-white/70 hover:text-white transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1477,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleAddCategory,\n                                                                        className: \"bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: \"Criar Categoria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1483,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1476,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1422,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    showAddMemory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: \"auto\"\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Nova Mem\\xf3ria\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1502,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"T\\xedtulo da Mem\\xf3ria *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1505,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newMemory.titulo,\n                                                                                onChange: (e)=>setNewMemory((prev)=>({\n                                                                                            ...prev,\n                                                                                            titulo: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Ex: Informa\\xe7\\xf5es importantes sobre...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1508,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1504,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Conte\\xfado *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1519,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                value: newMemory.conteudo,\n                                                                                onChange: (e)=>setNewMemory((prev)=>({\n                                                                                            ...prev,\n                                                                                            conteudo: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                                                                                rows: 4,\n                                                                                placeholder: \"Digite o conte\\xfado da mem\\xf3ria...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1522,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1518,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                        children: \"Categoria\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1534,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                        value: newMemory.categoria || \"\",\n                                                                                        onChange: (e)=>setNewMemory((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    categoria: e.target.value || null\n                                                                                                })),\n                                                                                        className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"\",\n                                                                                                className: \"bg-gray-800\",\n                                                                                                children: \"Sem categoria\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1544,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            memoryCategories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: category.nome,\n                                                                                                    className: \"bg-gray-800\",\n                                                                                                    children: category.nome\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                    lineNumber: 1546,\n                                                                                                    columnNumber: 39\n                                                                                                }, this))\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1537,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1533,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                        children: \"Cor da Mem\\xf3ria\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1553,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex space-x-2\",\n                                                                                        children: colors.slice(0, 4).map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>setNewMemory((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            cor: color\n                                                                                                        })),\n                                                                                                className: \"w-8 h-8 rounded-full border-2 transition-all duration-200 \".concat(newMemory.cor === color ? \"border-white scale-110\" : \"border-white/30\"),\n                                                                                                style: {\n                                                                                                    backgroundColor: color\n                                                                                                }\n                                                                                            }, color, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1558,\n                                                                                                columnNumber: 39\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1556,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1552,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1532,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium\",\n                                                                                children: \"Escopo da Mem\\xf3ria\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1571,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                value: newMemory.global ? \"global\" : newMemory.chatId || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    const value = e.target.value;\n                                                                                    if (value === \"global\") {\n                                                                                        setNewMemory((prev)=>({\n                                                                                                ...prev,\n                                                                                                global: true,\n                                                                                                chatId: null\n                                                                                            }));\n                                                                                    } else {\n                                                                                        setNewMemory((prev)=>({\n                                                                                                ...prev,\n                                                                                                global: false,\n                                                                                                chatId: value\n                                                                                            }));\n                                                                                    }\n                                                                                },\n                                                                                className: \"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"global\",\n                                                                                        className: \"bg-gray-800 text-white\",\n                                                                                        children: \"\\uD83C\\uDF10 Global (todos os chats)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1588,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                                                                        label: \"Chats Espec\\xedficos\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: chat.id,\n                                                                                                className: \"bg-gray-800 text-white\",\n                                                                                                children: [\n                                                                                                    \"\\uD83D\\uDCAC \",\n                                                                                                    chat.name\n                                                                                                ]\n                                                                                            }, chat.id, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1593,\n                                                                                                columnNumber: 39\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1591,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1574,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/50 text-xs\",\n                                                                                children: newMemory.global ? \"Esta mem\\xf3ria ficar\\xe1 dispon\\xedvel em todos os chats\" : \"Esta mem\\xf3ria ficar\\xe1 dispon\\xedvel apenas no chat selecionado\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1599,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1570,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1503,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3 mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowAddMemory(false),\n                                                                        className: \"px-4 py-2 text-white/70 hover:text-white transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1608,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleAddMemory,\n                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: \"Criar Mem\\xf3ria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1614,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1607,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1496,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    memoryCategories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Categorias\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1628,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                                children: memoryCategories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-between mb-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"w-4 h-4 rounded-full\",\n                                                                                                style: {\n                                                                                                    backgroundColor: category.cor\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1634,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                                className: \"text-white font-medium\",\n                                                                                                children: category.nome\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1638,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1633,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteCategory(index),\n                                                                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1645,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                            lineNumber: 1644,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1640,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1632,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            category.descricao && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/60 text-sm\",\n                                                                                children: category.descricao\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1651,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1631,\n                                                                        columnNumber: 33\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1629,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1627,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: [\n                                                                    \"Mem\\xf3rias (\",\n                                                                    memories.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1661,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            memories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white/5 border border-white/10 rounded-xl p-8 text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-12 h-12 text-white/40 mx-auto mb-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1667,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1666,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/60\",\n                                                                        children: \"Nenhuma mem\\xf3ria criada ainda\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1670,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/40 text-sm mt-1\",\n                                                                        children: 'Clique em \"Nova Mem\\xf3ria\" para come\\xe7ar'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1671,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1665,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: memories.map((memory, index)=>{\n                                                                    var _chats_find;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-start justify-between mb-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-3\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"w-4 h-4 rounded-full flex-shrink-0\",\n                                                                                                style: {\n                                                                                                    backgroundColor: memory.cor\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1681,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                                        className: \"text-white font-semibold\",\n                                                                                                        children: memory.titulo\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                        lineNumber: 1686,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"flex items-center space-x-2 mt-1\",\n                                                                                                        children: [\n                                                                                                            memory.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                className: \"bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full\",\n                                                                                                                children: memory.categoria\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                                lineNumber: 1689,\n                                                                                                                columnNumber: 45\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                className: \"text-xs px-2 py-1 rounded-full \".concat(memory.global ? \"bg-green-500/20 text-green-300\" : \"bg-orange-500/20 text-orange-300\"),\n                                                                                                                children: memory.global ? \"\\uD83C\\uDF10 Global\" : \"\\uD83D\\uDCAC \".concat(((_chats_find = chats.find((chat)=>chat.id === memory.chatId)) === null || _chats_find === void 0 ? void 0 : _chats_find.name) || \"Chat Espec\\xedfico\")\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                                lineNumber: 1693,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                        lineNumber: 1687,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1685,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1680,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteMemory(index),\n                                                                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1711,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                            lineNumber: 1710,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1706,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1679,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/80 text-sm leading-relaxed\",\n                                                                                children: memory.conteudo\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1716,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1678,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1676,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1660,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 1391,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"memoria\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 1384,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 836,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 835,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                        lineNumber: 766,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-t border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogout,\n                                    className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                    children: \"Sair da Conta\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 1734,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 1733,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"px-6 py-2 text-white/70 hover:text-white transition-colors font-medium\",\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 1746,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: saveConfigurations,\n                                        disabled: loading,\n                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                        children: loading ? \"Salvando...\" : \"Salvar Configura\\xe7\\xf5es\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 1752,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 1745,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                        lineNumber: 1732,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                lineNumber: 736,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n            lineNumber: 730,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n        lineNumber: 728,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsModal, \"9r2KA0acj+bIwIFNy70pF2R783A=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__.useAppearance\n    ];\n});\n_c = SettingsModal;\nvar _c;\n$RefreshReg$(_c, \"SettingsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/SettingsModal.tsx\n"));

/***/ })

});