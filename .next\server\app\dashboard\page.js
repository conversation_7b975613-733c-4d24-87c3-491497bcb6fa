(()=>{var e={};e.id=702,e.ids=[702],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},41808:e=>{"use strict";e.exports=require("net")},49411:e=>{"use strict";e.exports=require("node:path")},97742:e=>{"use strict";e.exports=require("node:process")},41041:e=>{"use strict";e.exports=require("node:url")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},81852:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=a(67096),s=a(16132),o=a(37284),n=a.n(o),l=a(32564),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);a.d(t,i);let d=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,42918)),"C:\\Users\\<USER>\\Desktop\\SiteRafthor\\rafthor\\src\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,42540)),"C:\\Users\\<USER>\\Desktop\\SiteRafthor\\rafthor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\SiteRafthor\\rafthor\\src\\app\\dashboard\\page.tsx"],m="/dashboard/page",u={require:a,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},73799:(e,t,a)=>{Promise.resolve().then(a.bind(a,16868))},16868:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>Dashboard});var r=a(30784),s=a(9885),o=a.n(s),n=a(78157),l=a(57114),i=a(72373),d=a(29904),c=a(31640),m=a(97602),u=a(37686);let SliderInput=({label:e,value:t,onChange:a,min:s,max:o,step:n,description:l,icon:i})=>{let d=(()=>{let e=(s+o)/2;return t<.8*e?"baixo":t>1.2*e?"alto":"medio"})();return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[i,e]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("span",{className:"text-white/80 text-sm font-mono bg-white/10 px-3 py-1 rounded-lg",children:t.toFixed(1)}),r.jsx("div",{className:"text-xs px-3 py-1 rounded-full bg-blue-500/20 text-blue-300 font-medium",children:"baixo"===d?"Baixo":"medio"===d?"Padr\xe3o":"Alto"})]})]}),(0,r.jsxs)("div",{className:"relative py-4",children:[r.jsx("div",{className:"w-full h-1 bg-white/20 rounded-full relative",children:r.jsx("div",{className:"absolute left-1/2 top-0 bottom-0 w-0.5 bg-white/40 transform -translate-x-1/2"})}),r.jsx("input",{type:"range",min:s,max:o,step:n,value:t,onChange:e=>a(parseFloat(e.target.value)),className:"absolute inset-0 w-full opacity-0 cursor-pointer z-10"}),(0,r.jsxs)("div",{className:"absolute -bottom-2 left-0 right-0 flex justify-between text-xs text-white/40",children:[r.jsx("span",{children:s}),r.jsx("span",{className:"font-semibold text-blue-300",children:"1.0"}),r.jsx("span",{children:o})]}),r.jsx(m.E.div,{className:"absolute top-1/2 w-6 h-6 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full transform -translate-y-1/2 -translate-x-1/2 shadow-lg shadow-blue-500/30 border-2 border-white/20 cursor-pointer",style:{left:`${(t-s)/(o-s)*100}%`},whileHover:{scale:1.2},whileTap:{scale:.9},transition:{duration:.2},children:r.jsx("div",{className:"absolute inset-1 bg-white/20 rounded-full"})})]}),r.jsx("p",{className:"text-white/60 text-xs leading-relaxed",children:l})]})};function CreateChatModal({isOpen:e,onClose:t,username:a,onChatCreated:o,editingChat:n}){let[l,h]=(0,s.useState)("geral"),[x,p]=(0,s.useState)(!1),[g,b]=(0,s.useState)(""),[f,v]=(0,s.useState)({name:"",systemPrompt:"",context:"",password:"",latexInstructions:!1,temperature:1,frequencyPenalty:1,repetitionPenalty:1,maxTokens:2048});(0,s.useEffect)(()=>{n&&e?loadChatData():!n&&e&&v({name:"",systemPrompt:"",context:"",password:"",latexInstructions:!1,temperature:1,frequencyPenalty:1,repetitionPenalty:1,maxTokens:2048})},[n,e]);let loadChatData=async()=>{if(n)try{let e=await (0,d.getDoc)((0,d.doc)(i.db,"usuarios",a,"conversas",n.id));if(e.exists()){let t=e.data();v({name:t.name||"",systemPrompt:t.systemPrompt||"",context:t.context||"",password:t.password||"",latexInstructions:t.latexInstructions||!1,temperature:t.temperature||1,frequencyPenalty:t.frequencyPenalty||1,repetitionPenalty:t.repetitionPenalty||1,maxTokens:t.maxTokens||2048})}}catch(e){console.error("Erro ao carregar dados do chat:",e),b("Erro ao carregar dados do chat")}},handleInputChange=(e,t)=>{v(a=>({...a,[e]:t}))},generateChatId=()=>{let e=Date.now(),t=Math.random().toString(36).substring(2,8);return`chat_${e}_${t}`},createChat=async()=>{if(!f.name.trim()){b("Nome do chat \xe9 obrigat\xf3rio");return}p(!0),b("");try{if(n){let e=new Date().toISOString(),r={context:f.context,frequencyPenalty:f.frequencyPenalty,lastUpdatedAt:e,latexInstructions:f.latexInstructions,maxTokens:f.maxTokens,name:f.name,password:f.password,repetitionPenalty:f.repetitionPenalty,systemPrompt:f.systemPrompt,temperature:f.temperature,updatedAt:e};await (0,d.r7)((0,d.doc)(i.db,"usuarios",a,"conversas",n.id),r),console.log("Chat atualizado com sucesso:",n.id),o(n.id),t()}else{let e=generateChatId(),r=new Date().toISOString(),s={context:f.context,createdAt:r,folderId:null,frequencyPenalty:f.frequencyPenalty,isFixed:!1,lastUpdatedAt:r,lastUsedModel:"",latexInstructions:f.latexInstructions,maxTokens:f.maxTokens,name:f.name,password:f.password,repetitionPenalty:f.repetitionPenalty,sessionTime:{lastSessionStart:null,lastUpdated:null,totalTime:0},systemPrompt:f.systemPrompt,temperature:f.temperature,ultimaMensagem:"",ultimaMensagemEm:null,updatedAt:r};await (0,d.pl)((0,d.doc)(i.db,"usuarios",a,"conversas",e),s);let n={id:e,name:f.name,messages:[],createdAt:r,lastUpdated:r},l=new Blob([JSON.stringify(n,null,2)],{type:"application/json"}),m=(0,c.iH)(i.tO,`usuarios/${a}/conversas/${e}/chat.json`);await (0,c.KV)(m,l),console.log("Chat criado com sucesso:",e),o(e),t(),v({name:"",systemPrompt:"",context:"",password:"",latexInstructions:!1,temperature:1,frequencyPenalty:1,repetitionPenalty:1,maxTokens:2048})}}catch(e){console.error("Erro ao processar chat:",e),b(n?"Erro ao atualizar conversa. Tente novamente.":"Erro ao criar conversa. Tente novamente.")}finally{p(!1)}};return e?r.jsx(u.M,{children:(0,r.jsxs)(m.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:t,children:[r.jsx(m.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-gradient-to-br from-black/40 via-blue-900/30 to-purple-900/40 backdrop-blur-xl"}),r.jsx("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[...Array(20)].map((e,t)=>r.jsx(m.E.div,{className:"absolute w-2 h-2 bg-blue-400/20 rounded-full",initial:{x:Math.random()*window.innerWidth,y:Math.random()*window.innerHeight,scale:0},animate:{y:[null,-100],scale:[0,1,0],opacity:[0,.6,0]},transition:{duration:3*Math.random()+2,repeat:1/0,delay:2*Math.random()}},t))}),(0,r.jsxs)(m.E.div,{initial:{scale:.8,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.8,opacity:0,y:50},transition:{type:"spring",duration:.6,bounce:.3},className:"relative bg-gradient-to-br from-slate-900/90 via-blue-900/90 to-indigo-900/90 backdrop-blur-2xl border border-white/20 rounded-3xl w-full max-w-2xl max-h-[90vh] overflow-hidden shadow-2xl shadow-blue-900/50",onClick:e=>e.stopPropagation(),children:[r.jsx("div",{className:"absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/50 to-transparent"}),r.jsx("div",{className:"absolute top-0 bottom-0 left-0 w-px bg-gradient-to-b from-transparent via-white/30 to-transparent"}),r.jsx("div",{className:"absolute top-0 bottom-0 right-0 w-px bg-gradient-to-b from-transparent via-white/30 to-transparent"}),(0,r.jsxs)(m.E.div,{className:"relative p-6 border-b border-white/10 bg-gradient-to-r from-white/5 to-transparent",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(m.E.div,{className:"relative w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/30",whileHover:{scale:1.1,rotate:5},transition:{duration:.3},children:[r.jsx(m.E.svg,{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",initial:{scale:0},animate:{scale:1},transition:{delay:.4,type:"spring"},children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),r.jsx("div",{className:"absolute inset-0 bg-white/20 rounded-2xl blur-xl"})]}),(0,r.jsxs)("div",{children:[r.jsx(m.E.h2,{className:"text-2xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.3},children:n?"Editar Conversa":"Nova Conversa"}),r.jsx(m.E.p,{className:"text-white/70 text-sm mt-1",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.4},children:n?"Altere as configura\xe7\xf5es da conversa":"Configure sua nova conversa com IA"})]})]}),r.jsx(m.E.button,{onClick:t,className:"absolute top-4 right-4 text-white/60 hover:text-white transition-all duration-300 p-3 hover:bg-white/10 rounded-2xl group",whileHover:{scale:1.1,rotate:90},whileTap:{scale:.9},transition:{duration:.2},children:r.jsx("svg",{className:"w-5 h-5 transition-transform duration-300 group-hover:rotate-90",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)(m.E.div,{className:"flex bg-gradient-to-r from-white/10 to-white/5 mx-6 mt-6 rounded-2xl p-1 backdrop-blur-sm border border-white/10",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,r.jsxs)(m.E.button,{onClick:()=>h("geral"),className:`flex-1 flex items-center justify-center space-x-2 px-6 py-4 rounded-xl transition-all duration-500 relative overflow-hidden ${"geral"===l?"text-white shadow-xl":"text-white/70 hover:text-white hover:bg-white/10"}`,whileHover:{scale:1.02},whileTap:{scale:.98},children:["geral"===l&&r.jsx(m.E.div,{className:"absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl",layoutId:"activeTab",transition:{type:"spring",bounce:.2,duration:.6}}),r.jsx(m.E.svg,{className:"w-5 h-5 relative z-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",animate:{rotate:"geral"===l?360:0},transition:{duration:.5},children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),r.jsx("span",{className:"font-semibold relative z-10",children:"Geral"})]}),(0,r.jsxs)(m.E.button,{onClick:()=>h("avancado"),className:`flex-1 flex items-center justify-center space-x-2 px-6 py-4 rounded-xl transition-all duration-500 relative overflow-hidden ${"avancado"===l?"text-white shadow-xl":"text-white/70 hover:text-white hover:bg-white/10"}`,whileHover:{scale:1.02},whileTap:{scale:.98},children:["avancado"===l&&r.jsx(m.E.div,{className:"absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl",layoutId:"activeTab",transition:{type:"spring",bounce:.2,duration:.6}}),(0,r.jsxs)(m.E.svg,{className:"w-5 h-5 relative z-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",animate:{rotate:"avancado"===l?360:0},transition:{duration:.5},children:[r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),r.jsx("span",{className:"font-semibold relative z-10",children:"Avan\xe7ado"})]})]}),(0,r.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[60vh]",children:[(0,r.jsxs)(u.M,{mode:"wait",children:["geral"===l&&(0,r.jsxs)(m.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},className:"space-y-5",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[r.jsx("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),"Nome do chat"]}),r.jsx("span",{className:"text-red-400 text-xs bg-red-500/20 px-2 py-1 rounded-full",children:"obrigat\xf3rio"})]}),r.jsx("input",{type:"text",value:f.name,onChange:e=>handleInputChange("name",e.target.value),placeholder:"Ex: Projeto de f\xedsica",className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15"}),r.jsx("p",{className:"text-white/60 text-xs",children:"Nome obrigat\xf3rio para identificar a conversa"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[r.jsx("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"System Prompt"]}),r.jsx("span",{className:"text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded-full",children:"opcional"})]}),r.jsx("textarea",{value:f.systemPrompt,onChange:e=>handleInputChange("systemPrompt",e.target.value),placeholder:"Instru\xe7\xf5es para o comportamento da IA...",rows:3,className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15 resize-none"}),r.jsx("p",{className:"text-white/60 text-xs",children:'Define como a IA deve se comportar e responder (ex: "Seja um assistente especializado em matem\xe1tica")'})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[r.jsx("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),"Contexto"]}),r.jsx("span",{className:"text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded-full",children:"opcional"})]}),r.jsx("textarea",{value:f.context,onChange:e=>handleInputChange("context",e.target.value),placeholder:"Informa\xe7\xf5es adicionais de contexto para a conversa...",rows:3,className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15 resize-none"}),r.jsx("p",{className:"text-white/60 text-xs",children:"Informa\xe7\xf5es de fundo que a IA deve considerar durante toda a conversa"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[(0,r.jsxs)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24",children:[r.jsx("rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2"}),r.jsx("path",{d:"M7 11V7a5 5 0 0 1 10 0v4"})]}),"Senha do Chat"]}),r.jsx("span",{className:"text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded-full",children:"opcional"})]}),r.jsx("input",{type:"password",value:f.password,onChange:e=>handleInputChange("password",e.target.value),placeholder:"Deixe vazio para chat sem prote\xe7\xe3o",className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15"}),r.jsx("p",{className:"text-white/60 text-xs",children:"Se definida, ser\xe1 necess\xe1rio inserir a senha para acessar este chat"})]}),r.jsx("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/5 rounded-2xl border border-white/10",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[r.jsx("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})}),"Instru\xe7\xf5es LaTeX"]}),r.jsx("p",{className:"text-white/60 text-xs mt-1",children:"Habilita formata\xe7\xe3o matem\xe1tica avan\xe7ada"})]}),r.jsx("button",{onClick:()=>handleInputChange("latexInstructions",!f.latexInstructions),className:`relative w-14 h-7 rounded-full transition-all duration-300 ${f.latexInstructions?"bg-gradient-to-r from-blue-500 to-cyan-500":"bg-white/20"}`,children:r.jsx("div",{className:`absolute top-1 w-5 h-5 bg-white rounded-full shadow-lg transition-all duration-300 ${f.latexInstructions?"left-8":"left-1"}`})})]})})]},"geral"),"avancado"===l&&(0,r.jsxs)(m.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},className:"space-y-6",children:[r.jsx(SliderInput,{label:"Temperatura",value:f.temperature,onChange:e=>handleInputChange("temperature",e),min:0,max:2,step:.1,description:"Controla a criatividade das respostas. Esquerda = mais preciso (0.1-0.8), Centro = balanceado (1.0), Direita = mais criativo (1.2-2.0).",icon:r.jsx("svg",{className:"w-4 h-4 text-blue-400",fill:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{fillRule:"evenodd",d:"M12.963 2.286a.75.75 0 0 0-1.071-.136 9.742 9.742 0 0 0-3.539 6.176 7.547 7.547 0 0 1-1.705-1.715.75.75 0 0 0-1.152-.082A9 9 0 1 0 15.68 4.534a7.46 7.46 0 0 1-2.717-2.248ZM15.75 14.25a3.75 3.75 0 1 1-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 0 1 1.925-3.546 3.75 3.75 0 0 1 3.255 3.718Z",clipRule:"evenodd"})})}),r.jsx(SliderInput,{label:"Frequency Penalty",value:f.frequencyPenalty,onChange:e=>handleInputChange("frequencyPenalty",e),min:0,max:2,step:.1,description:"Reduz repeti\xe7\xe3o de palavras. Esquerda = sem penalidade (0.0-0.8), Centro = padr\xe3o (1.0), Direita = alta penalidade (1.2-2.0).",icon:r.jsx("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),r.jsx(SliderInput,{label:"Repetition Penalty",value:f.repetitionPenalty,onChange:e=>handleInputChange("repetitionPenalty",e),min:0,max:2,step:.1,description:"Penaliza tokens repetidos. Esquerda = sem penalidade (0.0-0.8), Centro = padr\xe3o (1.0), Direita = alta penalidade (1.2-2.0).",icon:r.jsx("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[r.jsx("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z"})}),"Limite de Tokens"]}),r.jsx("span",{className:"text-white/80 text-sm font-mono bg-white/10 px-3 py-1 rounded-lg",children:f.maxTokens})]}),r.jsx("input",{type:"number",value:f.maxTokens,onChange:e=>handleInputChange("maxTokens",parseInt(e.target.value)||2048),min:512,max:8192,step:256,className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15"}),r.jsx("p",{className:"text-white/60 text-xs leading-relaxed",children:"M\xe1ximo de tokens que a IA pode gerar por resposta. Valores t\xedpicos: 512-4096."})]})]},"avancado")]}),g&&r.jsx(m.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-xl text-red-300 text-sm",children:g})]}),r.jsx(m.E.div,{className:"p-6 border-t border-white/10 bg-gradient-to-r from-white/5 to-transparent backdrop-blur-sm",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[r.jsx(m.E.button,{onClick:t,disabled:x,className:"px-8 py-4 text-white/70 hover:text-white transition-all duration-500 hover:bg-white/10 rounded-2xl disabled:opacity-50 font-semibold border border-white/20 hover:border-white/30 backdrop-blur-sm",whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},transition:{duration:.2},children:"Cancelar"}),(0,r.jsxs)(m.E.button,{onClick:createChat,disabled:x||!f.name.trim(),className:"px-10 py-4 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white rounded-2xl transition-all duration-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-3 font-semibold shadow-xl shadow-blue-500/30 hover:shadow-blue-500/50 border border-blue-400/30 hover:border-blue-400/50 backdrop-blur-sm relative overflow-hidden",whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},transition:{duration:.2},children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"}),x?(0,r.jsxs)(r.Fragment,{children:[r.jsx(m.E.div,{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),r.jsx("span",{children:n?"Salvando...":"Criando..."})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(m.E.svg,{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",whileHover:{rotate:n?0:90},transition:{duration:.3},children:n?r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"}):r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),r.jsx("span",{children:n?"Salvar Altera\xe7\xf5es":"Criar Conversa"})]})]})]})})]})]})}):null}let h=[{name:"Azul",value:"blue",bg:"bg-blue-500",selected:"bg-blue-600"},{name:"Verde",value:"green",bg:"bg-green-500",selected:"bg-green-600"},{name:"Amarelo",value:"yellow",bg:"bg-yellow-500",selected:"bg-yellow-600"},{name:"Vermelho",value:"red",bg:"bg-red-500",selected:"bg-red-600"},{name:"Roxo",value:"purple",bg:"bg-purple-500",selected:"bg-purple-600"},{name:"Ciano",value:"cyan",bg:"bg-cyan-500",selected:"bg-cyan-600"},{name:"Lima",value:"lime",bg:"bg-lime-500",selected:"bg-lime-600"},{name:"Laranja",value:"orange",bg:"bg-orange-500",selected:"bg-orange-600"},{name:"Rosa",value:"pink",bg:"bg-pink-500",selected:"bg-pink-600"},{name:"Cinza",value:"gray",bg:"bg-gray-500",selected:"bg-gray-600"}];function CreateFolderModal({isOpen:e,onClose:t,username:a,onFolderCreated:o,editingFolder:n=null}){let[l,c]=(0,s.useState)(!1),[x,p]=(0,s.useState)(""),[g,b]=(0,s.useState)({name:"",description:"",color:"blue",expandedByDefault:!0});(0,s.useEffect)(()=>{n?b({name:n.name,description:n.description||"",color:n.color,expandedByDefault:n.expandedByDefault}):b({name:"",description:"",color:"blue",expandedByDefault:!0})},[n]);let handleInputChange=(e,t)=>{b(a=>({...a,[e]:t}))},generateFolderId=()=>{let e=Date.now(),t=Math.random().toString(36).substring(2,8);return`folder_${e}_${t}`},createFolder=async()=>{if(!g.name.trim()){p("Nome da pasta \xe9 obrigat\xf3rio");return}c(!0),p("");try{let e=new Date().toISOString();if(n){let t={name:g.name,description:g.description,color:g.color,expandedByDefault:g.expandedByDefault,updatedAt:e};await (0,d.r7)((0,d.doc)(i.db,"usuarios",a,"pastas",n.id),t),console.log("Pasta atualizada com sucesso:",n.id),o(n.id)}else{let t=generateFolderId(),r={name:g.name,description:g.description,color:g.color,expandedByDefault:g.expandedByDefault,createdAt:e,updatedAt:e,chatCount:0};await (0,d.pl)((0,d.doc)(i.db,"usuarios",a,"pastas",t),r),console.log("Pasta criada com sucesso:",t),o(t)}t(),n||b({name:"",description:"",color:"blue",expandedByDefault:!0})}catch(e){console.error("Erro ao salvar pasta:",e),p(n?"Erro ao atualizar pasta. Tente novamente.":"Erro ao criar pasta. Tente novamente.")}finally{c(!1)}};return e?r.jsx(u.M,{children:(0,r.jsxs)(m.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:t,children:[r.jsx(m.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-gradient-to-br from-black/40 via-blue-900/30 to-purple-900/40 backdrop-blur-xl"}),(0,r.jsxs)(m.E.div,{initial:{scale:.8,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.8,opacity:0,y:50},transition:{type:"spring",duration:.6,bounce:.3},className:"relative bg-gradient-to-br from-slate-900/90 via-blue-900/90 to-indigo-900/90 backdrop-blur-2xl border border-white/20 rounded-3xl w-full max-w-md overflow-hidden shadow-2xl shadow-blue-900/50",onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:"relative p-6 border-b border-white/10 bg-gradient-to-r from-white/5 to-transparent",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/30",children:r.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"})})}),r.jsx("div",{children:r.jsx("h2",{className:"text-xl font-bold text-white",children:n?"Editar Pasta":"Nova Pasta"})})]}),r.jsx("button",{onClick:t,className:"absolute top-4 right-4 text-white/60 hover:text-white transition-all duration-300 p-2 hover:bg-white/10 rounded-xl",children:r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-5",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[r.jsx("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),"Nome da pasta"]}),r.jsx("span",{className:"text-red-400 text-xs bg-red-500/20 px-2 py-1 rounded-full",children:"obrigat\xf3rio"})]}),r.jsx("input",{type:"text",value:g.name,onChange:e=>handleInputChange("name",e.target.value),placeholder:"Digite o nome da pasta",className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15"}),r.jsx("p",{className:"text-white/60 text-xs",children:"Nome para identificar a pasta de chats"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[r.jsx("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Descri\xe7\xe3o"]}),r.jsx("textarea",{value:g.description,onChange:e=>handleInputChange("description",e.target.value),placeholder:"Descri\xe7\xe3o opcional da pasta",rows:2,className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15 resize-none"}),r.jsx("p",{className:"text-white/60 text-xs",children:"Descri\xe7\xe3o opcional para a pasta"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx("label",{className:"text-sm font-medium text-blue-300",children:"Cor da pasta"}),r.jsx("div",{className:"grid grid-cols-5 gap-2",children:h.map(e=>r.jsx("button",{onClick:()=>handleInputChange("color",e.value),className:`w-10 h-10 rounded-xl transition-all duration-200 ${g.color===e.value?`${e.selected} ring-2 ring-white/50 scale-110`:`${e.bg} hover:scale-105`}`,title:e.name,children:g.color===e.value&&r.jsx("svg",{className:"w-5 h-5 text-white mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})},e.value))}),r.jsx("p",{className:"text-white/60 text-xs",children:"Escolha uma cor para identificar visualmente a pasta"})]}),r.jsx("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/5 rounded-2xl border border-white/10",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm font-medium text-blue-300",children:"Expandida por padr\xe3o"}),r.jsx("p",{className:"text-white/60 text-xs mt-1",children:"A pasta ficar\xe1 aberta quando voc\xea acessar o chat"})]}),r.jsx("button",{onClick:()=>handleInputChange("expandedByDefault",!g.expandedByDefault),className:`relative w-14 h-7 rounded-full transition-all duration-300 ${g.expandedByDefault?"bg-gradient-to-r from-blue-500 to-cyan-500":"bg-white/20"}`,children:r.jsx("div",{className:`absolute top-1 w-5 h-5 bg-white rounded-full shadow-lg transition-all duration-300 ${g.expandedByDefault?"left-8":"left-1"}`})})]})}),x&&r.jsx(m.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"p-3 bg-red-500/20 border border-red-500/30 rounded-xl text-red-300 text-sm",children:x})]}),r.jsx("div",{className:"p-6 border-t border-white/10 bg-white/5",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[r.jsx("button",{onClick:t,disabled:l,className:"px-6 py-3 text-white/70 hover:text-white transition-all duration-300 hover:bg-white/10 rounded-xl disabled:opacity-50 font-medium",children:"Cancelar"}),r.jsx("button",{onClick:createFolder,disabled:l||!g.name.trim(),className:"px-8 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 font-medium shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40",children:l?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),r.jsx("span",{children:n?"Salvando...":"Criando..."})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:n?"M5 13l4 4L19 7":"M12 4v16m8-8H4"})}),r.jsx("span",{children:n?"Salvar Altera\xe7\xf5es":"Criar Pasta"})]})})]})})]})]})}):null}function ConfirmDeleteModal({isOpen:e,onClose:t,onConfirm:a,title:s,message:o,itemName:n,isLoading:l=!1}){return e?r.jsx(u.M,{children:(0,r.jsxs)(m.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:t,children:[r.jsx(m.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-gradient-to-br from-black/40 via-red-900/30 to-black/40 backdrop-blur-xl"}),(0,r.jsxs)(m.E.div,{initial:{scale:.8,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.8,opacity:0,y:50},transition:{type:"spring",duration:.6,bounce:.3},className:"relative bg-gradient-to-br from-slate-900/90 via-red-900/90 to-slate-900/90 backdrop-blur-2xl border border-red-500/30 rounded-3xl w-full max-w-md overflow-hidden shadow-2xl shadow-red-900/50",onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:"relative p-6 border-b border-red-500/20 bg-gradient-to-r from-red-500/10 to-transparent",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg shadow-red-500/30",children:r.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),r.jsx("div",{children:r.jsx("h2",{className:"text-xl font-bold text-white",children:s})})]}),r.jsx("button",{onClick:t,className:"absolute top-4 right-4 text-white/60 hover:text-white transition-all duration-300 p-2 hover:bg-white/10 rounded-xl",children:r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),r.jsx("div",{className:"p-6 space-y-4",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("p",{className:"text-white/80 text-base leading-relaxed",children:o}),r.jsx("div",{className:"mt-3 p-3 bg-red-500/20 border border-red-500/30 rounded-xl",children:(0,r.jsxs)("p",{className:"text-red-300 font-semibold text-sm",children:['"',n,'"']})}),r.jsx("p",{className:"text-red-400/80 text-sm mt-3 font-medium",children:"Esta a\xe7\xe3o n\xe3o pode ser desfeita."})]})}),r.jsx("div",{className:"p-6 border-t border-red-500/20 bg-red-500/5",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[r.jsx("button",{onClick:t,disabled:l,className:"px-6 py-3 text-white/70 hover:text-white transition-all duration-300 hover:bg-white/10 rounded-xl disabled:opacity-50 font-medium",children:"Cancelar"}),r.jsx("button",{onClick:a,disabled:l,className:"px-8 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 font-medium shadow-lg shadow-red-500/25 hover:shadow-red-500/40",children:l?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),r.jsx("span",{children:"Deletando..."})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),r.jsx("span",{children:"Deletar"})]})})]})})]})]})}):null}function PasswordProtectedModal({isOpen:e,onClose:t,onSuccess:a,chatName:o,onPasswordSubmit:n}){let[l,i]=(0,s.useState)(""),[d,c]=(0,s.useState)(!1),[h,x]=(0,s.useState)(""),[p,g]=(0,s.useState)(!1),handleSubmit=async e=>{if(e.preventDefault(),!l.trim()){x("Digite a senha");return}c(!0),x("");try{let e=await n(l);e?(i(""),a()):(x("Senha incorreta. Tente novamente."),i(""))}catch(e){console.error("Erro ao verificar senha:",e),x("Erro ao verificar senha. Tente novamente.")}finally{c(!1)}},handleClose=()=>{i(""),x(""),g(!1),t()};return e?r.jsx(u.M,{children:(0,r.jsxs)(m.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:handleClose,children:[r.jsx(m.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-gradient-to-br from-black/60 via-blue-900/40 to-purple-900/60 backdrop-blur-xl"}),(0,r.jsxs)(m.E.div,{initial:{scale:.8,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.8,opacity:0,y:50},transition:{type:"spring",duration:.6,bounce:.3},className:"relative bg-gradient-to-br from-slate-900/95 via-blue-900/95 to-indigo-900/95 backdrop-blur-2xl border border-blue-500/30 rounded-3xl w-full max-w-md overflow-hidden shadow-2xl shadow-blue-900/50",onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:"relative p-6 border-b border-blue-500/20 bg-gradient-to-r from-blue-500/10 to-transparent",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/30 relative",children:[r.jsx("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})}),r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-2xl blur-lg opacity-30 -z-10"})]}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-bold text-white",children:"Chat Protegido"}),r.jsx("p",{className:"text-blue-200/80 text-sm",children:"Digite a senha para continuar"})]})]}),r.jsx("button",{onClick:handleClose,className:"absolute top-4 right-4 text-white/60 hover:text-white transition-all duration-300 p-2 hover:bg-white/10 rounded-xl",children:r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-2xl flex items-center justify-center mx-auto mb-3 border border-blue-500/30",children:r.jsx("svg",{className:"w-8 h-8 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),r.jsx("h3",{className:"text-lg font-semibold text-white mb-1",children:o}),r.jsx("p",{className:"text-blue-300/70 text-sm",children:"Esta conversa est\xe1 protegida por senha"})]}),(0,r.jsxs)("form",{onSubmit:handleSubmit,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[(0,r.jsxs)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),"Senha"]}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{type:p?"text":"password",value:l,onChange:e=>i(e.target.value),placeholder:"Digite a senha da conversa",className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 pr-12 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15",disabled:d}),r.jsx("button",{type:"button",onClick:()=>g(!p),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors",disabled:d,children:p?r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})}):(0,r.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]})]}),h&&(0,r.jsxs)(m.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"p-3 bg-red-500/20 border border-red-500/30 rounded-xl text-red-300 text-sm flex items-center space-x-2",children:[r.jsx("svg",{className:"w-4 h-4 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),r.jsx("span",{children:h})]}),r.jsx("button",{type:"submit",disabled:d||!l.trim(),className:"w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white py-3 rounded-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 font-medium shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-105 transform",children:d?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}),r.jsx("span",{children:"Verificando..."})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"})}),r.jsx("span",{children:"Desbloquear"})]})})]}),r.jsx("div",{className:"text-center",children:r.jsx("p",{className:"text-white/50 text-xs",children:"A senha \xe9 necess\xe1ria para acessar esta conversa protegida"})})]})]})]})}):null}let x=(0,s.forwardRef)(({userData:e,isOpen:t,isCollapsed:o=!1,onToggle:n,onSettingsOpen:l,onChatSelect:m,currentChat:u,onUpdateOpenRouterBalance:h,showCloseButton:x=!0},p)=>{let[g,b]=(0,s.useState)([]),[f,v]=(0,s.useState)([]),[w,j]=(0,s.useState)({balance:0,isLoading:!0,error:void 0}),[y,N]=(0,s.useState)(!1),[k,C]=(0,s.useState)(!1),[M,S]=(0,s.useState)(null),[D,E]=(0,s.useState)(!1),[L,A]=(0,s.useState)(!1),[F,I]=(0,s.useState)(null),[T,P]=(0,s.useState)(null),[z,B]=(0,s.useState)(null),[$,R]=(0,s.useState)(null),[W,O]=(0,s.useState)({isOpen:!1,type:"chat",id:"",name:""}),[U,_]=(0,s.useState)(!1),[H,q]=(0,s.useState)({isOpen:!1,chatId:"",chatName:"",action:"access"}),V=(0,s.useRef)(!1),fetchOpenRouterBalance=async()=>{if(V.current){console.log("Requisi\xe7\xe3o de saldo j\xe1 em andamento, ignorando...");return}try{V.current=!0,console.log("Iniciando busca do saldo do OpenRouter..."),j(e=>({...e,isLoading:!0,error:void 0}));let t="";try{let a=(0,d.collection)(i.db,"usuarios",e.username,"endpoints"),r=await (0,d.getDocs)(a);r.forEach(e=>{let a=e.data();a.isActive&&a.url&&a.url.includes("openrouter.ai")&&(t=a.apiKey)})}catch(e){console.log("Nova estrutura n\xe3o encontrada, tentando estrutura antiga...")}if(!t)try{let{doc:r,getDoc:s}=await Promise.resolve().then(a.bind(a,29904)),o=r(i.db,"usuarios",e.username,"configuracoes","settings"),n=await s(o);if(n.exists()){let e=n.data();e.endpoints&&Object.values(e.endpoints).forEach(e=>{e.ativo&&e.url&&e.url.includes("openrouter.ai")&&(t=e.apiKey)})}}catch(e){console.log("Estrutura antiga tamb\xe9m n\xe3o encontrada")}if(!t){j(e=>({...e,isLoading:!1,error:"Nenhuma API key do OpenRouter configurada"}));return}console.log("API Key encontrada, buscando saldo...");let r=await fetch("/api/openrouter/credits",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({apiKey:t})}),s=await r.json();console.log("Resposta da API:",s),s.success?(j({balance:s.balance,isLoading:!1,error:void 0}),console.log("Saldo carregado com sucesso:",s.balance)):(j(e=>({...e,isLoading:!1,error:s.error||"Erro ao buscar saldo"})),console.log("Erro ao buscar saldo:",s.error))}catch(e){console.error("Erro ao buscar saldo do OpenRouter:",e),j(e=>({...e,isLoading:!1,error:"Erro ao conectar com OpenRouter"}))}finally{V.current=!1}},loadChats=async()=>{try{let t=(0,d.collection)(i.db,"usuarios",e.username,"conversas"),a=(0,d.query)(t,(0,d.Xo)("lastUpdatedAt","desc")),r=await (0,d.getDocs)(a),s=[];r.forEach(e=>{let t=e.data();s.push({id:e.id,name:t.name||"Conversa sem nome",lastMessage:t.ultimaMensagem||"Nenhuma mensagem ainda",lastMessageTime:t.ultimaMensagemEm||t.createdAt,folder:t.folderId,password:t.password})});let o=(0,d.collection)(i.db,"usuarios",e.username,"pastas"),n=(0,d.query)(o,(0,d.Xo)("createdAt","asc")),l=await (0,d.getDocs)(n),c=[];l.forEach(e=>{let t=e.data(),a=s.filter(t=>t.folder===e.id);c.push({id:e.id,name:t.name,description:t.description,color:t.color||"#3B82F6",isExpanded:!1!==t.expandedByDefault,chats:a})});let m=s.filter(e=>!e.folder);b(c),v(m)}catch(e){console.error("Erro ao carregar chats e pastas:",e)}},K=(0,s.useRef)(!1);(0,s.useEffect)(()=>{e.username&&(loadChats(),K.current||(console.log("Carregando saldo do OpenRouter pela primeira vez..."),fetchOpenRouterBalance(),K.current=!0))},[e.username]),(0,s.useImperativeHandle)(p,()=>({reloadChats:loadChats,updateOpenRouterBalance:fetchOpenRouterBalance}));let handleEditFolder=async t=>{try{let a=await (0,d.getDocs)((0,d.query)((0,d.collection)(i.db,"usuarios",e.username,"pastas"))),r=a.docs.find(e=>e.id===t)?.data();r&&(I({id:t,name:r.name,description:r.description,color:r.color||"#3B82F6",expandedByDefault:!1!==r.expandedByDefault}),A(!0))}catch(e){console.error("Erro ao carregar dados da pasta:",e),alert("Erro ao carregar dados da pasta.")}},handleDeleteFolder=(e,t)=>{O({isOpen:!0,type:"folder",id:e,name:t})},handleDeleteChat=(e,t)=>{O({isOpen:!0,type:"chat",id:e,name:t})},deleteChatAttachments=async t=>{try{let a=(0,c.iH)(i.tO,`usuarios/${e.username}/conversas/${t}/anexos`),r=await (0,c.aF)(a),s=r.items.map(e=>(0,c.oq)(e));await Promise.all(s),console.log(`${r.items.length} anexos deletados do chat ${t}`)}catch(e){console.log("Erro ao deletar anexos ou pasta de anexos n\xe3o encontrada:",e)}},confirmDelete=async()=>{_(!0);try{if("folder"===W.type){await (0,d.oe)((0,d.doc)(i.db,"usuarios",e.username,"pastas",W.id));let t=g.find(e=>e.id===W.id)?.chats||[];for(let a of t)await (0,d.r7)((0,d.doc)(i.db,"usuarios",e.username,"conversas",a.id),{folderId:null,updatedAt:new Date().toISOString()});console.log("Pasta deletada:",W.id)}else{await (0,d.oe)((0,d.doc)(i.db,"usuarios",e.username,"conversas",W.id));try{let t=(0,c.iH)(i.tO,`usuarios/${e.username}/conversas/${W.id}/chat.json`);await (0,c.oq)(t)}catch(e){console.log("Arquivo chat.json no storage n\xe3o encontrado:",e)}await deleteChatAttachments(W.id),u===W.id&&m(""),console.log("Chat deletado:",W.id)}loadChats(),O({isOpen:!1,type:"chat",id:"",name:""})}catch(e){console.error("Erro ao deletar:",e),alert("Erro ao deletar. Tente novamente.")}finally{_(!1)}},handleDragStart=e=>{P(e)},handleDragEnd=()=>{P(null),B(null)},handleDragOver=(e,t)=>{e.preventDefault(),B(t)},handleDragLeave=()=>{B(null)},handleDrop=async(t,a)=>{if(t.preventDefault(),T)try{await (0,d.r7)((0,d.doc)(i.db,"usuarios",e.username,"conversas",T),{folderId:a,updatedAt:new Date().toISOString()}),console.log(`Chat ${T} movido para pasta ${a||"sem pasta"}`),loadChats()}catch(e){console.error("Erro ao mover chat:",e)}finally{P(null),B(null)}},handleEditChat=e=>{S(e),C(!0)},toggleFolder=e=>{b(t=>t.map(t=>t.id===e?{...t,isExpanded:!t.isExpanded}:t))},formatTime=e=>{let t=new Date(e),a=new Date,r=(a.getTime()-t.getTime())/36e5;return r<24?t.toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"}):r<168?t.toLocaleDateString("pt-BR",{weekday:"short"}):t.toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit"})},getFolderHexColor=e=>({blue:"#3B82F6",green:"#10B981",yellow:"#F59E0B",red:"#EF4444",purple:"#8B5CF6",cyan:"#06B6D4",lime:"#84CC16",orange:"#F97316",pink:"#EC4899",gray:"#6B7280"})[e]||e,checkChatPassword=async(t,a)=>{try{let r=await (0,d.getDocs)((0,d.query)((0,d.collection)(i.db,"usuarios",e.username,"conversas"))),s=r.docs.find(e=>e.id===t)?.data();if(s&&s.password)return s.password===a;return!0}catch(e){return console.error("Erro ao verificar senha:",e),!1}},handleProtectedAction=(e,t,a)=>{let r=[...f,...g.flatMap(e=>e.chats)].find(t=>t.id===e);r?.password?q({isOpen:!0,chatId:e,chatName:t,action:a}):executeAction(e,t,a)},executeAction=(e,t,a)=>{switch(a){case"access":m(e);break;case"edit":let r=[...f,...g.flatMap(e=>e.chats)].find(t=>t.id===e);r&&handleEditChat(r);break;case"delete":handleDeleteChat(e,t)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:`
        fixed top-0 left-0 h-full w-80 bg-gradient-to-b from-blue-950/95 via-blue-900/95 to-blue-950/95
        backdrop-blur-xl border-r border-blue-700/30 z-50 transform transition-all duration-300 shadow-2xl
        ${t?"translate-x-0":"-translate-x-full"}
        ${o?"lg:-translate-x-full":"lg:translate-x-0"}
      `,children:[x&&r.jsx("button",{onClick:n,className:"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-blue-600 hover:bg-blue-500 rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-xl border-2 border-white/20 z-10 group",title:"Fechar sidebar",children:r.jsx("svg",{className:"w-4 h-4 text-white transition-transform duration-300 group-hover:rotate-180",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[r.jsx("div",{className:"p-6 border-b border-white/10",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center",children:r.jsx("span",{className:"text-white font-semibold text-lg",children:e.username.charAt(0).toUpperCase()})}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h3",{className:"text-white font-semibold text-lg",children:e.username}),w.isLoading?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin"}),r.jsx("p",{className:"text-blue-200 text-sm",children:"Carregando..."})]}):w.error?r.jsx("p",{className:"text-red-400 text-sm",title:w.error,children:"$0.00"}):(0,r.jsxs)("p",{className:"text-blue-200 text-sm",children:["$",w.balance.toFixed(4)]})]}),r.jsx("button",{onClick:l,className:"text-blue-200 hover:text-white transition-colors p-1",children:(0,r.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})})]})}),(0,r.jsxs)("div",{className:"p-4 space-y-2",children:[(0,r.jsxs)("button",{onClick:()=>{m(null)},className:"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg  transition-all duration-200 flex items-center space-x-3 font-medium",children:[r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})}),r.jsx("span",{children:"\xc1rea Inicial"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>{N(!0)},className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 font-medium",children:[r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),r.jsx("span",{children:"Nova Conversa"})]}),r.jsx("button",{onClick:()=>{E(!0)},className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center font-medium",title:"Nova Pasta",children:r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"})})})]})]}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent",children:[r.jsx("div",{className:"px-4 py-2 border-b border-white/10",children:r.jsx("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("h4",{className:"text-blue-300 text-sm font-bold uppercase tracking-wider flex items-center space-x-2",children:[r.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),r.jsx("span",{children:"Conversas"})]})})}),r.jsx("div",{className:"px-2 py-2",children:g.map(e=>(0,r.jsxs)("div",{className:"mb-2",onDragOver:t=>handleDragOver(t,e.id),onDragLeave:handleDragLeave,onDrop:t=>handleDrop(t,e.id),children:[r.jsx("div",{className:`group relative rounded-lg transition-all duration-300 cursor-pointer ${$===e.id?"bg-blue-800/40":"hover:bg-blue-800/30"} ${z===e.id?"bg-blue-500/30 ring-2 ring-blue-400/50":""}`,onMouseEnter:()=>R(e.id),onMouseLeave:()=>R(null),onClick:()=>toggleFolder(e.id),children:(0,r.jsxs)("div",{className:"flex items-center justify-between p-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 flex-1 min-w-0",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx("svg",{className:`w-4 h-4 text-blue-300 transition-transform duration-200 ${e.isExpanded?"rotate-90":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})}),r.jsx("div",{className:"w-6 h-6 rounded flex items-center justify-center flex-shrink-0",style:{backgroundColor:getFolderHexColor(e.color)+"40"},children:r.jsx("svg",{className:"w-4 h-4",style:{color:getFolderHexColor(e.color)},fill:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{d:"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("h4",{className:"text-base font-semibold text-blue-100 truncate",children:e.name}),r.jsx("span",{className:"text-xs text-blue-300/70 bg-blue-900/50 px-2 py-0.5 rounded-full",children:e.chats.length})]}),e.description&&r.jsx("p",{className:"text-xs text-blue-300/60 truncate mt-0.5",children:e.description})]})]}),(0,r.jsxs)("div",{className:`flex items-center space-x-1 transition-all duration-300 ${$===e.id?"opacity-100 translate-x-0":"opacity-0 translate-x-2"}`,children:[r.jsx("button",{className:"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200",title:"Editar pasta",onClick:t=>{t.stopPropagation(),handleEditFolder(e.id)},children:r.jsx("svg",{className:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),r.jsx("button",{className:"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200",title:"Deletar pasta",onClick:t=>{t.stopPropagation(),handleDeleteFolder(e.id,e.name)},children:r.jsx("svg",{className:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})}),e.isExpanded&&r.jsx("div",{className:"ml-6 mt-2 space-y-1",children:e.chats.map(e=>(0,r.jsxs)("div",{draggable:!0,onDragStart:()=>handleDragStart(e.id),onDragEnd:handleDragEnd,className:`group relative rounded-xl transition-all duration-300 cursor-move ${u===e.id?"bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-500/30 shadow-lg":"hover:bg-blue-800/30 border border-transparent"} ${T===e.id?"opacity-50 scale-95":""}`,children:[(0,r.jsxs)("button",{className:"w-full text-left p-3 flex items-start space-x-3",onClick:()=>handleProtectedAction(e.id,e.name,"access"),children:[(0,r.jsxs)("div",{className:`w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative ${u===e.id?"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30":"bg-blue-700/50 group-hover:bg-blue-600/70"}`,children:[e.password&&r.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900",children:r.jsx("svg",{className:"w-2 h-2 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),r.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",strokeWidth:2,children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})]}),(0,r.jsxs)("div",{className:"overflow-hidden flex-1 min-w-0 transition-all duration-300 group-hover:pr-20",children:[r.jsx("h4",{className:`truncate text-sm font-semibold mb-1 ${u===e.id?"text-white":"text-blue-100 group-hover:text-white"}`,children:e.name}),r.jsx("p",{className:`truncate text-xs leading-relaxed transition-all duration-300 ${u===e.id?"text-blue-200/80":"text-blue-300/70 group-hover:text-blue-200"}`,children:e.lastMessage||"Nenhuma mensagem ainda..."}),e.lastMessageTime&&r.jsx("span",{className:`text-xs mt-1 block ${u===e.id?"text-blue-300/60":"text-blue-400/50 group-hover:text-blue-300/70"}`,children:formatTime(e.lastMessageTime)})]})]}),(0,r.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300",children:[r.jsx("button",{className:"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm",title:"Editar",onClick:t=>{t.stopPropagation(),handleProtectedAction(e.id,e.name,"edit")},children:r.jsx("svg",{className:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),r.jsx("button",{className:"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm",title:"Deletar",onClick:t=>{t.stopPropagation(),handleProtectedAction(e.id,e.name,"delete")},children:r.jsx("svg",{className:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]},e.id))})]},e.id))}),(0,r.jsxs)("div",{className:"px-2 py-2",onDragOver:e=>handleDragOver(e,null),onDragLeave:handleDragLeave,onDrop:e=>handleDrop(e,null),children:[r.jsx("div",{className:"px-3 py-2",children:(0,r.jsxs)("h5",{className:`text-blue-300 text-xs font-bold uppercase tracking-wider transition-all duration-200 flex items-center space-x-2 ${null===z&&T?"text-blue-400":""}`,children:[r.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),(0,r.jsxs)("span",{children:["Sem Pasta ",null===z&&T?"(Solte aqui para remover da pasta)":""]})]})}),r.jsx("div",{className:`space-y-1 min-h-[60px] transition-all duration-200 ${null===z&&T?"bg-blue-500/10 rounded-lg p-2 border-2 border-dashed border-blue-400/50":""}`,children:0===f.length?(0,r.jsxs)("div",{className:"text-center py-4",children:[r.jsx("div",{className:"w-8 h-8 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-2",children:r.jsx("svg",{className:"w-4 h-4 text-white/30",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),r.jsx("p",{className:"text-white/40 text-xs",children:"Nenhuma conversa sem pasta"})]}):f.map(e=>r.jsx(ChatItem,{chat:e,isActive:u===e.id,onClick:()=>handleProtectedAction(e.id,e.name,"access"),onEdit:(e,t)=>handleProtectedAction(e,t,"edit"),onDelete:(e,t)=>handleProtectedAction(e,t,"delete"),onDragStart:handleDragStart,onDragEnd:handleDragEnd,isDragging:T===e.id},e.id))})]}),0===g.length&&0===f.length&&(0,r.jsxs)("div",{className:"px-4 py-8 text-center",children:[r.jsx("div",{className:"text-white/30 mb-2",children:r.jsx("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),r.jsx("p",{className:"text-white/50 text-sm",children:"Nenhuma conversa ainda"}),r.jsx("p",{className:"text-white/30 text-xs mt-1",children:'Clique em "Nova Conversa" para come\xe7ar'})]})]}),r.jsx("div",{className:"lg:hidden p-4 border-t border-white/10",children:(0,r.jsxs)("button",{onClick:n,className:"w-full bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg  transition-all duration-200 flex items-center justify-center space-x-2",children:[r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),r.jsx("span",{children:"Fechar"})]})})]})]}),r.jsx(CreateChatModal,{isOpen:y,onClose:()=>N(!1),username:e.username,onChatCreated:e=>{console.log("Chat criado:",e),loadChats(),m(e)}}),M&&r.jsx(CreateChatModal,{isOpen:k,onClose:()=>{C(!1),S(null)},username:e.username,onChatCreated:e=>{console.log("Chat atualizado:",e),loadChats(),C(!1),S(null)},editingChat:M}),r.jsx(CreateFolderModal,{isOpen:D,onClose:()=>E(!1),username:e.username,onFolderCreated:e=>{console.log("Pasta criada:",e),loadChats()}}),r.jsx(CreateFolderModal,{isOpen:L,onClose:()=>{A(!1),I(null)},username:e.username,onFolderCreated:e=>{console.log("Pasta atualizada:",e),loadChats(),A(!1),I(null)},editingFolder:F}),r.jsx(ConfirmDeleteModal,{isOpen:W.isOpen,onClose:()=>O({isOpen:!1,type:"chat",id:"",name:""}),onConfirm:confirmDelete,title:"folder"===W.type?"Deletar Pasta":"Deletar Conversa",message:"folder"===W.type?'Tem certeza que deseja deletar esta pasta? Todas as conversas dentro dela ser\xe3o movidas para "Sem Pasta".':"Tem certeza que deseja deletar esta conversa? Todas as mensagens ser\xe3o perdidas permanentemente.",itemName:W.name,isLoading:U}),r.jsx(PasswordProtectedModal,{isOpen:H.isOpen,onClose:()=>q({isOpen:!1,chatId:"",chatName:"",action:"access"}),onSuccess:()=>{executeAction(H.chatId,H.chatName,H.action),q({isOpen:!1,chatId:"",chatName:"",action:"access"})},chatName:H.chatName,onPasswordSubmit:e=>checkChatPassword(H.chatId,e)})]})});function ChatItem({chat:e,isActive:t,onClick:a,onEdit:s,onDelete:o,onDragStart:n,onDragEnd:l,isDragging:i}){return(0,r.jsxs)("div",{draggable:!0,onDragStart:()=>n(e.id),onDragEnd:l,className:`relative w-full rounded-lg transition-all duration-200 group cursor-move ${t?"bg-blue-600/50":"hover:bg-white/5"} ${i?"opacity-50 scale-95":""}`,children:[r.jsx("button",{onClick:a,className:"w-full text-left px-3 py-3 text-white/80 hover:text-white",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsxs)("div",{className:`w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative ${t?"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30":"bg-blue-700/50 group-hover:bg-blue-600/70"}`,children:[e.password&&r.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900",children:r.jsx("svg",{className:"w-2 h-2 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),r.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",strokeWidth:2,children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[r.jsx("h6",{className:"font-medium text-sm truncate",children:e.name}),r.jsx("span",{className:"text-xs text-white/50 flex-shrink-0 ml-2 group-hover:opacity-0 transition-opacity duration-200",children:(e=>{let t=new Date(e),a=new Date,r=(a.getTime()-t.getTime())/36e5;return r<24?t.toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"}):r<168?`h\xe1 ${Math.floor(r/24)} dias`:t.toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit"})})(e.lastMessageTime)})]}),r.jsx("p",{className:"text-xs text-white/60 line-clamp-2 leading-relaxed",children:e.lastMessage})]})]})}),(0,r.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1",children:[r.jsx("button",{onClick:t=>{t.stopPropagation(),s(e.id,e.name)},className:"p-1.5 bg-blue-600/80 hover:bg-blue-600 text-white rounded-md transition-colors duration-200",title:"Configurar chat",children:(0,r.jsxs)("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})}),r.jsx("button",{onClick:t=>{t.stopPropagation(),o(e.id,e.name)},className:"p-1.5 bg-red-600/80 hover:bg-red-600 text-white rounded-md transition-colors duration-200",title:"Deletar chat",children:r.jsx("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})}x.displayName="Sidebar";let useSessionTime=e=>{let[t,a]=(0,s.useState)("0s"),[r]=(0,s.useState)(Date.now()),o=(0,s.useRef)(null),formatTime=e=>{let t=Math.floor(e/1e3),a=Math.floor(t/60),r=Math.floor(a/60),s=Math.floor(r/24),o=Math.floor(s/30),n=t%60,l=a%60,i=r%24,d=s%30;return 0===a?`${t}s`:0===r?`${l}:${n.toString().padStart(2,"0")}`:0===s?`${i}:${l.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`:0===o?`${d}d ${i.toString().padStart(2,"0")}:${l.toString().padStart(2,"0")}`:`${o}m ${d}d ${i.toString().padStart(2,"0")}:${l.toString().padStart(2,"0")}`};return(0,s.useEffect)(()=>(e&&(o.current=setInterval(()=>{let e=Date.now()-r,t=formatTime(e);a(t)},1e3)),()=>{o.current&&(clearInterval(o.current),o.current=null)}),[e,r]),t};function Upperbar({currentChat:e,chatName:t,aiModel:a,onDownload:s,onAttachments:o,onStatistics:n,isLoading:l=!1,attachmentsCount:i=0,aiMetadata:d}){let c=useSessionTime(e),m=t||(e?`Chat ${e}`:"Nova Conversa");return(0,r.jsxs)("div",{className:"h-14 sm:h-16 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border-b border-blue-700/30 shadow-xl relative",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none"}),(0,r.jsxs)("div",{className:"h-full flex items-center justify-between px-3 sm:px-4 lg:px-6 relative z-10",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3 bg-blue-900/30 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 min-w-0",children:[r.jsx("div",{className:"w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-400 rounded-full shadow-lg shadow-blue-400/50 flex-shrink-0"}),r.jsx("span",{className:"text-xs sm:text-sm font-medium text-blue-100 truncate",children:a||"GPT-4.1 Nano"}),d?.usedCoT&&r.jsx("div",{className:"px-1.5 sm:px-2 py-0.5 sm:py-1 bg-cyan-600/20 text-cyan-300 text-xs rounded-full border border-cyan-500/30 hidden sm:block",children:"CoT"})]}),r.jsx("div",{className:"w-px h-4 sm:h-6 bg-blue-600/30 hidden sm:block"}),(0,r.jsxs)("div",{className:"flex items-center space-x-1.5 sm:space-x-2 bg-blue-900/20 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 hidden sm:flex",children:[r.jsx("svg",{className:"w-3 h-3 sm:w-4 sm:h-4 text-blue-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),r.jsx("span",{className:"text-xs sm:text-sm text-blue-200 font-mono",children:c})]})]}),r.jsx("div",{className:"absolute left-1/2 transform -translate-x-1/2 hidden sm:block",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3 bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2 shadow-lg max-w-xs lg:max-w-sm",children:[r.jsx("div",{className:`w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full shadow-lg flex-shrink-0 ${l?"bg-yellow-400 shadow-yellow-400/50 animate-pulse":"bg-cyan-400 shadow-cyan-400/50"}`}),r.jsx("h1",{className:"text-sm sm:text-lg font-semibold text-white truncate",children:m})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>{o?o():console.log("Anexos clicado")},className:"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105 relative",title:"Anexos",children:[r.jsx("svg",{className:"w-4 h-4 sm:w-5 sm:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"})}),i>0&&r.jsx("span",{className:"absolute -top-1 -right-1 bg-cyan-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium shadow-lg",children:i>99?"99+":i})]}),r.jsx("button",{onClick:()=>{n?n():console.log("Estat\xedsticas clicado")},className:"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105",title:"Estat\xedsticas",children:r.jsx("svg",{className:"w-4 h-4 sm:w-5 sm:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),r.jsx("button",{onClick:()=>{s?s():console.log("Download clicado")},className:"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/30 hover:scale-105 border border-blue-500/30",title:"Download",children:r.jsx("svg",{className:"w-4 h-4 sm:w-5 sm:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})})]})]})]})}var p=a(46794),g=a(83140),b=a(61440),f=a(60313),v=a(18823);a(32289),a(18172);let w=o().memo(({content:e,isStreaming:t})=>r.jsx(p.UG,{remarkPlugins:[g.Z,b.Z],rehypePlugins:[f.Z,[v.Z,{detect:!0,ignoreMissing:!0}]],components:{code({node:e,inline:t,className:a,children:s,...o}){let n=/language-(\w+)/.exec(a||""),l=n?n[1]:"text";return!t&&n?(0,r.jsxs)("div",{className:"code-block-container group",children:[(0,r.jsxs)("div",{className:"code-header",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("div",{className:"flex gap-1.5",children:[r.jsx("div",{className:"w-3 h-3 rounded-full bg-red-400/80"}),r.jsx("div",{className:"w-3 h-3 rounded-full bg-yellow-400/80"}),r.jsx("div",{className:"w-3 h-3 rounded-full bg-green-400/80"})]}),r.jsx("span",{className:"text-sm font-medium text-slate-300 capitalize",children:l})]}),r.jsx("button",{className:"copy-button opacity-0 group-hover:opacity-100 transition-opacity duration-200",onClick:()=>navigator.clipboard.writeText(String(s)),title:"Copiar c\xf3digo",children:r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})})})]}),r.jsx("pre",{className:"code-content",children:r.jsx("code",{className:a,...o,children:s})})]}):r.jsx("code",{className:"inline-code",...o,children:s})},a:({children:e,href:t,...a})=>r.jsx("a",{href:t,target:"_blank",rel:"noopener noreferrer",className:"markdown-link",...a,children:e}),table:({children:e,...t})=>r.jsx("div",{className:"table-wrapper",children:r.jsx("table",{className:"markdown-table",...t,children:e})}),th:({children:e,...t})=>r.jsx("th",{className:"table-header",...t,children:e}),td:({children:e,...t})=>r.jsx("td",{className:"table-cell",...t,children:e}),blockquote:({children:e,...t})=>r.jsx("blockquote",{className:"markdown-blockquote",...t,children:r.jsx("div",{className:"quote-content",children:e})}),ul:({children:e,...t})=>r.jsx("ul",{className:"markdown-list unordered",...t,children:e}),ol:({children:e,...t})=>r.jsx("ol",{className:"markdown-list ordered",...t,children:e}),li:({children:e,...t})=>r.jsx("li",{className:"list-item",...t,children:e}),h1:({children:e,...t})=>r.jsx("h1",{className:"markdown-heading h1",...t,children:r.jsx("span",{className:"heading-content",children:e})}),h2:({children:e,...t})=>r.jsx("h2",{className:"markdown-heading h2",...t,children:r.jsx("span",{className:"heading-content",children:e})}),h3:({children:e,...t})=>r.jsx("h3",{className:"markdown-heading h3",...t,children:r.jsx("span",{className:"heading-content",children:e})}),h4:({children:e,...t})=>r.jsx("h4",{className:"markdown-heading h4",...t,children:r.jsx("span",{className:"heading-content",children:e})}),h5:({children:e,...t})=>r.jsx("h5",{className:"markdown-heading h5",...t,children:r.jsx("span",{className:"heading-content",children:e})}),h6:({children:e,...t})=>r.jsx("h6",{className:"markdown-heading h6",...t,children:r.jsx("span",{className:"heading-content",children:e})}),p:({children:e,...t})=>r.jsx("p",{className:"markdown-paragraph",...t,children:e}),hr:({...e})=>r.jsx("hr",{className:"markdown-divider",...e})},children:e}),(e,t)=>{if(t.isStreaming){let a=e.content.length,r=t.content.length;return!(r-a>=100||r<a)}return e.content===t.content}),j=o().memo(({content:e,className:t="",hasWebSearch:a=!1,webSearchAnnotations:o=[],isStreaming:n=!1})=>{let detectWebSearch=e=>{let t=e.match(/\[[\w.-]+\.[\w]+\]/g);return null!==t&&t.length>0},processWebSearchLinks=e=>e,getWebSearchInfo=(e,t)=>{if(t.length>0){let e=new Set(t.map(e=>{try{return new URL(e.url).hostname.replace("www.","")}catch(t){return e.url}}));return{sourceCount:t.length,sources:Array.from(e)}}let a=e.match(/\[[\w.-]+\.[\w]+\]\([^)]+\)/g)||[],r=new Set(a.map(e=>{let t=e.match(/\[([\w.-]+\.[\w]+)\]/);return t?t[1]:e})),s=Array.from(r);return{sourceCount:a.length,sources:s}},{isWebSearchMessage:l,webSearchInfo:i,processedContent:d}=(0,s.useMemo)(()=>{let t=a||detectWebSearch(e),r=t?getWebSearchInfo(e,o):{sourceCount:0,sources:[]},s=t?processWebSearchLinks(e):e;return{isWebSearchMessage:t,webSearchInfo:r,processedContent:s}},[e,a,o]);return(0,r.jsxs)("div",{className:`rafthor-markdown ${t} ${n?"streaming":""}`,children:[l&&(0,r.jsxs)("div",{className:"web-search-indicator",children:[(0,r.jsxs)("div",{className:"search-badge",children:[(0,r.jsxs)("svg",{className:"search-icon",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M21 12a9 9 0 11-18 0 9 9 0 0118 0z"}),r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9 12l2 2 4-4"})]}),r.jsx("span",{children:"Busca na Web"}),r.jsx("div",{className:"source-count",children:i.sourceCount})]}),i.sources.length>0&&(0,r.jsxs)("div",{className:"source-list",children:[i.sources.slice(0,3).map((e,t)=>r.jsx("span",{className:"source-tag",children:e},t)),i.sources.length>3&&(0,r.jsxs)("span",{className:"source-tag more",children:["+",i.sources.length-3]})]})]}),r.jsx(w,{content:d,isStreaming:n})]})});var y=a(80443),N=a(28626),k=a(85574),C=a(39546);function AttachmentDisplay({attachments:e,isUserMessage:t=!1}){let[a,o]=(0,s.useState)(null);if(!e||0===e.length)return null;let handleImageClick=e=>{o(e)},handleDownload=async e=>{try{let t=await fetch(e.url),a=await t.blob(),r=window.URL.createObjectURL(a),s=document.createElement("a");s.href=r,s.download=e.filename,document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(r),document.body.removeChild(s)}catch(e){console.error("Erro ao baixar arquivo:",e)}},formatFileSize=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return(0,r.jsxs)("div",{className:"mt-2 space-y-2",children:[e.map(e=>r.jsx("div",{className:`
            border rounded-lg p-3 max-w-sm
            ${t?"bg-blue-50 border-blue-200":"bg-gray-50 border-gray-200"}
          `,children:"image"===e.type?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(y.Z,{className:"w-4 h-4 text-blue-600"}),r.jsx("span",{className:"text-sm font-medium text-gray-700 truncate",children:e.filename}),r.jsx("span",{className:"text-xs text-gray-500",children:formatFileSize(e.size)})]}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("img",{src:e.url,alt:e.filename,className:"max-w-full h-auto rounded cursor-pointer hover:opacity-90 transition-opacity",style:{maxHeight:"200px"},onClick:()=>handleImageClick(e.url)}),r.jsx("button",{onClick:()=>handleImageClick(e.url),className:"absolute top-2 right-2 bg-black bg-opacity-50 text-white p-1 rounded hover:bg-opacity-70 transition-all",title:"Expandir imagem",children:r.jsx(N.Z,{className:"w-4 h-4"})})]}),r.jsx("div",{className:"flex gap-2",children:(0,r.jsxs)("button",{onClick:()=>handleDownload(e),className:"flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 transition-colors",children:[r.jsx(k.Z,{className:"w-3 h-3"}),"Baixar"]})})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(C.Z,{className:"w-4 h-4 text-red-600"}),r.jsx("span",{className:"text-sm font-medium text-gray-700 truncate",children:e.filename}),r.jsx("span",{className:"text-xs text-gray-500",children:formatFileSize(e.size)})]}),r.jsx("div",{className:"flex gap-2",children:(0,r.jsxs)("button",{onClick:()=>handleDownload(e),className:"flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 transition-colors",children:[r.jsx(k.Z,{className:"w-3 h-3"}),"Baixar PDF"]})})]})},e.id)),a&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4",onClick:()=>o(null),children:(0,r.jsxs)("div",{className:"relative max-w-full max-h-full",children:[r.jsx("img",{src:a,alt:"Imagem expandida",className:"max-w-full max-h-full object-contain"}),r.jsx("button",{onClick:()=>o(null),className:"absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all",children:"✕"})]})})]})}function DeleteMessageModal({isOpen:e,onClose:t,onConfirm:a,messagePreview:s,isDeleting:o=!1}){if(!e)return null;let n=s.length>100?s.substring(0,100)+"...":s;return(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[r.jsx("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm",onClick:t}),(0,r.jsxs)("div",{className:"relative bg-gradient-to-br from-gray-900 via-blue-900/20 to-gray-900 border border-red-500/30 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[r.jsx("div",{className:"bg-gradient-to-r from-red-600/20 to-red-500/20 border-b border-red-500/30 p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center border border-red-500/30",children:r.jsx("svg",{className:"w-6 h-6 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-white",children:"Confirmar Exclus\xe3o"}),r.jsx("p",{className:"text-red-300/70 text-sm",children:"Esta a\xe7\xe3o n\xe3o pode ser desfeita"})]})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[r.jsx("p",{className:"text-gray-300 text-sm leading-relaxed",children:"Tem certeza que deseja excluir esta mensagem permanentemente?"}),(0,r.jsxs)("div",{className:"bg-gray-800/50 border border-gray-600/30 rounded-lg p-3",children:[r.jsx("p",{className:"text-xs text-gray-400 mb-1",children:"Mensagem:"}),(0,r.jsxs)("p",{className:"text-gray-200 text-sm leading-relaxed break-words",children:['"',n,'"']})]}),r.jsx("div",{className:"bg-red-900/20 border border-red-500/30 rounded-lg p-3",children:(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[r.jsx("svg",{className:"w-4 h-4 text-red-400 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),r.jsx("p",{className:"text-red-300/80 text-xs",children:"A mensagem ser\xe1 removida permanentemente do chat e n\xe3o poder\xe1 ser recuperada."})]})})]}),r.jsx("div",{className:"bg-gray-800/30 border-t border-gray-600/30 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3",children:[r.jsx("button",{onClick:t,disabled:o,className:"px-4 py-2 text-sm font-medium text-gray-300 hover:text-white bg-gray-700/50 hover:bg-gray-600/50 border border-gray-600/30 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancelar"}),r.jsx("button",{onClick:a,disabled:o,className:"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-red-600 to-red-500 hover:from-red-500 hover:to-red-400 border border-red-500/30 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-red-500/20 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:o?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"w-4 h-4 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),r.jsx("span",{children:"Excluindo..."})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),r.jsx("span",{children:"Excluir"})]})})]})})]})]})}function RegenerateMessageModal({isOpen:e,onClose:t,onConfirm:a,messagePreview:s,messagesAffectedCount:o,isRegenerating:n=!1}){if(!e)return null;let l=s.length>100?s.substring(0,100)+"...":s;return(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[r.jsx("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm",onClick:t}),(0,r.jsxs)("div",{className:"relative bg-gradient-to-br from-gray-900 via-blue-900/20 to-gray-900 border border-blue-500/30 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[r.jsx("div",{className:"bg-gradient-to-r from-blue-600/20 to-cyan-500/20 border-b border-blue-500/30 p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30",children:r.jsx("svg",{className:"w-6 h-6 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-white",children:"Regenerar Mensagem"}),r.jsx("p",{className:"text-blue-300/70 text-sm",children:"Confirme para continuar"})]})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsxs)("p",{className:"text-gray-300 text-sm leading-relaxed",children:["Deseja regenerar a resposta para esta mensagem?",o>0&&(0,r.jsxs)("span",{className:"text-orange-300",children:[" ","Isso remover\xe1 ",o," mensagem",o>1?"s":""," que ",o>1?"v\xeam":"vem"," depois desta."]})]}),(0,r.jsxs)("div",{className:"bg-gray-800/50 border border-gray-600/30 rounded-lg p-3",children:[r.jsx("p",{className:"text-xs text-gray-400 mb-1",children:"Mensagem que ser\xe1 enviada novamente:"}),(0,r.jsxs)("p",{className:"text-gray-200 text-sm leading-relaxed break-words",children:['"',l,'"']})]}),o>0&&r.jsx("div",{className:"bg-orange-900/20 border border-orange-500/30 rounded-lg p-3",children:(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[r.jsx("svg",{className:"w-4 h-4 text-orange-400 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-orange-300/80 text-xs font-medium mb-1",children:"Aten\xe7\xe3o: Mensagens posteriores ser\xe3o removidas"}),(0,r.jsxs)("p",{className:"text-orange-300/70 text-xs",children:[o," mensagem",o>1?"s":""," que ",o>1?"vieram":"veio"," depois desta ser\xe1",o>1?"m":""," permanentemente removida",o>1?"s":""," para manter a consist\xeancia da conversa."]})]})]})}),r.jsx("div",{className:"bg-blue-900/20 border border-blue-500/30 rounded-lg p-3",children:(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[r.jsx("svg",{className:"w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),r.jsx("p",{className:"text-blue-300/80 text-xs",children:"A IA gerar\xe1 uma nova resposta para esta mensagem. A mensagem original ser\xe1 mantida e apenas as respostas posteriores ser\xe3o removidas."})]})})]}),r.jsx("div",{className:"bg-gray-800/30 border-t border-gray-600/30 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3",children:[r.jsx("button",{onClick:t,disabled:n,className:"px-4 py-2 text-sm font-medium text-gray-300 hover:text-white bg-gray-700/50 hover:bg-gray-600/50 border border-gray-600/30 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancelar"}),r.jsx("button",{onClick:a,disabled:n,className:"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 border border-blue-500/30 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"w-4 h-4 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),r.jsx("span",{children:"Regenerando..."})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),r.jsx("span",{children:"Regenerar"})]})})]})})]})]})}function ChatInterface({messages:e,isLoading:t,isLoadingChat:a,isStreaming:o=!1,streamingMessageId:n,onDeleteMessage:l,onRegenerateMessage:i,onEditMessage:d,onEditAndRegenerate:c,onCopyMessage:m}){let u=(0,s.useRef)(null),[h,x]=(0,s.useState)(null),[p,g]=(0,s.useState)(""),[b,f]=(0,s.useState)(!1),[v,w]=(0,s.useState)(!1),[y,N]=(0,s.useState)({isOpen:!1,messageId:"",messageContent:"",isDeleting:!1}),[k,C]=(0,s.useState)({isOpen:!1,messageId:"",messageContent:"",messagesAffectedCount:0,isRegenerating:!1}),scrollToBottom=()=>{requestAnimationFrame(()=>{u.current?.scrollIntoView({behavior:"smooth"})})};(0,s.useEffect)(()=>{let e=setTimeout(()=>{scrollToBottom()},100);return()=>clearTimeout(e)},[e]);let formatTime=e=>new Date(e).toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"}),handleStartEdit=e=>{x(e.id),g(e.content)},handleSaveEdit=async()=>{if(h&&p.trim()){f(!0);try{let e=await d(h,p.trim());e&&(x(null),g(""))}catch(e){console.error("Erro ao salvar edi\xe7\xe3o:",e)}finally{f(!1)}}},handleSaveAndRegenerate=async()=>{if(h&&p.trim()){w(!0);try{await c(h,p.trim()),x(null),g("")}catch(e){console.error("Erro ao salvar e regenerar:",e)}finally{w(!1)}}},handleCancelEdit=()=>{x(null),g(""),f(!1),w(!1)},handleDeleteClick=e=>{N({isOpen:!0,messageId:e.id,messageContent:e.content,isDeleting:!1})},handleDeleteConfirm=async()=>{N(e=>({...e,isDeleting:!0}));try{await l(y.messageId),N({isOpen:!1,messageId:"",messageContent:"",isDeleting:!1})}catch(e){console.error("Erro ao deletar mensagem:",e),N(e=>({...e,isDeleting:!1}))}},handleRegenerateClick=t=>{let a=e.findIndex(e=>e.id===t.id),r=e.length-a-1;C({isOpen:!0,messageId:t.id,messageContent:t.content,messagesAffectedCount:r,isRegenerating:!1})},handleRegenerateConfirm=async()=>{C({isOpen:!1,messageId:"",messageContent:"",messagesAffectedCount:0,isRegenerating:!1});try{i(k.messageId)}catch(e){console.error("Erro ao regenerar mensagem:",e)}},MessageActions=({message:e,isUser:t})=>(0,r.jsxs)("div",{className:"flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[r.jsx("button",{onClick:()=>handleDeleteClick(e),className:"p-1.5 text-white/40 hover:text-red-400 hover:bg-red-400/10 rounded transition-all duration-200 hover:scale-110",title:"Excluir mensagem",children:r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})}),t&&r.jsx("button",{onClick:()=>handleRegenerateClick(e),className:"p-1.5 text-white/40 hover:text-blue-400 hover:bg-blue-400/10 rounded transition-all duration-200 hover:scale-110",title:"Regenerar mensagem",children:r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),r.jsx("button",{onClick:()=>handleStartEdit(e),className:"p-1.5 text-white/40 hover:text-green-400 hover:bg-green-400/10 rounded transition-all duration-200 hover:scale-110",title:"Editar mensagem",children:r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),r.jsx("button",{onClick:()=>m(e.content),className:"p-1.5 text-white/40 hover:text-purple-400 hover:bg-purple-400/10 rounded transition-all duration-200 hover:scale-110",title:"Copiar mensagem",children:r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})})})]});return(0,r.jsxs)("div",{className:"h-full overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent",style:{maxHeight:"100%"},children:[a?r.jsx("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md mx-auto",children:[r.jsx("div",{className:"mb-8",children:r.jsx("div",{className:"w-20 h-20 mx-auto bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-blue-500/30 backdrop-blur-sm",children:r.jsx("svg",{className:"w-10 h-10 text-blue-400 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h2",{className:"text-2xl font-semibold text-white/90 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent",children:"Carregando mensagens"}),r.jsx("p",{className:"text-white/60 text-base leading-relaxed",children:"Aguarde enquanto carregamos o hist\xf3rico da conversa..."}),r.jsx("div",{className:"mt-6",children:r.jsx("div",{className:"w-48 h-1 bg-gray-700/50 rounded-full mx-auto overflow-hidden",children:r.jsx("div",{className:"h-full bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full animate-pulse"})})})]})]})}):0===e.length?r.jsx("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md mx-auto",children:[r.jsx("div",{className:"mb-8",children:r.jsx("div",{className:"w-20 h-20 mx-auto bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-blue-500/30 backdrop-blur-sm",children:r.jsx("svg",{className:"w-10 h-10 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})})}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h2",{className:"text-2xl font-semibold text-white/90 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent",children:"Comece uma nova conversa"}),r.jsx("p",{className:"text-white/60 text-base leading-relaxed",children:"Digite sua mensagem abaixo para come\xe7ar a conversar com a IA"}),(0,r.jsxs)("div",{className:"mt-8 grid grid-cols-1 gap-3",children:[r.jsx("div",{className:"bg-blue-900/20 backdrop-blur-sm rounded-lg p-4 border border-blue-600/20 hover:border-blue-500/40 transition-all duration-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center",children:r.jsx("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})})}),(0,r.jsxs)("div",{className:"text-left",children:[r.jsx("p",{className:"text-white/80 text-sm font-medium",children:"Fa\xe7a perguntas"}),r.jsx("p",{className:"text-white/50 text-xs",children:"Tire d\xfavidas sobre qualquer assunto"})]})]})}),r.jsx("div",{className:"bg-cyan-900/20 backdrop-blur-sm rounded-lg p-4 border border-cyan-600/20 hover:border-cyan-500/40 transition-all duration-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-8 h-8 bg-cyan-500/20 rounded-lg flex items-center justify-center",children:r.jsx("svg",{className:"w-4 h-4 text-cyan-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"})})}),(0,r.jsxs)("div",{className:"text-left",children:[r.jsx("p",{className:"text-white/80 text-sm font-medium",children:"Pe\xe7a ajuda com c\xf3digo"}),r.jsx("p",{className:"text-white/50 text-xs",children:"Programa\xe7\xe3o, debugging e explica\xe7\xf5es"})]})]})}),r.jsx("div",{className:"bg-purple-900/20 backdrop-blur-sm rounded-lg p-4 border border-purple-600/20 hover:border-purple-500/40 transition-all duration-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center",children:r.jsx("svg",{className:"w-4 h-4 text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,r.jsxs)("div",{className:"text-left",children:[r.jsx("p",{className:"text-white/80 text-sm font-medium",children:"Crie conte\xfado"}),r.jsx("p",{className:"text-white/50 text-xs",children:"Textos, resumos e ideias criativas"})]})]})})]})]})]})}):(0,r.jsxs)("div",{className:"space-y-4 min-h-0",children:[e.map(e=>(0,r.jsxs)("div",{className:`flex items-start space-x-2 group animate-message-slide-in ${"user"===e.sender?"flex-row-reverse space-x-reverse":""}`,children:[r.jsx("div",{className:`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg border-2 ${"user"===e.sender?"bg-gradient-to-br from-green-400 via-emerald-500 to-green-600 border-green-400/30":"bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 border-blue-400/30"}`,children:r.jsx("span",{className:"text-white text-sm font-bold drop-shadow-sm",children:"user"===e.sender?"U":"AI"})}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("div",{className:`backdrop-blur-sm rounded-2xl p-3 max-w-3xl border ${"user"===e.sender?"bg-green-600/20 border-green-500/20 rounded-tr-md ml-auto":"bg-blue-600/20 border-blue-500/20 rounded-tl-md"}`,children:h===e.id?(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx("textarea",{value:p,onChange:e=>g(e.target.value),className:"w-full min-h-[120px] p-3 bg-gray-800/50 border border-gray-600/30 rounded-lg text-white placeholder-gray-400 resize-y focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50",placeholder:"Digite sua mensagem...",autoFocus:!0}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[r.jsx("button",{onClick:handleCancelEdit,disabled:b||v,className:"px-3 py-1.5 text-sm text-gray-400 hover:text-white bg-gray-700/50 hover:bg-gray-600/50 rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancelar"}),r.jsx("button",{onClick:handleSaveEdit,disabled:b||v||!p.trim(),className:"px-3 py-1.5 text-sm text-white bg-blue-600 hover:bg-blue-500 rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:b?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"w-4 h-4 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),r.jsx("span",{children:"Salvando..."})]}):r.jsx("span",{children:"Salvar"})}),r.jsx("button",{onClick:handleSaveAndRegenerate,disabled:b||v||!p.trim(),className:"px-3 py-1.5 text-sm text-white bg-green-600 hover:bg-green-500 rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:v?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"w-4 h-4 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),r.jsx("span",{children:"Salvando e Enviando..."})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("span",{children:"Salvar e Enviar"}),r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})})]})})]})]}):(0,r.jsxs)("div",{children:[e.attachments&&e.attachments.length>0&&r.jsx(AttachmentDisplay,{attachments:e.attachments,isUserMessage:"user"===e.sender}),e.content&&r.jsx(j,{content:e.content,hasWebSearch:e.hasWebSearch,webSearchAnnotations:e.webSearchAnnotations,isStreaming:o&&e.id===n})]})}),r.jsx("div",{className:`flex items-center gap-3 mt-2 ${"user"===e.sender?"justify-end":"justify-start"}`,children:"user"===e.sender?(0,r.jsxs)(r.Fragment,{children:[r.jsx(MessageActions,{message:e,isUser:!0}),r.jsx("p",{className:"text-white/40 text-xs",children:formatTime(e.timestamp)})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("p",{className:"text-white/40 text-xs",children:formatTime(e.timestamp)}),r.jsx(MessageActions,{message:e,isUser:!1})]})})]})]},e.id)),t&&(0,r.jsxs)("div",{className:"flex items-start space-x-2 animate-message-slide-in",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 animate-pulse shadow-lg border-2 border-blue-400/30",children:r.jsx("span",{className:"text-white text-sm font-bold drop-shadow-sm",children:"AI"})}),r.jsx("div",{className:"flex-1",children:r.jsx("div",{className:"bg-blue-600/20 backdrop-blur-sm rounded-2xl rounded-tl-md p-3 max-w-3xl border border-blue-500/20 shimmer-effect",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("div",{className:"flex space-x-1",children:[r.jsx("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-typing"}),r.jsx("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-typing",style:{animationDelay:"0.2s"}}),r.jsx("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-typing",style:{animationDelay:"0.4s"}})]}),r.jsx("span",{className:"text-white/60 text-sm animate-pulse",children:"Digitando..."})]})})})]}),r.jsx("div",{ref:u,className:"h-0"})]}),r.jsx(DeleteMessageModal,{isOpen:y.isOpen,onClose:()=>{N({isOpen:!1,messageId:"",messageContent:"",isDeleting:!1})},onConfirm:handleDeleteConfirm,messagePreview:y.messageContent,isDeleting:y.isDeleting}),r.jsx(RegenerateMessageModal,{isOpen:k.isOpen,onClose:()=>{C({isOpen:!1,messageId:"",messageContent:"",messagesAffectedCount:0,isRegenerating:!1})},onConfirm:handleRegenerateConfirm,messagePreview:k.messageContent,messagesAffectedCount:k.messagesAffectedCount,isRegenerating:k.isRegenerating})]})}var M=a(42610);let S=[{id:"gpt-4.1-nano",name:"GPT-4.1 Nano",description:"R\xe1pido e eficiente"},{id:"gpt-4-turbo",name:"GPT-4 Turbo",description:"Mais poderoso"},{id:"claude-3-sonnet",name:"Claude 3 Sonnet",description:"Criativo e preciso"},{id:"gemini-pro",name:"Gemini Pro",description:"Multimodal"}];function InputBar({message:e,setMessage:t,onSendMessage:a,isLoading:o,selectedModel:n,onModelChange:l,onScrollToTop:i,onScrollToBottom:d,isStreaming:c=!1,onCancelStreaming:m,onOpenModelModal:u,username:h,chatId:x,activeAttachmentsCount:p=0}){let[g,b]=(0,s.useState)(!1),[f,v]=(0,s.useState)([]),[w,j]=(0,s.useState)([]),[y,N]=(0,s.useState)(!1),k=(0,s.useRef)(null),C=(0,s.useRef)(null),D=(0,s.useRef)(null);(0,s.useEffect)(()=>()=>{D.current&&clearTimeout(D.current)},[]),(0,s.useEffect)(()=>{adjustTextareaHeight()},[e]),(0,s.useEffect)(()=>{let handleDragDropAttachments=e=>{let{attachments:t}=e.detail,a=t.map(e=>({id:e.id,filename:e.filename,type:"image"===e.type?"image":"document",file:new File([],e.filename,{type:"image"===e.type?"image/jpeg":"application/pdf"})}));v(e=>[...e,...a]),j(e=>[...e,...t]),console.log(`📎 ${t.length} anexo(s) adicionado(s) via drag-n-drop`)};return window.addEventListener("dragDropAttachments",handleDragDropAttachments),()=>{window.removeEventListener("dragDropAttachments",handleDragDropAttachments)}},[]);let handleSend=()=>{console.log("=== DEBUG: HANDLE SEND CHAMADO ==="),console.log("Mensagem:",e),console.log("Mensagem trim:",e?.trim()),console.log("Anexos locais:",f.length),console.log("Anexos processados:",w.length),console.log("IsLoading:",o),console.log("IsUploading:",y);let t=e?.trim(),r=f.length>0,s=(t||r)&&!o&&!y;console.log("=== DEBUG: CONDI\xc7\xd5ES DE ENVIO ==="),console.log("Tem mensagem:",!!t),console.log("Tem anexos:",r),console.log("Pode enviar:",s),s?(console.log("=== DEBUG: ENVIANDO MENSAGEM ==="),console.log("Mensagem:",e),console.log("Anexos locais:",f.length),console.log("Anexos processados:",w.length),console.log("Anexos processados detalhes:",w),console.log("Web Search Enabled:",g),a(w,g),v([]),j([])):(console.log("=== DEBUG: N\xc3O PODE ENVIAR MENSAGEM ==="),console.log("Raz\xf5es:",{semMensagemESemAnexos:!t&&!r,carregando:o,fazendoUpload:y}))},handleFileSelect=async e=>{let t=e.target.files;if(t&&h&&x){N(!0);try{let e=[];for(let a of Array.from(t)){let t={id:Date.now().toString()+Math.random().toString(36).substring(2,9),filename:a.name,type:a.type.startsWith("image/")?"image":"document",file:a};e.push(t)}v(t=>[...t,...e]);let a=await M.default.uploadMultipleAttachments(Array.from(t),h,x);j(e=>[...e,...a.map(e=>e.metadata)])}catch(e){console.error("Erro ao processar arquivos:",e),v(e=>e.filter(e=>!Array.from(t).some(t=>t.name===e.filename)))}finally{N(!1),C.current&&(C.current.value="")}}},removeAttachment=e=>{let t=f.find(t=>t.id===e);t&&(v(t=>t.filter(t=>t.id!==e)),j(e=>e.filter(e=>e.filename!==t.filename)))},adjustTextareaHeight=()=>{let e=k.current;if(!e)return;let t=e.scrollTop;e.style.height="auto";let a=e.scrollHeight;e.style.height=Math.max(44,Math.min(a,120))+"px",a>120?(e.style.overflowY="auto",t>0&&(e.scrollTop=t)):e.style.overflowY="hidden"},E=S.find(e=>e.id===n),L=E?E.name:n;return(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-x-0 -top-8 h-8 bg-gradient-to-t from-blue-950/30 via-blue-950/10 to-transparent pointer-events-none blur-sm"}),(0,r.jsxs)("div",{className:"p-3 sm:p-4 lg:p-6 bg-gradient-to-r from-blue-950/98 via-blue-900/98 to-blue-950/98 backdrop-blur-xl relative shadow-2xl shadow-blue-900/60",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/8 via-transparent to-cyan-500/8 pointer-events-none"}),r.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-blue-600/15 via-blue-500/5 to-transparent pointer-events-none"}),r.jsx("div",{className:"absolute top-0 inset-x-0 h-px bg-gradient-to-r from-transparent via-blue-400/30 to-transparent pointer-events-none"}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto relative z-10",children:[f.length>0&&r.jsx("div",{className:"mb-3 sm:mb-4 p-3 sm:p-4 bg-blue-900/30 backdrop-blur-sm rounded-lg sm:rounded-xl border border-blue-600/30",children:r.jsx("div",{className:"flex flex-wrap gap-2 sm:gap-3",children:f.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3 bg-blue-800/40 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 border border-blue-600/20",children:["image"===e.type?r.jsx("svg",{className:"w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-400 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}):r.jsx("svg",{className:"w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-400 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),r.jsx("span",{className:"text-xs sm:text-sm text-blue-100 truncate max-w-20 sm:max-w-32 font-medium",children:e.filename}),r.jsx("button",{onClick:()=>removeAttachment(e.id),className:"text-blue-300 hover:text-red-400 transition-colors p-0.5 sm:p-1 rounded-full hover:bg-red-500/20 flex-shrink-0",children:r.jsx("svg",{className:"w-3.5 h-3.5 sm:w-4 sm:h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]},e.id))})}),(n||p>0)&&(0,r.jsxs)("div",{className:"mb-1 flex items-center justify-start space-x-2",children:[n&&(0,r.jsxs)("div",{className:"bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg",children:[r.jsx("span",{className:"text-xs text-blue-300",children:"Modelo: "}),r.jsx("span",{className:"text-xs text-cyan-300 font-medium",children:L})]}),p>0&&(0,r.jsxs)("div",{className:"bg-green-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-green-600/30 shadow-lg",children:[r.jsx("span",{className:"text-xs text-green-300",children:"Anexos: "}),r.jsx("span",{className:"text-xs text-green-200 font-medium",children:p})]})]}),(0,r.jsxs)("div",{className:"flex items-end space-x-2 sm:space-x-3",children:[(0,r.jsxs)("div",{className:"flex-1 relative bg-gradient-to-r from-blue-900/80 to-blue-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-blue-600/30 shadow-xl shadow-blue-900/50",children:[r.jsx("div",{className:"absolute inset-0 rounded-xl sm:rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]"}),(0,r.jsxs)("div",{className:"relative z-10 flex items-end space-x-2 sm:space-x-3 p-3 sm:p-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2",children:[r.jsx("button",{onClick:u,className:"p-2 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 hover:scale-105",title:"Selecionar modelo",children:r.jsx("svg",{className:"w-4 h-4 sm:w-5 sm:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),r.jsx("button",{onClick:()=>{C.current&&C.current.click()},disabled:y,className:"p-2.5 rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105",title:"Anexar arquivo",children:y?r.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-400"}):r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"})})}),!["local/","offline/"].some(e=>n.startsWith(e))&&r.jsx("button",{onClick:()=>{b(!g)},className:`p-2.5 rounded-xl transition-all duration-200 hover:shadow-lg border hover:scale-105 backdrop-blur-sm ${g?"bg-cyan-600/30 hover:bg-cyan-600/40 text-cyan-200 hover:text-cyan-100 border-cyan-500/50 shadow-lg shadow-cyan-500/20":"bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20 hover:shadow-blue-500/20"}`,title:`Busca na web - ${g?"Ativada":"Desativada"}`,children:r.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{d:"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z"})})})]}),r.jsx("div",{className:"flex-1 relative",children:r.jsx("textarea",{ref:k,value:e,onChange:e=>{let a=e.target.value;t(a),requestAnimationFrame(()=>{adjustTextareaHeight()})},onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),handleSend())},className:"w-full bg-transparent border-none rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none resize-none text-sm selection:bg-blue-500/30",rows:1,placeholder:"Digite sua mensagem aqui...",disabled:o||c,style:{height:"44px",minHeight:"44px",maxHeight:"120px",lineHeight:"1.5",wordWrap:"break-word",whiteSpace:"pre-wrap",overflowY:"hidden",overflowX:"hidden",scrollbarWidth:"thin"}})}),c?r.jsx("button",{onClick:m,className:"p-3 rounded-xl bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-500 hover:via-red-400 hover:to-red-500 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-red-500/30",title:"Parar gera\xe7\xe3o",children:r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}):r.jsx("button",{onClick:handleSend,disabled:!e?.trim()&&0===f.length||o||y||c,className:"p-3 rounded-xl bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105 disabled:hover:scale-100 border border-blue-500/30",title:"Enviar mensagem",children:o?r.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white"}):r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})})]})]}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[i&&(0,r.jsxs)("button",{onClick:i,className:"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95",title:"Ir para o topo",children:[r.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"}),r.jsx("svg",{className:"relative w-5 h-5 transform group-hover:-translate-y-0.5 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M5 10l7-7m0 0l7 7m-7-7v18"})})]}),d&&(0,r.jsxs)("button",{onClick:d,className:"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95",title:"Ir para o final",children:[r.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"}),r.jsx("svg",{className:"relative w-5 h-5 transform group-hover:translate-y-0.5 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M19 14l-7 7m0 0l-7-7m7 7V4"})})]})]})]}),r.jsx("input",{ref:C,type:"file",multiple:!0,accept:"image/png,image/jpeg,image/webp,application/pdf",onChange:handleFileSelect,className:"hidden"})]})]})]})}var D=a(88908),E=a(69079),L=a(61607);let dashboard_DownloadModal=({isOpen:e,onClose:t,messages:a,chatName:o})=>{let[n,l]=(0,s.useState)("all"),[i,d]=(0,s.useState)(!1),[c,m]=(0,s.useState)(!1);(0,s.useEffect)(()=>{m(!0)},[]);let processMessageContent=e=>{try{E.TU.setOptions({breaks:!0,gfm:!0});let t=(0,E.TU)(e);if(t instanceof Promise)return e.replace(/\n/g,"<br>");return t=L.Z.sanitize(t)}catch(t){return console.error("Erro ao processar conte\xfado:",t),e.replace(/\n/g,"<br>")}},generateStyledHTML=(e,t,a)=>{let r={all:"Todas as mensagens",user:"Mensagens do usu\xe1rio",ai:"Mensagens da IA"};return`<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${t} - ${r[a]}</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- KaTeX CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css" integrity="sha384-GvrOXuhMATgEsSwCs4smul74iXGOixntILdUW9XmUC6+HX0sLNAK3q71HotJqlAn" crossorigin="anonymous">

    <!-- Highlight.js CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">

    <!-- KaTeX JavaScript -->
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js" integrity="sha384-cpW21h6RZv/phavutF+AuVYrr+dA8xD9zs6FwLpaCct6O9ctzYFfFr4dgmgccOTx" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>

    <!-- Highlight.js JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #0c1426 0%, #1e293b 25%, #0f172a 50%, #1e293b 75%, #0c1426 100%);
            background-attachment: fixed;
            min-height: 100vh;
            color: #f8fafc;
            line-height: 1.7;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 3rem 2rem;
            position: relative;
            z-index: 1;
        }

        .header {
            text-align: center;
            margin-bottom: 4rem;
            padding: 3rem 2rem;
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.3), rgba(59, 130, 246, 0.2));
            border-radius: 2rem;
            border: 1px solid rgba(59, 130, 246, 0.4);
            backdrop-filter: blur(20px);
            box-shadow:
                0 25px 50px -12px rgba(0, 0, 0, 0.5),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.8), transparent);
        }

        .header::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            animation: rotate 20s linear infinite;
            z-index: -1;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #60a5fa 0%, #93c5fd 25%, #dbeafe 50%, #93c5fd 75%, #60a5fa 100%);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
            position: relative;
            z-index: 1;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .header p {
            color: rgba(203, 213, 225, 0.9);
            font-size: 1.2rem;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }
        
        .messages {
            display: flex;
            flex-direction: column;
            gap: 2.5rem;
        }

        .message {
            border-radius: 1.5rem;
            padding: 2rem;
            border: 1px solid rgba(59, 130, 246, 0.3);
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow:
                0 10px 25px -5px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.05);
        }

        .message::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.8), transparent);
        }

        .message.user {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(34, 197, 94, 0.1));
            border-color: rgba(16, 185, 129, 0.4);
            margin-left: 3rem;
            transform: translateX(0);
        }

        .message.user::before {
            background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.8), transparent);
        }

        .message.ai {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(147, 51, 234, 0.1));
            border-color: rgba(59, 130, 246, 0.4);
            margin-right: 3rem;
            transform: translateX(0);
        }
        
        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            font-weight: 700;
            font-size: 1.1rem;
            position: relative;
            z-index: 1;
        }

        .message-header .icon {
            width: 40px;
            height: 40px;
            margin-right: 1rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow:
                0 4px 8px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .message-header .icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
            border-radius: 50%;
        }

        .message.user .icon {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.8), rgba(34, 197, 94, 0.6));
            border: 2px solid rgba(16, 185, 129, 0.5);
        }

        .message.ai .icon {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(147, 51, 234, 0.6));
            border: 2px solid rgba(59, 130, 246, 0.5);
        }
        
        .message-content {
            background: linear-gradient(135deg, rgba(15, 23, 42, 0.6), rgba(30, 41, 59, 0.4));
            padding: 2rem;
            border-radius: 1.25rem;
            border: 1px solid rgba(59, 130, 246, 0.2);
            word-wrap: break-word;
            font-size: 1rem;
            line-height: 1.8;
            backdrop-filter: blur(10px);
            box-shadow:
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                0 4px 6px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .message-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        }

        /* ===== ESTILOS PARA MARKDOWN ===== */
        .message-content h1,
        .message-content h2,
        .message-content h3,
        .message-content h4,
        .message-content h5,
        .message-content h6 {
            font-weight: 700;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
            line-height: 1.3;
            background: linear-gradient(135deg, #60a5fa, #93c5fd, #dbeafe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .message-content h1 { font-size: 1.875rem; border-bottom: 2px solid rgba(59, 130, 246, 0.3); padding-bottom: 0.5rem; }
        .message-content h2 { font-size: 1.5rem; border-bottom: 1px solid rgba(59, 130, 246, 0.2); padding-bottom: 0.25rem; }
        .message-content h3 { font-size: 1.25rem; }
        .message-content h4 { font-size: 1.125rem; }
        .message-content h5 { font-size: 1rem; }
        .message-content h6 { font-size: 0.875rem; }

        .message-content p {
            margin-bottom: 1rem;
            line-height: 1.7;
            color: #cbd5e1;
        }

        .message-content ul,
        .message-content ol {
            margin-bottom: 1rem;
            padding-left: 1.5rem;
            color: #cbd5e1;
        }

        .message-content ul li {
            position: relative;
            margin-bottom: 0.5rem;
            list-style: none;
            padding-left: 1.25rem;
        }

        .message-content ul li::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0.6rem;
            width: 6px;
            height: 6px;
            background: linear-gradient(135deg, #3b82f6, #60a5fa);
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .message-content ol li {
            margin-bottom: 0.5rem;
        }

        .message-content blockquote {
            margin: 1rem 0;
            padding: 1rem 1.25rem;
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.1), rgba(59, 130, 246, 0.05));
            border-left: 4px solid #3b82f6;
            border-radius: 0 0.5rem 0.5rem 0;
            font-style: italic;
            color: #e2e8f0;
        }

        .message-content code {
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.3), rgba(59, 130, 246, 0.2));
            color: #fbbf24;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 0.875rem;
            font-weight: 500;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        .message-content pre {
            background: linear-gradient(135deg, rgba(13, 28, 74, 0.8), rgba(30, 58, 138, 0.6)) !important;
            border: 1px solid rgba(59, 130, 246, 0.4);
            border-radius: 0.75rem;
            padding: 1.25rem;
            margin: 1rem 0;
            overflow-x: auto;
            position: relative;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .message-content pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #60a5fa, #93c5fd);
            border-radius: 0.75rem 0.75rem 0 0;
        }

        .message-content pre code {
            background: transparent !important;
            color: #e2e8f0 !important;
            padding: 0 !important;
            border: none !important;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .message-content a {
            color: #60a5fa;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 1px solid transparent;
        }

        .message-content a:hover {
            color: #93c5fd;
            border-bottom-color: #93c5fd;
        }

        .message-content table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin: 1rem 0;
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.1), rgba(59, 130, 246, 0.05));
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .message-content th {
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.8), rgba(59, 130, 246, 0.6));
            color: #ffffff;
            font-weight: 600;
            padding: 0.75rem 1rem;
            text-align: left;
            font-size: 0.875rem;
        }

        .message-content td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid rgba(59, 130, 246, 0.2);
            color: #cbd5e1;
        }

        .message-content tr:last-child td {
            border-bottom: none;
        }

        .message-content hr {
            margin: 1.5rem 0;
            border: none;
            height: 2px;
            background: linear-gradient(90deg, transparent, #3b82f6, #60a5fa, #3b82f6, transparent);
            border-radius: 1px;
        }

        .message-content strong {
            color: #f1f5f9;
            font-weight: 700;
        }

        .message-content em {
            color: #cbd5e1;
            font-style: italic;
        }

        /* ===== ESTILOS PARA KATEX ===== */
        .message-content .katex {
            font-size: 1.1em !important;
            color: #e2e8f0 !important;
        }

        .message-content .katex-display {
            margin: 1.5rem 0 !important;
            padding: 1rem !important;
            background: rgba(30, 58, 138, 0.1) !important;
            border: 1px solid rgba(59, 130, 246, 0.3) !important;
            border-radius: 0.5rem !important;
            overflow-x: auto !important;
            text-align: center;
        }

        .message-content .katex .base {
            color: #f1f5f9 !important;
        }

        .message-content .katex .mbin,
        .message-content .katex .mrel {
            color: #60a5fa !important;
        }

        .message-content .katex .mord {
            color: #e2e8f0 !important;
        }

        .message-content .katex .mop {
            color: #34d399 !important;
        }

        .message-content .katex .mopen,
        .message-content .katex .mclose {
            color: #fbbf24 !important;
        }

        /* ===== ESTILOS PARA HIGHLIGHT.JS ===== */
        .message-content .hljs {
            background: transparent !important;
            color: #e2e8f0;
            padding: 0;
        }

        .message-content .hljs-keyword {
            color: #c084fc !important;
            font-weight: 600;
        }

        .message-content .hljs-string {
            color: #34d399 !important;
        }

        .message-content .hljs-number {
            color: #fbbf24 !important;
        }

        .message-content .hljs-comment {
            color: #6b7280 !important;
            font-style: italic;
        }

        .message-content .hljs-function {
            color: #60a5fa !important;
        }

        .message-content .hljs-variable {
            color: #f87171 !important;
        }

        .message-content .hljs-title {
            color: #fbbf24 !important;
            font-weight: 600;
        }

        .message-content .hljs-attr {
            color: #60a5fa !important;
        }

        .message-content .hljs-built_in {
            color: #c084fc !important;
        }

        .message-content .hljs-type {
            color: #34d399 !important;
        }

        .message-content .hljs-literal {
            color: #f87171 !important;
        }

        .message-content .hljs-meta {
            color: #6b7280 !important;
        }

        .message-content .hljs-tag {
            color: #60a5fa !important;
        }

        .message-content .hljs-name {
            color: #fbbf24 !important;
        }

        /* ===== RESPONSIVIDADE ===== */
        /* ===== RESPONSIVIDADE MELHORADA ===== */
        @media (max-width: 768px) {
            .container {
                padding: 1.5rem 1rem;
            }

            .header {
                padding: 2rem 1.5rem;
                margin-bottom: 3rem;
            }

            .header h1 {
                font-size: 2.25rem;
            }

            .header p {
                font-size: 1rem;
            }

            .message {
                margin-left: 1rem !important;
                margin-right: 1rem !important;
                padding: 1.5rem;
            }

            .message-header .icon {
                width: 32px;
                height: 32px;
                font-size: 1rem;
            }

            .message-content {
                padding: 1.5rem;
                font-size: 0.9rem;
            }

            .message-content h1 { font-size: 1.5rem; }
            .message-content h2 { font-size: 1.25rem; }
            .message-content h3 { font-size: 1.125rem; }

            .message-content pre {
                padding: 1rem;
                font-size: 0.8rem;
            }

            .message-content .katex-display {
                font-size: 0.9em !important;
                padding: 0.75rem !important;
            }

            .message-time {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
            }

            .footer {
                padding: 2rem 1.5rem;
                margin-top: 3rem;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.875rem;
            }

            .message {
                margin-left: 0.5rem !important;
                margin-right: 0.5rem !important;
                padding: 1.25rem;
            }

            .message-content {
                padding: 1.25rem;
                font-size: 0.875rem;
            }

            .message-content h1 { font-size: 1.25rem; }
            .message-content h2 { font-size: 1.125rem; }
            .message-content h3 { font-size: 1rem; }
        }

        /* ===== EFEITOS ESPECIAIS ===== */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message {
            animation: fadeIn 0.6s ease-out;
        }

        .message:nth-child(even) {
            animation-delay: 0.1s;
        }

        .message:nth-child(odd) {
            animation-delay: 0.2s;
        }

        /* ===== SCROLL SUAVE ===== */
        html {
            scroll-behavior: smooth;
        }

        /* ===== SELE\xc7\xc3O DE TEXTO ===== */
        ::selection {
            background: rgba(59, 130, 246, 0.3);
            color: #ffffff;
        }

        ::-moz-selection {
            background: rgba(59, 130, 246, 0.3);
            color: #ffffff;
        }

        .message-time {
            font-size: 0.875rem;
            color: rgba(148, 163, 184, 0.8);
            margin-top: 1.5rem;
            text-align: right;
            font-weight: 500;
            padding: 0.5rem 1rem;
            background: rgba(15, 23, 42, 0.3);
            border-radius: 0.75rem;
            border: 1px solid rgba(59, 130, 246, 0.1);
            backdrop-filter: blur(5px);
            display: inline-block;
            float: right;
            clear: both;
        }

        .footer {
            text-align: center;
            margin-top: 4rem;
            padding: 3rem 2rem;
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.2), rgba(59, 130, 246, 0.1));
            border-radius: 2rem;
            border: 1px solid rgba(59, 130, 246, 0.3);
            backdrop-filter: blur(20px);
            box-shadow:
                0 10px 25px -5px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.05);
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.6), transparent);
        }

        .footer p {
            color: rgba(203, 213, 225, 0.9);
            font-size: 1rem;
            font-weight: 500;
            margin: 0;
            background: linear-gradient(135deg, #94a3b8, #cbd5e1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .message.user {
                margin-left: 0;
            }
            
            .message.ai {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${t}</h1>
            <p>${r[a]} • ${e.length} mensagem${1!==e.length?"s":""}</p>
        </div>
        
        <div class="messages">
            ${e.map(e=>`
                <div class="message ${"user"===e.role?"user":"ai"}">
                    <div class="message-header">
                        <div class="icon">
                            ${"user"===e.role?"\uD83D\uDC64":"\uD83E\uDD16"}
                        </div>
                        ${"user"===e.role?"Voc\xea":"IA"}
                    </div>
                    <div class="message-content">${processMessageContent(e.content)}</div>
                    <div class="message-time">
                        ${new Date(e.timestamp).toLocaleString("pt-BR")}
                    </div>
                </div>
            `).join("")}
        </div>
        
        <div class="footer">
            <p>✨ Exportado em ${new Date().toLocaleString("pt-BR")} • Rafthor AI ✨</p>
        </div>
    </div>

    <script>
        // Inicializar Highlight.js
        document.addEventListener('DOMContentLoaded', function() {
            hljs.highlightAll();

            // Inicializar KaTeX
            if (typeof renderMathInElement !== 'undefined') {
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: '$$', right: '$$', display: true},
                        {left: '$', right: '$', display: false},
                        {left: '\\\\(', right: '\\\\)', display: false},
                        {left: '\\\\[', right: '\\\\]', display: true}
                    ],
                    throwOnError: false,
                    errorColor: '#cc0000',
                    strict: false,
                    trust: false,
                    macros: {
                        "\\\\f": "#1f(#2)"
                    }
                });
            }
        });
    </script>
</body>
</html>`},handleDownload=async()=>{d(!0);try{let e=a;"user"===n?e=a.filter(e=>"user"===e.role):"ai"===n&&(e=a.filter(e=>"assistant"===e.role));let r=generateStyledHTML(e,o,n),s=new Blob([r],{type:"text/html"}),l=URL.createObjectURL(s),i=document.createElement("a");i.href=l,i.download=`${o}_${n}_messages.html`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(l),t()}catch(e){console.error("Error generating download:",e)}finally{d(!1)}};if(!c||!e)return null;let u=r.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center transition-opacity duration-300",style:{zIndex:99999,position:"fixed",top:0,left:0,right:0,bottom:0},children:(0,r.jsxs)("div",{className:"border border-blue-600/30 rounded-2xl shadow-2xl p-0 w-full max-w-lg transform transition-all duration-300 relative",style:{background:"linear-gradient(135deg, rgba(30, 58, 138, 0.95) 0%, rgba(29, 78, 216, 0.95) 50%, rgba(30, 58, 138, 0.95) 100%)",backdropFilter:"blur(20px)",position:"relative",zIndex:1e5},children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-cyan-500/10 pointer-events-none rounded-2xl"}),(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-blue-600/30 relative z-10",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center",children:r.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-semibold text-blue-100",children:"Download do Chat"}),r.jsx("p",{className:"text-sm text-blue-300/70",children:"Exporte suas conversas em HTML"})]})]}),r.jsx("button",{onClick:t,className:"text-blue-300 hover:text-blue-100 transition-all duration-200 p-2 rounded-xl hover:bg-blue-800/40 hover:scale-105",children:r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("div",{className:"p-6 relative z-10",children:[(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("p",{className:"text-blue-200 font-medium mb-2",children:"Escolha quais mensagens incluir no arquivo HTML:"}),r.jsx("p",{className:"text-blue-300/70 text-sm",children:"O arquivo ser\xe1 exportado com formata\xe7\xe3o completa, incluindo Markdown, LaTeX, syntax highlighting e estilos profissionais"})]}),(0,r.jsxs)("div",{className:"space-y-4 mb-8",children:[r.jsx("label",{className:`flex items-center p-4 rounded-xl border transition-all duration-200 cursor-pointer backdrop-blur-sm hover:scale-[1.02] ${"all"===n?"border-blue-500/50 bg-blue-600/20 shadow-lg shadow-blue-500/20":"border-blue-600/30 bg-blue-900/30 hover:border-blue-500/40 hover:bg-blue-800/40"}`,children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("input",{type:"radio",name:"downloadType",value:"all",checked:"all"===n,onChange:e=>l(e.target.value),className:"w-4 h-4 text-blue-600 bg-blue-900 border-blue-600 focus:ring-blue-500 focus:ring-2"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("svg",{className:"w-5 h-5 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),r.jsx("div",{className:"text-blue-100 font-semibold",children:"Todas as mensagens"})]}),r.jsx("div",{className:"text-blue-300/70 text-sm mt-1",children:"Inclui mensagens do usu\xe1rio e da IA"})]})]})}),r.jsx("label",{className:`flex items-center p-4 rounded-xl border transition-all duration-200 cursor-pointer backdrop-blur-sm hover:scale-[1.02] ${"user"===n?"border-blue-500/50 bg-blue-600/20 shadow-lg shadow-blue-500/20":"border-blue-600/30 bg-blue-900/30 hover:border-blue-500/40 hover:bg-blue-800/40"}`,children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("input",{type:"radio",name:"downloadType",value:"user",checked:"user"===n,onChange:e=>l(e.target.value),className:"w-4 h-4 text-blue-600 bg-blue-900 border-blue-600 focus:ring-blue-500 focus:ring-2"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("svg",{className:"w-5 h-5 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),r.jsx("div",{className:"text-blue-100 font-semibold",children:"Apenas mensagens do usu\xe1rio"})]}),r.jsx("div",{className:"text-blue-300/70 text-sm mt-1",children:"Somente suas perguntas e coment\xe1rios"})]})]})}),r.jsx("label",{className:`flex items-center p-4 rounded-xl border transition-all duration-200 cursor-pointer backdrop-blur-sm hover:scale-[1.02] ${"ai"===n?"border-blue-500/50 bg-blue-600/20 shadow-lg shadow-blue-500/20":"border-blue-600/30 bg-blue-900/30 hover:border-blue-500/40 hover:bg-blue-800/40"}`,children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("input",{type:"radio",name:"downloadType",value:"ai",checked:"ai"===n,onChange:e=>l(e.target.value),className:"w-4 h-4 text-blue-600 bg-blue-900 border-blue-600 focus:ring-blue-500 focus:ring-2"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("svg",{className:"w-5 h-5 text-cyan-400",fill:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})}),r.jsx("div",{className:"text-blue-100 font-semibold",children:"Apenas mensagens da IA"})]}),r.jsx("div",{className:"text-blue-300/70 text-sm mt-1",children:"Somente as respostas da intelig\xeancia artificial"})]})]})})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[r.jsx("button",{onClick:t,className:"px-6 py-3 text-blue-300 hover:text-blue-100 transition-all duration-200 rounded-xl hover:bg-blue-800/30 font-medium",children:"Cancelar"}),r.jsx("button",{onClick:handleDownload,disabled:i,className:"px-8 py-3 bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-semibold shadow-lg hover:shadow-blue-500/30 hover:scale-105 disabled:hover:scale-100",children:i?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-3"}),"Gerando arquivo..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("svg",{className:"w-5 h-5 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Baixar HTML"]})})]})]})]})});return(0,D.createPortal)(u,document.body)},getUserAPIEndpoints=async e=>{try{let t=(0,d.collection)(i.db,"usuarios",e,"endpoints"),a=await (0,d.getDocs)(t);if(!a.empty){let e=[];return a.forEach(t=>{e.push(t.data())}),e.sort((e,t)=>t.createdAt-e.createdAt),e}let r=(0,d.doc)(i.db,"usuarios",e,"configuracoes","settings"),s=await (0,d.getDoc)(r);if(s.exists()){let e=s.data();if(e.endpoints){let t=[];return Object.entries(e.endpoints).forEach(([e,a])=>{t.push({id:`legacy_${e}`,name:a.nome||e,url:a.url||"",apiKey:a.apiKey||"",isActive:a.ativo||!1,createdAt:Date.now(),settings:{defaultModel:a.modeloPadrao}})}),t}}return[{id:"default_openrouter",name:"OpenRouter",url:"https://openrouter.ai/api/v1/chat/completions",apiKey:"",isActive:!1,createdAt:Date.now(),settings:{defaultModel:"meta-llama/llama-3.1-8b-instruct:free"}},{id:"default_deepseek",name:"DeepSeek",url:"https://api.deepseek.com/v1/chat/completions",apiKey:"",isActive:!1,createdAt:Date.now(),settings:{defaultModel:"deepseek-chat"}}]}catch(e){throw console.error("Error getting user API endpoints:",e),e}},A=new class{async fetchModels(){if(this.cache&&Date.now()-this.cache.timestamp<this.CACHE_DURATION)return this.cache.models;try{let e=await fetch("https://openrouter.ai/api/v1/models",{headers:{Authorization:"Bearer your-openrouter-api-key-here","Content-Type":"application/json"}});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);let t=await e.json(),a=t.data.map(e=>({id:e.id,name:e.name,description:e.description||"",context_length:e.context_length,pricing:{prompt:e.pricing.prompt,completion:e.pricing.completion,image:e.pricing.image},architecture:e.architecture,created:e.created,isFavorite:!1}));return this.cache={models:a,timestamp:Date.now()},a}catch(e){if(console.error("Error fetching OpenRouter models:",e),this.cache)return this.cache.models;throw e}}async fetchCredits(e){if(this.creditsCache&&Date.now()-this.creditsCache.timestamp<this.CREDITS_CACHE_DURATION){let e=this.creditsCache.credits.total_credits-this.creditsCache.credits.total_usage;return{balance:e}}try{let t=await fetch("https://openrouter.ai/api/v1/credits",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw Error(`HTTP error! status: ${t.status}`);let a=await t.json(),r=a.data;this.creditsCache={credits:r,timestamp:Date.now()};let s=r.total_credits-r.total_usage;return{balance:s}}catch(e){if(console.error("Error fetching OpenRouter credits:",e),this.creditsCache){let e=this.creditsCache.credits.total_credits-this.creditsCache.credits.total_usage;return{balance:e}}return{balance:0,error:e instanceof Error?e.message:"Erro desconhecido"}}}filterByCategory(e,t){switch(t){case"free":return e.filter(e=>this.isFreeModel(e));case"paid":return e.filter(e=>!this.isFreeModel(e));case"favorites":return e.filter(e=>e.isFavorite);default:return e}}sortModels(e,t){let a=[...e];switch(t){case"newest":return a.sort((e,t)=>(t.created||0)-(e.created||0));case"price_low":return a.sort((e,t)=>this.getTotalPrice(e)-this.getTotalPrice(t));case"price_high":return a.sort((e,t)=>this.getTotalPrice(t)-this.getTotalPrice(e));case"context_high":return a.sort((e,t)=>t.context_length-e.context_length);default:return a}}isFreeModel(e){let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return 0===t&&0===a}getTotalPrice(e){let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return t+a}formatPrice(e){let t=1e6*parseFloat(e);return 0===t?"Gr\xe1tis":t<.001?"< $0.001":`$${t.toFixed(3)}`}formatContextLength(e){return e.toLocaleString()}searchModels(e,t,a={}){let{fuzzyThreshold:r=.6,maxResults:s=50,boostFavorites:o=!1}=a;if(!t.trim())return[];let n=t.toLowerCase(),l=[];for(let t of e){let e=0,a=[],r=t.name,s=t.description||"";t.name.toLowerCase().includes(n)&&(e+=10,a.push("name"),r=this.highlightText(t.name,n)),t.id.toLowerCase().includes(n)&&(e+=7,a.push("id")),t.description&&t.description.toLowerCase().includes(n)&&(e+=3,a.push("description"),s=this.highlightText(t.description,n)),o&&t.isFavorite&&(e*=1.5),(n.includes("free")||n.includes("gr\xe1tis"))&&this.isFreeModel(t)&&(e+=5),(n.includes("expensive")||n.includes("caro"))&&this.getTotalPrice(t)>2e-5&&(e+=5),e>0&&l.push({model:t,score:e,matchedFields:a,highlightedName:r,highlightedDescription:s})}return l.sort((e,t)=>t.score-e.score).slice(0,s)}highlightText(e,t){let a=RegExp(`(${t})`,"gi");return e.replace(a,'<mark class="bg-yellow-300 text-black px-1 rounded">$1</mark>')}clearCache(){this.cache=null}constructor(){this.cache=null,this.creditsCache=null,this.CACHE_DURATION=3e5,this.CREDITS_CACHE_DURATION=3e4}},F=new class{async fetchModels(){if(this.cache&&Date.now()-this.cache.timestamp<this.CACHE_DURATION)return this.cache.models;try{let e=[{id:"deepseek-chat",name:"DeepSeek Chat",description:"Modelo de chat geral da DeepSeek, otimizado para conversas e tarefas diversas.",context_length:32768,pricing:{prompt:"0.00014",completion:"0.00028"},architecture:{input_modalities:["text"],output_modalities:["text"],tokenizer:"deepseek"},created:Date.now(),isFavorite:!1},{id:"deepseek-coder",name:"DeepSeek Coder",description:"Modelo especializado em programa\xe7\xe3o e desenvolvimento de c\xf3digo.",context_length:16384,pricing:{prompt:"0.00014",completion:"0.00028"},architecture:{input_modalities:["text"],output_modalities:["text"],tokenizer:"deepseek"},created:Date.now(),isFavorite:!1}];return this.cache={models:e,timestamp:Date.now()},e}catch(e){if(console.error("Error fetching DeepSeek models:",e),this.cache)return this.cache.models;throw e}}filterByCategory(e,t){switch(t){case"free":return e.filter(e=>this.isFreeModel(e));case"paid":return e.filter(e=>!this.isFreeModel(e));default:return e}}sortModels(e,t){let a=[...e];switch(t){case"newest":return a.sort((e,t)=>(t.created||0)-(e.created||0));case"price_low":return a.sort((e,t)=>this.getTotalPrice(e)-this.getTotalPrice(t));case"price_high":return a.sort((e,t)=>this.getTotalPrice(t)-this.getTotalPrice(e));case"context_high":return a.sort((e,t)=>t.context_length-e.context_length);default:return a}}isFreeModel(e){let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return 0===t&&0===a}getTotalPrice(e){let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return t+a}formatPrice(e){let t=1e6*parseFloat(e);return 0===t?"Gr\xe1tis":t<.001?"< $0.001":`$${t.toFixed(3)}`}formatContextLength(e){return e.toLocaleString()}searchModels(e,t,a={}){let{fuzzyThreshold:r=.6,maxResults:s=50,boostFavorites:o=!1}=a;if(!t.trim())return[];let n=t.toLowerCase(),l=[];for(let t of e){let e=0,a=[],r=t.name,s=t.description||"";t.name.toLowerCase().includes(n)&&(e+=10,a.push("name"),r=this.highlightText(t.name,n)),t.id.toLowerCase().includes(n)&&(e+=7,a.push("id")),t.description&&t.description.toLowerCase().includes(n)&&(e+=3,a.push("description"),s=this.highlightText(t.description,n)),o&&t.isFavorite&&(e*=1.5),(n.includes("free")||n.includes("gr\xe1tis"))&&this.isFreeModel(t)&&(e+=5),(n.includes("code")||n.includes("programming")||n.includes("coder"))&&t.id.includes("coder")&&(e+=5),e>0&&l.push({model:t,score:e,matchedFields:a,highlightedName:r,highlightedDescription:s})}return l.sort((e,t)=>t.score-e.score).slice(0,s)}highlightText(e,t){let a=RegExp(`(${t})`,"gi");return e.replace(a,'<mark class="bg-yellow-300 text-black px-1 rounded">$1</mark>')}clearCache(){this.cache=null}constructor(){this.cache=null,this.CACHE_DURATION=3e5}},I=new class{escapeModelId(e){return e.replace(/\//g,"_SLASH_").replace(/\./g,"_DOT_")}unescapeModelId(e){return e.replace(/_SLASH_/g,"/").replace(/_DOT_/g,".")}async addToFavorites(e,t,a,r){try{let s=this.escapeModelId(a),o=(0,d.doc)(i.db,"usuarios",e,"endpoints",t,"modelos_favoritos",s),n={modelId:a,modelName:r,endpointId:t,addedAt:Date.now()};await (0,d.pl)(o,n),console.log("Model added to favorites successfully")}catch(e){throw console.error("Error adding model to favorites:",e),e}}async removeFromFavorites(e,t,a){try{let r=this.escapeModelId(a),s=(0,d.doc)(i.db,"usuarios",e,"endpoints",t,"modelos_favoritos",r);await (0,d.oe)(s),console.log("Model removed from favorites successfully")}catch(e){throw console.error("Error removing model from favorites:",e),e}}async isModelFavorited(e,t,a){try{let r=this.escapeModelId(a),s=(0,d.doc)(i.db,"usuarios",e,"endpoints",t,"modelos_favoritos",r),o=await (0,d.getDoc)(s);return o.exists()}catch(e){return console.error("Error checking if model is favorited:",e),!1}}async toggleFavorite(e,t,a,r){try{let s=await this.isModelFavorited(e,t,a);if(s)return await this.removeFromFavorites(e,t,a),!1;return await this.addToFavorites(e,t,a,r),!0}catch(e){throw console.error("Error toggling favorite:",e),e}}async getFavoriteModels(e,t){try{let a=(0,d.collection)(i.db,"usuarios",e,"endpoints",t,"modelos_favoritos"),r=await (0,d.getDocs)(a),s=[];return r.forEach(e=>{let t=e.data();t.modelId=this.unescapeModelId(t.modelId),s.push(t)}),s.sort((e,t)=>t.addedAt-e.addedAt),s}catch(e){throw console.error("Error getting favorite models:",e),e}}async getFavoriteModelIds(e,t){try{let a=await this.getFavoriteModels(e,t);return new Set(a.map(e=>e.modelId))}catch(e){return console.error("Error getting favorite model IDs:",e),new Set}}async getFavoritesStats(e,t){try{let a=await this.getFavoriteModels(e,t);if(0===a.length)return{totalFavorites:0};let r=[...a].sort((e,t)=>t.addedAt-e.addedAt);return{totalFavorites:a.length,mostRecentlyAdded:r[0],oldestFavorite:r[r.length-1]}}catch(e){return console.error("Error getting favorites stats:",e),{totalFavorites:0}}}async clearAllFavorites(e,t){try{let a=await this.getFavoriteModels(e,t),r=a.map(a=>this.removeFromFavorites(e,t,a.modelId));await Promise.all(r),console.log("All favorites cleared successfully")}catch(e){throw console.error("Error clearing all favorites:",e),e}}async exportFavorites(e,t){try{return await this.getFavoriteModels(e,t)}catch(e){throw console.error("Error exporting favorites:",e),e}}async importFavorites(e,t,a){try{let r=a.map(a=>this.addToFavorites(e,t,a.modelId,a.modelName));await Promise.all(r),console.log("Favorites imported successfully")}catch(e){throw console.error("Error importing favorites:",e),e}}},T=new class{async trackSearchTerm(e,t){if(t.trim()&&e)try{let a=t.toLowerCase().trim();await this.updateGlobalSearchStats(a),await this.updateUserSearchHistory(e,a)}catch(e){console.error("Error tracking search term:",e)}}async trackModelSelection(e,t,a,r){if(e&&t)try{let s={modelId:t,modelName:a,searchTerm:r?.trim()||void 0,timestamp:Date.now(),userId:e},o=(0,d.doc)((0,d.collection)(i.db,"search_analytics","model_selections","events"));await (0,d.pl)(o,s),await this.updateModelStats(t,a)}catch(e){console.error("Error tracking model selection:",e)}}async updateGlobalSearchStats(e){try{let t=(0,d.doc)(i.db,"search_analytics","global","terms",e),a=await (0,d.getDoc)(t);a.exists()?await (0,d.r7)(t,{count:(0,d.nP)(1),lastUsed:Date.now()}):await (0,d.pl)(t,{term:e,count:1,firstUsed:Date.now(),lastUsed:Date.now()})}catch(e){console.error("Error updating global search stats:",e)}}async updateUserSearchHistory(e,t){try{let a=(0,d.doc)(i.db,"search_analytics","users",e),r=await (0,d.getDoc)(a);if(r.exists()){let e=r.data(),s=e.searches.find(e=>e.term===t);s?(s.count++,s.lastUsed=Date.now()):e.searches.push({term:t,count:1,firstUsed:Date.now(),lastUsed:Date.now()}),e.searches=e.searches.sort((e,t)=>t.lastUsed-e.lastUsed).slice(0,50),await (0,d.r7)(a,{searches:e.searches,totalSearches:(0,d.nP)(1),lastSearchAt:Date.now()})}else{let r={userId:e,searches:[{term:t,count:1,firstUsed:Date.now(),lastUsed:Date.now()}],totalSearches:1,lastSearchAt:Date.now()};await (0,d.pl)(a,r)}}catch(e){console.error("Error updating user search history:",e)}}async updateModelStats(e,t){try{let a=(0,d.doc)(i.db,"search_analytics","models",e),r=await (0,d.getDoc)(a);r.exists()?await (0,d.r7)(a,{selectionCount:(0,d.nP)(1),lastSelected:Date.now()}):await (0,d.pl)(a,{modelId:e,modelName:t,selectionCount:1,firstSelected:Date.now(),lastSelected:Date.now()})}catch(e){console.error("Error updating model stats:",e)}}async getPopularSearchTerms(e=10){try{let t=(0,d.collection)(i.db,"search_analytics","global","terms"),a=(0,d.query)(t,(0,d.Xo)("count","desc"),(0,d.b9)(e)),r=await (0,d.getDocs)(a),s=[];return r.forEach(e=>{s.push(e.data())}),s}catch(e){return console.error("Error getting popular search terms:",e),[]}}async getUserSearchHistory(e){try{let t=(0,d.doc)(i.db,"search_analytics","users",e),a=await (0,d.getDoc)(t);if(a.exists()){let e=a.data();return e.searches.sort((e,t)=>t.lastUsed-e.lastUsed)}return[]}catch(e){return console.error("Error getting user search history:",e),[]}}async getSearchSuggestions(e,t,a=5){try{let r=await this.getUserSearchHistory(e),s=await this.getPopularSearchTerms(20),o=[...r.map(e=>e.term),...s.map(e=>e.term)],n=o.filter(e=>e.toLowerCase().startsWith(t.toLowerCase())&&e.toLowerCase()!==t.toLowerCase()).slice(0,a);return Array.from(new Set(n))}catch(e){return console.error("Error getting search suggestions:",e),[]}}async getUserAnalytics(e){try{let t=await this.getUserSearchHistory(e);if(0===t.length)return{totalSearches:0,uniqueTerms:0,recentSearches:[]};let a=t.reduce((e,t)=>e+t.count,0),r=t.reduce((e,t)=>t.count>e.count?t:e);return{totalSearches:a,uniqueTerms:t.length,mostUsedTerm:r,recentSearches:t.slice(0,10),lastSearchAt:Math.max(...t.map(e=>e.lastUsed))}}catch(e){return console.error("Error getting user analytics:",e),{totalSearches:0,uniqueTerms:0,recentSearches:[]}}}async clearUserSearchHistory(e){try{let t=(0,d.doc)(i.db,"search_analytics","users",e);await (0,d.pl)(t,{userId:e,searches:[],totalSearches:0,lastSearchAt:Date.now()})}catch(e){throw console.error("Error clearing user search history:",e),e}}};function highlightText(e,t){let a=RegExp(`(${t})`,"gi");return e.replace(a,'<mark class="bg-yellow-300 text-black px-1 rounded">$1</mark>')}let P=new class{getSmartCategories(){return this.smartCategories}getModelsByCategory(e,t){let a=this.smartCategories.find(e=>e.id===t);return a?e.filter(a.filter):e}getCategoryStats(e){let t=e.length;return this.smartCategories.map(a=>{let r=e.filter(a.filter);return{category:a,count:r.length,percentage:t>0?r.length/t*100:0}})}filterByPriceRange(e,t,a){return e.filter(e=>{let r=parseFloat(e.pricing.prompt),s=parseFloat(e.pricing.completion),o=r+s;return o>=t&&o<=a})}filterByContextRange(e,t,a){return e.filter(e=>e.context_length>=t&&e.context_length<=a)}filterByInputModalities(e,t){return e.filter(e=>!!e.architecture?.input_modalities&&t.every(t=>e.architecture.input_modalities.includes(t)))}filterByKeywords(e,t){return e.filter(e=>{let a=`${e.name} ${e.description||""} ${e.id}`.toLowerCase();return t.some(e=>a.includes(e.toLowerCase()))})}applyAdvancedFilters(e,t){let a=[...e];return t.categories&&t.categories.length>0&&(a=a.filter(e=>t.categories.some(t=>{let a=this.smartCategories.find(e=>e.id===t);return!!a&&a.filter(e)}))),t.priceRange&&(a=this.filterByPriceRange(a,t.priceRange.min,t.priceRange.max)),t.contextRange&&(a=this.filterByContextRange(a,t.contextRange.min,t.contextRange.max)),t.inputModalities&&t.inputModalities.length>0&&(a=this.filterByInputModalities(a,t.inputModalities)),t.keywords&&t.keywords.length>0&&(a=this.filterByKeywords(a,t.keywords)),t.onlyFavorites&&(a=a.filter(e=>e.isFavorite)),a}getSuggestedFilters(e){let t=this.getCategoryStats(e),a=t.filter(e=>e.count>0).sort((e,t)=>t.count-e.count).slice(0,5),r=[{label:"Gratuito",min:0,max:0},{label:"Muito Barato (< $1/1M)",min:1e-9,max:1e-6},{label:"Barato ($1-10/1M)",min:1e-6,max:1e-5},{label:"M\xe9dio ($10-100/1M)",min:1e-5,max:1e-4},{label:"Caro (> $100/1M)",min:1e-4,max:1/0}].map(t=>({...t,count:this.filterByPriceRange(e,t.min,t.max).length})),s=[{label:"Pequeno (< 8K)",min:0,max:8e3},{label:"M\xe9dio (8K - 32K)",min:8e3,max:32e3},{label:"Grande (32K - 128K)",min:32e3,max:128e3},{label:"Muito Grande (> 128K)",min:128e3,max:1/0}].map(t=>({...t,count:this.filterByContextRange(e,t.min,t.max).length}));return{popularCategories:a,priceRanges:r,contextRanges:s}}constructor(){this.smartCategories=[{id:"vision",name:"Vis\xe3o",description:"Modelos que processam imagens",icon:"\uD83D\uDC41️",filter:e=>!!(e.architecture?.input_modalities?.includes("image")||e.name.toLowerCase().includes("vision")||e.description?.toLowerCase().includes("vision")||e.description?.toLowerCase().includes("image"))},{id:"coding",name:"C\xf3digo",description:"Modelos especializados em programa\xe7\xe3o",icon:"\uD83D\uDCBB",filter:e=>["code","coding","programming","developer","coder"].some(t=>e.name.toLowerCase().includes(t)||!!e.description?.toLowerCase().includes(t))},{id:"reasoning",name:"Racioc\xednio",description:"Modelos otimizados para racioc\xednio l\xf3gico",icon:"\uD83E\uDDE0",filter:e=>["reasoning","logic","math","analysis","thinking"].some(t=>e.name.toLowerCase().includes(t)||!!e.description?.toLowerCase().includes(t))},{id:"creative",name:"Criativo",description:"Modelos para tarefas criativas",icon:"\uD83C\uDFA8",filter:e=>["creative","writing","story","art","creative"].some(t=>e.name.toLowerCase().includes(t)||!!e.description?.toLowerCase().includes(t))},{id:"fast",name:"R\xe1pido",description:"Modelos otimizados para velocidade",icon:"⚡",filter:e=>["fast","quick","speed","turbo","instant"].some(t=>e.name.toLowerCase().includes(t)||!!e.description?.toLowerCase().includes(t))},{id:"large_context",name:"Grande Contexto",description:"Modelos com contexto extenso (>32K)",icon:"\uD83D\uDCDA",filter:e=>e.context_length>32e3},{id:"cheap",name:"Econ\xf4mico",description:"Modelos com pre\xe7os baixos",icon:"\uD83D\uDCB0",filter:e=>{let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return t+a<1e-6}},{id:"premium",name:"Premium",description:"Modelos de alta qualidade",icon:"⭐",filter:e=>["gpt-4","claude-3","premium","pro","advanced"].some(t=>e.name.toLowerCase().includes(t)||e.id.toLowerCase().includes(t))}]}},HighlightedText=({text:e,highlight:t})=>{if(!t.trim())return r.jsx("span",{children:e});let a=RegExp(`(${t})`,"gi"),s=e.split(a);return r.jsx("span",{children:s.map((e,t)=>a.test(e)?r.jsx("mark",{className:"bg-yellow-300 text-black px-1 rounded",children:e},t):r.jsx("span",{children:e},t))})},components_AdvancedSearchInput=({value:e,onChange:t,suggestions:a=[],isSearching:o=!1,placeholder:n="Buscar...",showSuggestions:l=!0,onSuggestionSelect:i,className:d=""})=>{let[c,m]=(0,s.useState)(!1),[u,h]=(0,s.useState)(-1),x=(0,s.useRef)(null),p=(0,s.useRef)(null);(0,s.useEffect)(()=>{l&&a.length>0&&e.trim()?m(!0):m(!1)},[a,e,l]),(0,s.useEffect)(()=>{let handleClickOutside=e=>{x.current&&p.current&&!x.current.contains(e.target)&&!p.current.contains(e.target)&&(m(!1),h(-1))};return document.addEventListener("mousedown",handleClickOutside),()=>{document.removeEventListener("mousedown",handleClickOutside)}},[]);let handleSuggestionClick=e=>{t(e),i?.(e),m(!1),h(-1),x.current?.focus()};return(0,r.jsxs)("div",{className:`relative ${d}`,children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o?r.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-400"}):r.jsx("svg",{className:"w-4 h-4 text-blue-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),r.jsx("input",{ref:x,type:"text",value:e,onChange:e=>{let a=e.target.value;t(a),h(-1)},onKeyDown:e=>{if(c&&0!==a.length)switch(e.key){case"ArrowDown":e.preventDefault(),h(e=>e<a.length-1?e+1:0);break;case"ArrowUp":e.preventDefault(),h(e=>e>0?e-1:a.length-1);break;case"Enter":if(e.preventDefault(),u>=0){let e=a[u];t(e),i?.(e),m(!1),h(-1)}break;case"Escape":m(!1),h(-1),x.current?.blur()}},onFocus:()=>{l&&a.length>0&&e.trim()&&m(!0)},placeholder:n,className:"w-full pl-10 pr-10 py-3 bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl text-blue-100 placeholder:text-blue-300/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200"}),e&&r.jsx("button",{onClick:()=>{t(""),m(!1),h(-1),x.current?.focus()},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-blue-300 hover:text-blue-200 transition-colors duration-200",children:r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),c&&a.length>0&&r.jsx("div",{ref:p,className:"absolute z-50 w-full mt-1 bg-blue-900/95 backdrop-blur-sm border border-blue-600/30 rounded-xl shadow-2xl max-h-60 overflow-y-auto",children:a.map((e,t)=>r.jsx("button",{onClick:()=>handleSuggestionClick(e),className:`w-full text-left px-4 py-3 text-sm transition-all duration-200 first:rounded-t-xl last:rounded-b-xl ${t===u?"bg-blue-600/50 text-blue-100":"text-blue-200 hover:bg-blue-800/50 hover:text-blue-100"}`,children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("svg",{className:"w-3 h-3 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),r.jsx("span",{children:e})]})},e))})]})},dashboard_ExpensiveModelConfirmationModal=({isOpen:e,model:t,onConfirm:a,onCancel:s})=>{if(!e||!t)return null;let o=parseFloat(t.pricing.prompt),n=parseFloat(t.pricing.completion),l=o+n,formatPrice=e=>{let t=1e6*e;return 0===t?"Gr\xe1tis":t<.001?"< $0.001":`$${t.toFixed(3)}`};return r.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center transition-opacity duration-300 z-[60]",children:(0,r.jsxs)("div",{className:"bg-gradient-to-br from-amber-950/95 via-orange-900/95 to-red-950/95 backdrop-blur-xl rounded-2xl border border-amber-600/40 shadow-2xl w-full max-w-md mx-4 overflow-hidden relative",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-amber-500/10 via-transparent to-red-500/10 pointer-events-none rounded-2xl"}),r.jsx("div",{className:"p-6 border-b border-amber-700/30 relative z-10",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("div",{className:"w-12 h-12 rounded-full bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center flex-shrink-0",children:r.jsx("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h2",{className:"text-xl font-semibold text-amber-100",children:"Modelo Caro Detectado"}),r.jsx("p",{className:"text-amber-200/70 text-sm mt-1",children:"Este modelo tem custos elevados"})]})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-6 relative z-10",children:[(0,r.jsxs)("div",{className:"bg-amber-900/30 backdrop-blur-sm rounded-xl p-4 border border-amber-600/30",children:[r.jsx("h3",{className:"font-semibold text-amber-100 mb-2",children:t.name}),r.jsx("p",{className:"text-amber-200/80 text-sm mb-3",children:t.description}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,r.jsxs)("div",{className:"bg-amber-800/30 rounded-lg p-3 border border-amber-600/20",children:[r.jsx("span",{className:"block text-xs text-amber-300/70 font-medium mb-1",children:"Input"}),(0,r.jsxs)("span",{className:"text-amber-200 font-semibold",children:[formatPrice(o),"/1M"]})]}),(0,r.jsxs)("div",{className:"bg-amber-800/30 rounded-lg p-3 border border-amber-600/20",children:[r.jsx("span",{className:"block text-xs text-amber-300/70 font-medium mb-1",children:"Output"}),(0,r.jsxs)("span",{className:"text-amber-200 font-semibold",children:[formatPrice(n),"/1M"]})]})]}),r.jsx("div",{className:"mt-4 p-3 bg-gradient-to-r from-red-900/40 to-orange-900/40 rounded-lg border border-red-600/30",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-red-200 font-medium",children:"Custo Total por 1M tokens:"}),r.jsx("span",{className:"text-red-100 font-bold text-lg",children:formatPrice(l)})]})})]}),r.jsx("div",{className:"bg-red-900/30 backdrop-blur-sm rounded-xl p-4 border border-red-600/30",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[r.jsx("div",{className:"w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center flex-shrink-0 mt-0.5",children:r.jsx("svg",{className:"w-4 h-4 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h4",{className:"text-red-200 font-medium mb-2",children:"⚠️ Aten\xe7\xe3o aos Custos"}),(0,r.jsxs)("ul",{className:"text-red-300/80 text-sm space-y-1",children:[r.jsx("li",{children:"• Este modelo tem custos superiores a $20 por milh\xe3o de tokens"}),r.jsx("li",{children:"• Conversas longas podem gerar custos significativos"}),r.jsx("li",{children:"• Monitore seu uso para evitar surpresas na fatura"})]})]})]})}),(0,r.jsxs)("div",{className:"bg-blue-900/30 backdrop-blur-sm rounded-xl p-4 border border-blue-600/30",children:[r.jsx("h4",{className:"text-blue-200 font-medium mb-3",children:"\uD83D\uDCA1 Estimativa de Custos"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between text-blue-300/80",children:[r.jsx("span",{children:"Mensagem curta (~100 tokens):"}),r.jsx("span",{className:"font-medium",children:formatPrice(1e-4*l)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-blue-300/80",children:[r.jsx("span",{children:"Mensagem m\xe9dia (~500 tokens):"}),r.jsx("span",{className:"font-medium",children:formatPrice(5e-4*l)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-blue-300/80",children:[r.jsx("span",{children:"Mensagem longa (~2000 tokens):"}),r.jsx("span",{className:"font-medium",children:formatPrice(.002*l)})]})]})]})]}),(0,r.jsxs)("div",{className:"p-6 border-t border-amber-700/30 flex space-x-3 relative z-10",children:[r.jsx("button",{onClick:s,className:"flex-1 px-6 py-3 bg-gray-700/50 hover:bg-gray-600/50 backdrop-blur-sm border border-gray-600/30 hover:border-gray-500/50 rounded-xl text-gray-200 hover:text-gray-100 transition-all duration-200 font-medium",children:"Cancelar"}),r.jsx("button",{onClick:a,className:"flex-1 px-6 py-3 bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-500 hover:to-orange-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 shadow-lg hover:shadow-amber-500/30",children:"Usar Mesmo Assim"})]})]})})},ModelCard=({model:e,isSelected:t,onSelect:a,onToggleFavorite:s,isToggling:o=!1,service:n=A,searchTerm:l="",searchResult:i})=>{let d=n===A&&A.getTotalPrice(e)>2e-5;return(0,r.jsxs)("div",{"data-model-id":e.id,className:`p-5 rounded-xl border transition-all duration-200 backdrop-blur-sm hover:scale-[1.02] relative ${t?"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20":"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40"}`,children:[d&&r.jsx("div",{className:"absolute -top-2 -right-2 z-10",children:r.jsx("div",{className:"bg-gradient-to-r from-amber-500 to-orange-500 rounded-full p-1.5 shadow-lg border-2 border-slate-800",children:r.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})})})}),(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("h3",{className:"font-semibold text-blue-100 truncate",children:i?.highlightedName?r.jsx("span",{dangerouslySetInnerHTML:{__html:i.highlightedName}}):l?r.jsx(HighlightedText,{text:e.name,highlight:l}):e.name}),d&&r.jsx("span",{className:"text-xs bg-amber-500/20 text-amber-300 px-2 py-0.5 rounded-full border border-amber-500/30 font-medium",children:"CARO"}),i&&i.matchedFields.length>0&&r.jsx("div",{className:"flex space-x-1",children:i.matchedFields.slice(0,3).map(e=>r.jsx("span",{className:"text-xs bg-green-500/20 text-green-300 px-1.5 py-0.5 rounded border border-green-500/30",title:`Encontrado em: ${e}`,children:"name"===e?"\uD83D\uDCDD":"description"===e?"\uD83D\uDCC4":"provider"===e?"\uD83C\uDFE2":"tags"===e?"\uD83C\uDFF7️":"\uD83D\uDD0D"},e))})]}),r.jsx("p",{className:"text-xs text-blue-300/70 truncate mt-1 font-mono",children:r.jsx(HighlightedText,{text:e.id,highlight:l})})]}),n.isFreeModel(e)&&r.jsx("span",{className:"px-3 py-1 bg-green-600/30 text-green-300 text-xs rounded-full border border-green-500/30 font-medium",children:"Gr\xe1tis"})]}),e.description&&r.jsx("div",{className:"mt-3 mb-3",children:r.jsx("p",{className:"text-sm text-blue-300/80 line-clamp-2",children:i?.highlightedDescription?r.jsx("span",{dangerouslySetInnerHTML:{__html:i.highlightedDescription}}):l?r.jsx(HighlightedText,{text:e.description,highlight:l}):e.description})}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20",children:[r.jsx("span",{className:"block text-xs text-blue-300/70 font-medium mb-1",children:"Contexto"}),r.jsx("span",{className:"text-blue-200 font-semibold",children:n.formatContextLength(e.context_length)})]}),(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20",children:[r.jsx("span",{className:"block text-xs text-blue-300/70 font-medium mb-1",children:"Input"}),(0,r.jsxs)("span",{className:"text-blue-200 font-semibold",children:[n.formatPrice(e.pricing.prompt),"/1M"]})]}),(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20",children:[r.jsx("span",{className:"block text-xs text-blue-300/70 font-medium mb-1",children:"Output"}),(0,r.jsxs)("span",{className:"text-blue-200 font-semibold",children:[n.formatPrice(e.pricing.completion),"/1M"]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[r.jsx("button",{onClick:s,disabled:o,className:`p-2.5 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed ${e.isFavorite?"text-yellow-400 hover:text-yellow-300 bg-yellow-500/20 border border-yellow-500/30":"text-blue-300 hover:text-yellow-400 bg-blue-800/30 border border-blue-600/20 hover:bg-yellow-500/20 hover:border-yellow-500/30"}`,title:o?"Processando...":e.isFavorite?"Remover dos favoritos":"Adicionar aos favoritos",children:o?r.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-current"}):r.jsx("svg",{className:"w-5 h-5",fill:e.isFavorite?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"})})}),r.jsx("button",{onClick:a,className:"px-6 py-2.5 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 shadow-lg hover:shadow-blue-500/30",children:"Selecionar"})]})]})]})},DeepSeekModelCard=({model:e,isSelected:t,onSelect:a})=>(0,r.jsxs)("div",{className:`relative p-6 rounded-2xl border transition-all duration-300 cursor-pointer group backdrop-blur-sm hover:scale-[1.02] ${t?"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20":"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40"}`,onClick:a,children:[r.jsx("div",{className:"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-br from-blue-600/10 to-cyan-600/10"}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[r.jsx("div",{className:"w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center",children:"deepseek-chat"===e.id?r.jsx("svg",{className:"w-8 h-8 text-blue-400",fill:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})}):r.jsx("svg",{className:"w-8 h-8 text-cyan-400",fill:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h3",{className:"font-semibold text-blue-100 text-lg",children:e.name}),r.jsx("p",{className:"text-sm text-blue-300/70 mt-1",children:e.description})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center",children:[r.jsx("div",{className:"text-xs text-blue-300/70 font-medium mb-1",children:"Contexto"}),r.jsx("div",{className:"text-sm font-semibold text-blue-200",children:F.formatContextLength(e.context_length)})]}),(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center",children:[r.jsx("div",{className:"text-xs text-blue-300/70 font-medium mb-1",children:"Input"}),(0,r.jsxs)("div",{className:"text-sm font-semibold text-blue-200",children:[F.formatPrice(e.pricing.prompt),"/1M"]})]}),(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center",children:[r.jsx("div",{className:"text-xs text-blue-300/70 font-medium mb-1",children:"Output"}),(0,r.jsxs)("div",{className:"text-sm font-semibold text-blue-200",children:[F.formatPrice(e.pricing.completion),"/1M"]})]})]}),r.jsx("button",{onClick:e=>{e.stopPropagation(),a()},className:`w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 hover:scale-105 ${t?"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg shadow-blue-500/30":"bg-blue-800/40 text-blue-200 hover:bg-gradient-to-r hover:from-blue-600 hover:to-cyan-600 hover:text-white border border-blue-600/30"}`,children:t?"Selecionado":"Selecionar Modelo"})]})]}),dashboard_ModelSelectionModal=({isOpen:e,onClose:t,currentModel:o,onModelSelect:l})=>{let{user:d}=(0,n.useAuth)(),[c,m]=(0,s.useState)([]),[u,h]=(0,s.useState)(null),[x,p]=(0,s.useState)([]),[g,b]=(0,s.useState)(new Set),[f,v]=(0,s.useState)(!1),[w,j]=(0,s.useState)(null),[y,N]=(0,s.useState)(4),[k,C]=(0,s.useState)(""),[M,S]=(0,s.useState)(!1),[D,E]=(0,s.useState)(null),[L,z]=(0,s.useState)(null),[B,$]=(0,s.useState)([]),[R,W]=(0,s.useState)(new Set),[O,U]=(0,s.useState)(null),[_,H]=(0,s.useState)(new Map),[q,V]=(0,s.useState)(null),[K,G]=(0,s.useState)({category:"paid",sortBy:"newest",searchTerm:""}),{searchTerm:Z,setSearchTerm:X,searchResults:J,suggestions:Y,isSearching:Q,hasSearched:ee,clearSearch:et,trackModelSelection:ea}=function(e,t={}){let{debounceMs:a=300,enableSuggestions:r=!0,cacheResults:o=!0,fuzzyThreshold:n=.6,maxResults:l=50,boostFavorites:i=!1,userId:d=null,trackAnalytics:c=!0}=t,[m,u]=(0,s.useState)(""),[h,x]=(0,s.useState)([]),[p,g]=(0,s.useState)([]),[b,f]=(0,s.useState)(!1),[v,w]=(0,s.useState)(!1),[j,y]=(0,s.useState)(null),[N]=(0,s.useState)(new Map),k=(0,s.useCallback)(async t=>{if(!t.trim()){x([]),w(!1);return}f(!0);try{if(o&&N.has(t)){x(N.get(t)),w(!0),f(!1);return}let a=function(e,t,a={}){let{fuzzyThreshold:r=.6,maxResults:s=50,boostFavorites:o=!1}=a;if(!t.trim())return[];let n=t.toLowerCase(),l=[];for(let t of e){let e=0,a=[],r=t.name,s=t.description||"";t.name.toLowerCase().includes(n)&&(e+=10,a.push("name"),r=highlightText(t.name,n)),t.id.toLowerCase().includes(n)&&(e+=7,a.push("id")),t.description&&t.description.toLowerCase().includes(n)&&(e+=3,a.push("description"),s=highlightText(t.description,n)),o&&t.isFavorite&&(e*=1.5),(n.includes("free")||n.includes("gr\xe1tis"))&&function(e){let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return 0===t&&0===a}(t)&&(e+=5),(n.includes("expensive")||n.includes("caro"))&&function(e){let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return t+a}(t)>2e-5&&(e+=5),e>0&&l.push({model:t,score:e,matchedFields:a,highlightedName:r,highlightedDescription:s})}return l.sort((e,t)=>t.score-e.score).slice(0,s)}(e,t,{fuzzyThreshold:n,maxResults:l,boostFavorites:i});o&&N.set(t,a),x(a),w(!0),c&&d&&await T.trackSearchTerm(d,t)}catch(e){console.error("Error performing search:",e),x([])}finally{f(!1)}},[e,n,l,i,o,N,c,d]),C=(0,s.useCallback)(e=>{u(e),j&&clearTimeout(j);let t=setTimeout(()=>{k(e)},a);y(t)},[j,a,k]);(0,s.useEffect)(()=>{r&&m.length>0&&d?T.getSearchSuggestions(d,m,5).then(g).catch(e=>{console.error("Error loading suggestions:",e),g([])}):g([])},[m,r,d]);let M=(0,s.useCallback)(()=>{u(""),x([]),w(!1),g([]),j&&(clearTimeout(j),y(null))},[j]),S=(0,s.useCallback)(async t=>{if(c&&d){let a=e.find(e=>e.id===t);a&&await T.trackModelSelection(d,t,a.name,v?m:void 0)}},[c,d,e,v,m]);return(0,s.useEffect)(()=>()=>{j&&clearTimeout(j)},[j]),{searchTerm:m,setSearchTerm:C,searchResults:h,suggestions:p,isSearching:b,hasSearched:v,clearSearch:M,trackModelSelection:S}}(x,{debounceMs:300,enableSuggestions:!1,cacheResults:!0,fuzzyThreshold:.6,maxResults:50,boostFavorites:!0,userId:d?.email||null,trackAnalytics:!0});(0,s.useEffect)(()=>{if(d&&e){if(q&&Date.now()-q.timestamp<6e5){if(m(q.endpoints),!u&&q.endpoints.length>0){let e=q.endpoints.find(e=>"OpenRouter"===e.name),t=q.endpoints.find(e=>"DeepSeek"===e.name);e?h(e):t?h(t):h(q.endpoints[0])}}else loadEndpoints()}},[d,e]);let[er,es]=(0,s.useState)(null);(0,s.useEffect)(()=>{if(e&&u&&"OpenRouter"===u.name&&d&&x.length>0){let e=`${u.id}_${u.name}`;if(er!==e){console.log("Loading favorites for endpoint:",e),es(e);let t=setTimeout(()=>{updateFavoritesFromFirestore()},100);return()=>clearTimeout(t)}}},[e,u,d,x.length,er]),(0,s.useEffect)(()=>{if(u){let e=`${u.id}_${u.name}`;er&&er!==e&&es(null)}},[u]),(0,s.useEffect)(()=>{if(u){let e=`${u.id}_${u.name}`,t=_.get(e);if(t&&Date.now()-t.timestamp<3e5){p(t.models),U(u.id);let e=new Set(t.models.filter(e=>e.isFavorite).map(e=>e.id));b(e)}else O===u.id&&t||("OpenRouter"===u.name?loadOpenRouterModels():"DeepSeek"===u.name&&loadDeepSeekModels())}},[u,O,_]);let updateFavoritesFromFirestore=async()=>{if(u&&d&&"OpenRouter"===u.name)try{let e=await getUsernameFromFirestore(),t=await I.getFavoriteModelIds(e,u.id),a=Array.from(g),r=Array.from(t),s=a.length!==r.length||!a.every(e=>t.has(e));s&&console.log("Updating favorites from Firestore:",r),b(t),p(e=>{let a=e.map(e=>({...e,isFavorite:t.has(e.id)}));if(s){let e=a.filter(e=>e.isFavorite);console.log("Models updated with favorites:",e.map(e=>e.id))}return a});let o=`${u.id}_${u.name}`;H(e=>{let a=e.get(o);if(a){let r=a.models.map(e=>({...e,isFavorite:t.has(e.id)}));return new Map(e).set(o,{models:r,timestamp:a.timestamp})}return e})}catch(e){console.error("Error updating favorites from Firestore:",e)}};(0,s.useEffect)(()=>{$(P.getSmartCategories())},[]);let getUsernameFromFirestore=async()=>{if(!d?.email)return"unknown";try{let{collection:e,query:t,where:r,getDocs:s}=await Promise.resolve().then(a.bind(a,29904)),o=e(i.db,"usuarios"),n=t(o,r("email","==",d.email)),l=await s(n);if(!l.empty){let e=l.docs[0];return e.data().username||e.id}return"unknown"}catch(e){return console.error("Erro ao buscar username:",e),"unknown"}},loadEndpoints=async()=>{if(!d){console.log("No user found");return}v(!0),j(null);try{let e=await getUsernameFromFirestore(),t=await getUserAPIEndpoints(e);V({endpoints:t,timestamp:Date.now()}),m(t);let a=t.find(e=>"OpenRouter"===e.name),r=t.find(e=>"DeepSeek"===e.name);a?h(a):r?h(r):t.length>0&&h(t[0])}catch(e){console.error("Error loading endpoints:",e),j("Erro ao carregar endpoints: "+e.message)}finally{v(!1)}},loadOpenRouterModels=async()=>{if(u&&d){v(!0),j(null);try{let e=await A.fetchModels(),t=await getUsernameFromFirestore(),a=await I.getFavoriteModelIds(t,u.id);a.size>0&&console.log("Loaded favorite IDs:",Array.from(a));let r=e.map(e=>({...e,isFavorite:a.has(e.id)})),s=`${u.id}_${u.name}`;H(e=>new Map(e).set(s,{models:r,timestamp:Date.now()})),p(r),b(a),U(u.id);let o=r.filter(e=>e.isFavorite).length;o>0&&console.log("Models loaded with favorites:",o,"favorites found")}catch(e){console.error("Error loading models:",e),j("Erro ao carregar modelos")}finally{v(!1)}}},loadDeepSeekModels=async()=>{if(u&&d){v(!0),j(null);try{let e=await F.fetchModels(),t=`${u.id}_${u.name}`;H(a=>new Map(a).set(t,{models:e,timestamp:Date.now()})),p(e),U(u.id)}catch(e){console.error("Error loading DeepSeek models:",e),j("Erro ao carregar modelos DeepSeek")}finally{v(!1)}}},eo=(()=>{let e=[...x];if(e=u?.name==="DeepSeek"?"favorites"===K.category?[]:F.filterByCategory(e,K.category):A.filterByCategory(e,K.category),ee&&Z.trim()){let t=J.map(e=>e.model);e=t.filter(t=>e.some(e=>e.id===t.id))}else if(L){let t=P.getModelsByCategory(e,L);e=t}if(!ee||!Z.trim()){let t=u?.name==="DeepSeek"?F:A;e=t.sortModels(e,K.sortBy)}return e})(),handleToggleFavorite=async e=>{if(!d||!u)return;if(R.has(e.id)){console.log("Already toggling favorite for model:",e.id);return}console.log(`Toggling favorite: ${e.id} (${e.isFavorite?"removing":"adding"})`);let t=!e.isFavorite;try{W(t=>new Set(t).add(e.id));let a=new Set(g);if(t?a.add(e.id):a.delete(e.id),b(a),p(a=>a.map(a=>a.id===e.id?{...a,isFavorite:t}:a)),u){let a=`${u.id}_${u.name}`;H(r=>{let s=r.get(a);if(s){let o=s.models.map(a=>a.id===e.id?{...a,isFavorite:t}:a);return new Map(r).set(a,{models:o,timestamp:s.timestamp})}return r})}let r=await getUsernameFromFirestore(),s=await I.toggleFavorite(r,u.id,e.id,e.name);if(s!==t&&console.log("Status mismatch - Optimistic:",t,"Actual:",s),s!==t){console.warn("Optimistic update was incorrect, correcting...");let t=new Set(g);if(s?t.add(e.id):t.delete(e.id),b(t),p(t=>t.map(t=>t.id===e.id?{...t,isFavorite:s}:t)),u){let t=`${u.id}_${u.name}`;H(a=>{let r=a.get(t);if(r){let o=r.models.map(t=>t.id===e.id?{...t,isFavorite:s}:t);return new Map(a).set(t,{models:o,timestamp:r.timestamp})}return a})}}}catch(r){console.error("Error toggling favorite:",r);let a=new Set(g);if(t?a.delete(e.id):a.add(e.id),b(a),p(a=>a.map(a=>a.id===e.id?{...a,isFavorite:!t}:a)),u){let a=`${u.id}_${u.name}`;H(r=>{let s=r.get(a);if(s){let o=s.models.map(a=>a.id===e.id?{...a,isFavorite:!t}:a);return new Map(r).set(a,{models:o,timestamp:s.timestamp})}return r})}}finally{W(t=>{let a=new Set(t);return a.delete(e.id),a})}},isExpensiveModel=e=>{if(u?.name!=="OpenRouter")return!1;let t=A.getTotalPrice(e);return t>2e-5},handleSelectModel=e=>{ea(e.id),isExpensiveModel(e)?(E(e),S(!0)):(l(e.id),t())},handleUseCustomModel=()=>{k.trim()&&(l(k.trim()),t())};return e?(0,r.jsxs)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl rounded-2xl border border-blue-600/30 shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden relative",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none rounded-2xl"}),(0,r.jsxs)("div",{className:"p-6 border-b border-blue-700/30 relative z-10",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center",children:r.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),r.jsx("h2",{className:"text-xl font-semibold text-blue-100",children:"Selecionar Modelo"})]}),r.jsx("button",{onClick:t,className:"p-2 rounded-xl hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105",children:r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[r.jsx("label",{className:"block text-sm font-medium text-blue-200",children:"Endpoint"}),r.jsx("button",{onClick:()=>{if(u){let e=`${u.id}_${u.name}`;H(t=>{let a=new Map(t);return a.delete(e),a}),"OpenRouter"===u.name?loadOpenRouterModels():"DeepSeek"===u.name&&loadDeepSeekModels()}},disabled:f||!u,className:"p-2 rounded-lg hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed",title:"Atualizar modelos",children:r.jsx("svg",{className:`w-4 h-4 ${f?"animate-spin":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})]}),(0,r.jsxs)("select",{value:u?.id||"",onChange:e=>{let t=c.find(t=>t.id===e.target.value);h(t||null)},className:"w-full bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200",children:[r.jsx("option",{value:"",children:"Selecione um endpoint"}),c.map(e=>r.jsx("option",{value:e.id,className:"bg-blue-900 text-blue-100",children:e.name},e.id))]})]})]}),u?.name==="OpenRouter"&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"p-6 border-b border-blue-700/30 space-y-6 relative z-10",children:[(0,r.jsxs)("div",{className:"p-4 bg-blue-900/30 backdrop-blur-sm rounded-xl border border-blue-600/30",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-blue-200 mb-3 flex items-center space-x-2",children:[r.jsx("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})}),r.jsx("span",{children:"Modelo Customizado"})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[r.jsx("input",{type:"text",placeholder:"openai/gpt-4-1",value:k,onChange:e=>C(e.target.value),onKeyDown:e=>"Enter"===e.key&&handleUseCustomModel(),className:"flex-1 bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200"}),r.jsx("button",{onClick:handleUseCustomModel,disabled:!k.trim(),className:"px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:from-blue-800 disabled:to-blue-800 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 disabled:hover:scale-100",children:"Usar"})]}),r.jsx("p",{className:"text-xs text-blue-300/70 mt-3",children:"Digite o ID completo do modelo (ex: openai/gpt-4, anthropic/claude-3-sonnet)"})]}),r.jsx("div",{className:"flex space-x-1 bg-blue-900/30 backdrop-blur-sm rounded-xl p-1 border border-blue-600/20",children:["paid","free","favorites"].map(e=>r.jsx("button",{onClick:()=>G(t=>({...t,category:e})),className:`flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 ${K.category===e?"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg":"text-blue-300 hover:text-blue-200 hover:bg-blue-800/30"}`,children:"paid"===e?"Pagos":"free"===e?"Gr\xe1tis":"Favoritos"},e))}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex space-x-4",children:[r.jsx("div",{className:"flex-1",children:r.jsx(components_AdvancedSearchInput,{value:Z,onChange:X,suggestions:[],isSearching:Q,placeholder:"Buscar modelos... (ex: 'gpt-4', 'vision', 'cheap')",showSuggestions:!1})}),(0,r.jsxs)("select",{value:K.sortBy,onChange:e=>G(t=>({...t,sortBy:e.target.value})),className:"bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200",disabled:ee&&Z.trim().length>0,children:[r.jsx("option",{value:"newest",className:"bg-blue-900 text-blue-100",children:"Mais recentes"}),r.jsx("option",{value:"price_low",className:"bg-blue-900 text-blue-100",children:"Menor pre\xe7o"}),r.jsx("option",{value:"price_high",className:"bg-blue-900 text-blue-100",children:"Maior pre\xe7o"}),r.jsx("option",{value:"context_high",className:"bg-blue-900 text-blue-100",children:"Maior contexto"})]})]}),!ee&&(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[r.jsx("button",{onClick:()=>z(null),className:`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${L?"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200":"bg-blue-600 text-white"}`,children:"Todos"}),B.slice(0,6).map(e=>(0,r.jsxs)("button",{onClick:()=>z(e.id),className:`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-1 ${L===e.id?"bg-blue-600 text-white":"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200"}`,title:e.description,children:[r.jsx("span",{children:e.icon}),r.jsx("span",{children:e.name})]},e.id))]}),ee&&Z.trim()&&(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("span",{className:"text-blue-300",children:[eo.length," resultado",1!==eo.length?"s":"",' para "',Z,'"']}),r.jsx("button",{onClick:et,className:"text-blue-400 hover:text-blue-300 transition-colors duration-200",children:"Limpar busca"})]})]})]}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-scroll p-6 max-h-[32rem] relative z-10",children:[f&&r.jsx("div",{className:"flex justify-center py-8",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3",children:[r.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400"}),r.jsx("span",{className:"text-blue-200 text-sm font-medium",children:"Carregando modelos..."})]})}),w&&r.jsx("div",{className:"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[r.jsx("div",{className:"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center",children:r.jsx("svg",{className:"w-4 h-4 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),r.jsx("span",{className:"text-red-300 font-medium",children:w})]})}),!f&&!w&&0===eo.length&&(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx("div",{className:"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4",children:r.jsx("svg",{className:"w-8 h-8 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),r.jsx("p",{className:"text-blue-300 font-medium",children:ee&&Z.trim()?`Nenhum resultado para "${Z}"`:L?"Nenhum modelo na categoria selecionada":"Nenhum modelo encontrado"}),r.jsx("div",{className:"text-blue-400/70 text-sm mt-2 space-y-1",children:ee&&Z.trim()?(0,r.jsxs)(r.Fragment,{children:[r.jsx("p",{children:"Tente:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-left max-w-xs mx-auto",children:[r.jsx("li",{children:"Verificar a ortografia"}),r.jsx("li",{children:"Usar termos mais gen\xe9ricos"}),r.jsx("li",{children:"Explorar as categorias"}),r.jsx("li",{children:"Limpar filtros ativos"})]})]}):r.jsx("p",{children:"Tente ajustar os filtros ou usar as categorias"})})]}),r.jsx("div",{className:"space-y-3",children:eo.slice(0,y).map(e=>{let t=ee?J.find(t=>t.model.id===e.id):null;return r.jsx(ModelCard,{model:e,isSelected:o===e.id,onSelect:()=>handleSelectModel(e),onToggleFavorite:()=>handleToggleFavorite(e),isToggling:R.has(e.id),service:A,searchTerm:ee?Z:"",searchResult:t},e.id)})}),!f&&!w&&eo.length>y&&r.jsx("div",{className:"flex justify-center mt-6",children:(0,r.jsxs)("button",{onClick:()=>{N(e=>e+4)},className:"px-6 py-3 bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm border border-blue-600/30 hover:border-blue-500/50 rounded-xl text-blue-200 hover:text-blue-100 transition-all duration-200 flex items-center space-x-2 hover:scale-105",children:[r.jsx("span",{children:"Carregar mais modelos"}),r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]})}),!f&&!w&&eo.length>0&&(0,r.jsxs)("div",{className:"text-center mt-4 space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm text-blue-400/70",children:["Mostrando ",Math.min(y,eo.length)," de ",eo.length," modelos",x.length!==eo.length&&(0,r.jsxs)("span",{className:"ml-2 text-blue-300",children:["(",x.length," total)"]})]}),ee&&Z.trim()&&(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-4 text-xs text-blue-400/60",children:[(0,r.jsxs)("span",{children:["\uD83D\uDD0D Busca: ",Z]}),J.length>0&&(0,r.jsxs)("span",{children:["⚡ ",J.length," resultados"]})]})]})]})]}),u?.name==="DeepSeek"&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"p-6 border-b border-slate-700/30 space-y-4",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h3",{className:"text-lg font-medium text-slate-100 mb-2",children:"Modelos DeepSeek"}),r.jsx("p",{className:"text-sm text-slate-400",children:"Escolha entre nossos modelos especializados"})]})}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto p-6",children:[f&&r.jsx("div",{className:"flex justify-center py-8",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3",children:[r.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400"}),r.jsx("span",{className:"text-blue-200 text-sm font-medium",children:"Carregando modelos..."})]})}),w&&r.jsx("div",{className:"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[r.jsx("div",{className:"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center",children:r.jsx("svg",{className:"w-4 h-4 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),r.jsx("span",{className:"text-red-300 font-medium",children:w})]})}),!f&&!w&&0===x.length&&(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx("div",{className:"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4",children:r.jsx("svg",{className:"w-8 h-8 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),r.jsx("p",{className:"text-blue-300 font-medium",children:"Nenhum modelo encontrado"}),r.jsx("p",{className:"text-blue-400/70 text-sm mt-1",children:"Tente ajustar os filtros de busca"})]}),!f&&!w&&x.length>0&&r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:x.map(e=>r.jsx(DeepSeekModelCard,{model:e,isSelected:o===e.id,onSelect:()=>handleSelectModel(e)},e.id))})]})]}),u?.name!=="OpenRouter"&&u?.name!=="DeepSeek"&&u&&(0,r.jsxs)("div",{className:"p-8 text-center relative z-10",children:[r.jsx("div",{className:"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4",children:r.jsx("svg",{className:"w-8 h-8 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),r.jsx("p",{className:"text-blue-300 font-medium",children:"Sele\xe7\xe3o de modelos dispon\xedvel para OpenRouter e DeepSeek"}),r.jsx("p",{className:"text-blue-400/70 text-sm mt-1",children:"Selecione um desses endpoints para ver os modelos dispon\xedveis"})]})]}),r.jsx(dashboard_ExpensiveModelConfirmationModal,{isOpen:M,model:D,onConfirm:()=>{D&&(ea(D.id),l(D.id),S(!1),E(null),t())},onCancel:()=>{S(!1),E(null)}})]}):null};var z=a(56206),B=a(1264);function AttachmentsModal({isOpen:e,onClose:t,attachments:a,activeAttachments:o,onToggleAttachment:n}){let[l,i]=(0,s.useState)(null);(0,s.useEffect)(()=>{let handleEsc=e=>{"Escape"===e.key&&t()};return e&&(document.addEventListener("keydown",handleEsc),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",handleEsc),document.body.style.overflow="unset"}},[e,t]);let formatFileSize=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},formatDate=e=>new Date(e).toLocaleString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),handleImageClick=e=>{i(e)},handleDownload=e=>{let t=document.createElement("a");t.href=e.url,t.download=e.filename,t.target="_blank",document.body.appendChild(t),t.click(),document.body.removeChild(t)},isAttachmentActive=e=>o.includes(e),d=a.filter(e=>isAttachmentActive(e.id)).length,c=a.length;return e?(0,r.jsxs)(r.Fragment,{children:[r.jsx(u.M,{children:e&&r.jsx(m.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4",onClick:t,children:(0,r.jsxs)(m.E.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.95,opacity:0},className:"bg-gradient-to-br from-blue-900/95 to-blue-800/95 backdrop-blur-sm border border-blue-600/30 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden shadow-2xl",onClick:e=>e.stopPropagation(),children:[r.jsx("div",{className:"bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border-b border-blue-600/30 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30",children:r.jsx("svg",{className:"w-5 h-5 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"})})}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-semibold text-white",children:"Gerenciar Anexos"}),(0,r.jsxs)("p",{className:"text-blue-200 text-sm",children:[d," de ",c," anexos ativos no contexto"]})]})]}),r.jsx("button",{onClick:t,className:"p-2 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 border border-blue-600/20",children:r.jsx(z.Z,{className:"w-5 h-5"})})]})}),r.jsx("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:0===a.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx("div",{className:"w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-500/30",children:r.jsx("svg",{className:"w-8 h-8 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"})})}),r.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Nenhum anexo encontrado"}),r.jsx("p",{className:"text-blue-200",children:"Este chat ainda n\xe3o possui anexos enviados."})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("div",{className:"bg-blue-800/30 backdrop-blur-sm border border-blue-600/30 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-white font-medium",children:"Controles R\xe1pidos"}),r.jsx("p",{className:"text-blue-200 text-sm",children:"Ativar ou desativar todos os anexos"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>a.forEach(e=>{isAttachmentActive(e.id)||n(e.id)}),className:"px-4 py-2 bg-green-600/20 hover:bg-green-600/30 text-green-300 hover:text-green-200 rounded-lg border border-green-600/30 transition-all duration-200 text-sm",children:"Ativar Todos"}),r.jsx("button",{onClick:()=>a.forEach(e=>{isAttachmentActive(e.id)&&n(e.id)}),className:"px-4 py-2 bg-red-600/20 hover:bg-red-600/30 text-red-300 hover:text-red-200 rounded-lg border border-red-600/30 transition-all duration-200 text-sm",children:"Desativar Todos"})]})]})}),r.jsx("div",{className:"grid gap-4",children:a.map(e=>{let t=isAttachmentActive(e.id);return r.jsx(m.E.div,{layout:!0,className:`
                              bg-blue-800/30 backdrop-blur-sm border rounded-xl p-4 transition-all duration-200
                              ${t?"border-green-500/50 shadow-lg shadow-green-500/10":"border-blue-600/30 opacity-60"}
                            `,children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[r.jsx("div",{className:"flex-shrink-0 pt-1",children:r.jsx("button",{onClick:()=>n(e.id),className:`
                                    relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200
                                    ${t?"bg-green-600":"bg-gray-600"}
                                  `,children:r.jsx("span",{className:`
                                      inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200
                                      ${t?"translate-x-6":"translate-x-1"}
                                    `})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:["image"===e.type?r.jsx(y.Z,{className:"w-5 h-5 text-blue-400 flex-shrink-0"}):r.jsx(C.Z,{className:"w-5 h-5 text-red-400 flex-shrink-0"}),r.jsx("h4",{className:"text-white font-medium truncate",children:e.filename}),t&&(0,r.jsxs)("div",{className:"flex items-center space-x-1 bg-green-600/20 px-2 py-1 rounded-full border border-green-600/30",children:[r.jsx(B.Z,{className:"w-3 h-3 text-green-400"}),r.jsx("span",{className:"text-green-300 text-xs font-medium",children:"Ativo"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-blue-200 mb-3",children:[r.jsx("span",{children:formatFileSize(e.size)}),r.jsx("span",{children:"•"}),r.jsx("span",{children:formatDate(e.uploadedAt)}),r.jsx("span",{children:"•"}),r.jsx("span",{className:"capitalize",children:e.type})]}),"image"===e.type&&r.jsx("div",{className:"mb-3",children:r.jsx("img",{src:e.url,alt:e.filename,className:"max-w-32 h-auto rounded-lg cursor-pointer hover:opacity-90 transition-opacity border border-blue-600/30",onClick:()=>handleImageClick(e.url)})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:["image"===e.type&&(0,r.jsxs)("button",{onClick:()=>handleImageClick(e.url),className:"flex items-center space-x-1 px-3 py-1.5 bg-blue-600/20 hover:bg-blue-600/30 text-blue-300 hover:text-blue-200 rounded-lg border border-blue-600/30 transition-all duration-200 text-sm",children:[r.jsx(N.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Visualizar"})]}),(0,r.jsxs)("button",{onClick:()=>handleDownload(e),className:"flex items-center space-x-1 px-3 py-1.5 bg-gray-600/20 hover:bg-gray-600/30 text-gray-300 hover:text-gray-200 rounded-lg border border-gray-600/30 transition-all duration-200 text-sm",children:[r.jsx(k.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Download"})]})]})]})]})},e.id)})})]})}),r.jsx("div",{className:"bg-blue-800/30 backdrop-blur-sm border-t border-blue-600/30 p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"text-sm text-blue-200",children:(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Dica:"})," Anexos desativados n\xe3o ser\xe3o inclu\xeddos no contexto da conversa, mas permanecer\xe3o salvos no chat."]})}),r.jsx("button",{onClick:t,className:"px-6 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg transition-all duration-200 font-medium",children:"Fechar"})]})})]})})}),r.jsx(u.M,{children:l&&r.jsx(m.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/80 backdrop-blur-sm z-[60] flex items-center justify-center p-4",onClick:()=>i(null),children:(0,r.jsxs)(m.E.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.8,opacity:0},className:"relative max-w-[90vw] max-h-[90vh]",onClick:e=>e.stopPropagation(),children:[r.jsx("img",{src:l,alt:"Imagem expandida",className:"max-w-full max-h-full object-contain rounded-lg"}),r.jsx("button",{onClick:()=>i(null),className:"absolute top-4 right-4 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-all duration-200",children:r.jsx(z.Z,{className:"w-5 h-5"})})]})})})]}):null}let $=new class{calculateMessageStatistics(e,t){let a=this.filterMessages(e,t);if(0===a.length)return this.getEmptyStatistics();let r=a.filter(e=>"user"===e.role),s=a.filter(e=>"assistant"===e.role),o=this.countWords(r.map(e=>e.content).join(" ")),n=this.countWords(s.map(e=>e.content).join(" ")),l=o+n,i=r.reduce((e,t)=>e+t.content.length,0),d=s.reduce((e,t)=>e+t.content.length,0),c=i+d,m=a.reduce((e,t)=>e+(t.usage?.prompt_tokens||0),0),u=a.reduce((e,t)=>e+(t.usage?.completion_tokens||0),0),h=a.reduce((e,t)=>e+(t.usage?.cost||0),0),x=s.filter(e=>e.responseTime).map(e=>e.responseTime),p=x.length>0?x.reduce((e,t)=>e+t,0)/x.length:0,g=c/a.length,b=l/a.length,f=a.reduce((e,t)=>e+(t.content.match(/[.!?]+/g)||[]).length,0),v=f/a.length,w=this.getMostUsedWords(a.map(e=>e.content).join(" "));return{totalMessages:a.length,totalWords:l,totalWordsAI:n,totalWordsUser:o,totalPromptTokens:m,totalCompletionTokens:u,totalCost:h,averageResponseTime:p,averageMessageLength:g,averageWordsPerMessage:b,averageSentencesPerMessage:v,estimatedReadingTime:Math.ceil(l/250),totalCharactersAI:d,totalCharactersUser:i,totalCharacters:c,mostUsedWords:w}}calculateDetailedStatistics(e,t,a){let r=this.calculateMessageStatistics(e,a),s=this.filterMessages(e,a);return{...r,dailyStats:this.calculateDailyStats(s),weeklyStats:this.calculateWeeklyStats(s),monthlyStats:this.calculateMonthlyStats(s),modelStats:this.calculateModelStats(s),attachmentStats:this.calculateAttachmentStats(s),timeStats:this.calculateTimeStats(s,t),favoriteStats:this.calculateFavoriteStats(s)}}filterMessages(e,t){if(!t)return e;let a=[...e];return t.dateRange&&(a=a.filter(e=>{let a=new Date(e.timestamp);return a>=t.dateRange.start&&a<=t.dateRange.end})),a}countWords(e){return e.trim().split(/\s+/).filter(e=>e.length>0).length}getMostUsedWords(e,t=10){let a=e.toLowerCase().replace(/[^\w\s]/g,"").split(/\s+/).filter(e=>e.length>2),r=new Map;a.forEach(e=>{r.set(e,(r.get(e)||0)+1)});let s=a.length,o=Array.from(r.entries()).sort((e,t)=>t[1]-e[1]).slice(0,t).map(([e,t])=>({word:e,count:t,percentage:t/s*100}));return o}calculateDailyStats(e){let t=new Map;return e.forEach(e=>{let a=new Date(e.timestamp).toISOString().split("T")[0];t.has(a)||t.set(a,{messages:[],date:a}),t.get(a).messages.push(e)}),Array.from(t.values()).map(e=>{let t=this.countWords(e.messages.map(e=>e.content).join(" ")),a=e.messages.reduce((e,t)=>e+(t.usage?.total_tokens||0),0),r=e.messages.reduce((e,t)=>e+(t.usage?.cost||0),0),s=e.messages.filter(e=>e.responseTime).map(e=>e.responseTime),o=s.length>0?s.reduce((e,t)=>e+t,0)/s.length:0;return{date:e.date,messages:e.messages.length,words:t,tokens:a,cost:r,averageResponseTime:o}}).sort((e,t)=>e.date.localeCompare(t.date))}calculateWeeklyStats(e){let t=new Map;return e.forEach(e=>{let a=new Date(e.timestamp),r=a.getFullYear(),s=this.getWeekNumber(a),o=`${r}-W${s.toString().padStart(2,"0")}`;t.has(o)||t.set(o,[]),t.get(o).push(e)}),Array.from(t.entries()).map(([e,t])=>{let a=this.countWords(t.map(e=>e.content).join(" ")),r=t.reduce((e,t)=>e+(t.usage?.total_tokens||0),0),s=t.reduce((e,t)=>e+(t.usage?.cost||0),0),o=t.filter(e=>e.responseTime).map(e=>e.responseTime),n=o.length>0?o.reduce((e,t)=>e+t,0)/o.length:0;return{week:e,messages:t.length,words:a,tokens:r,cost:s,averageResponseTime:n}}).sort((e,t)=>e.week.localeCompare(t.week))}calculateMonthlyStats(e){let t=new Map;return e.forEach(e=>{let a=new Date(e.timestamp),r=`${a.getFullYear()}-${(a.getMonth()+1).toString().padStart(2,"0")}`;t.has(r)||t.set(r,[]),t.get(r).push(e)}),Array.from(t.entries()).map(([e,t])=>{let a=this.countWords(t.map(e=>e.content).join(" ")),r=t.reduce((e,t)=>e+(t.usage?.total_tokens||0),0),s=t.reduce((e,t)=>e+(t.usage?.cost||0),0),o=t.filter(e=>e.responseTime).map(e=>e.responseTime),n=o.length>0?o.reduce((e,t)=>e+t,0)/o.length:0;return{month:e,messages:t.length,words:a,tokens:r,cost:s,averageResponseTime:n}}).sort((e,t)=>e.month.localeCompare(t.month))}getWeekNumber(e){let t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate())),a=t.getUTCDay()||7;t.setUTCDate(t.getUTCDate()+4-a);let r=new Date(Date.UTC(t.getUTCFullYear(),0,1));return Math.ceil(((t.getTime()-r.getTime())/864e5+1)/7)}calculateModelStats(e){return[]}calculateAttachmentStats(e){let t=e.filter(e=>e.attachments&&e.attachments.length>0),a=t.flatMap(e=>e.attachments||[]),r=a.filter(e=>"image"===e.type).length,s=a.filter(e=>"pdf"===e.type).length,o=a.reduce((e,t)=>e+t.size,0),n=a.length>0?o/a.length:0,l={};return a.forEach(e=>{l[e.type]=(l[e.type]||0)+1}),{totalAttachments:a.length,imageAttachments:r,pdfAttachments:s,totalSize:o,averageSize:n,attachmentsByType:l}}calculateTimeStats(e,t){let a={},r={},s=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"];for(let e=0;e<24;e++)a[e]=0;s.forEach(e=>{r[e]=0}),e.forEach(e=>{let t=new Date(e.timestamp),o=t.getHours(),n=s[t.getDay()];a[o]++,r[n]++});let o=Object.entries(a).reduce((e,[t,a])=>a>e.count?{hour:parseInt(t),count:a}:e,{hour:0,count:0}).hour,n=Object.entries(r).reduce((e,[t,a])=>a>e.count?{day:t,count:a}:e,{day:"monday",count:0}).day,l=0,i=0,d=0,c=0,m=0;if(t){let e=t.filter(e=>e.sessionTime).map(e=>e.sessionTime.totalTime);e.length>0&&(i=(l=e.reduce((e,t)=>e+t,0))/e.length,d=Math.max(...e),c=Math.min(...e),m=e.length)}return{totalSessionTime:l,averageSessionTime:i,longestSession:d,shortestSession:c,sessionsCount:m,mostActiveHour:o,mostActiveDay:n,timeDistribution:a}}calculateFavoriteStats(e){let t=e.filter(e=>e.isFavorite),a={user:t.filter(e=>"user"===e.role).length,assistant:t.filter(e=>"assistant"===e.role).length},r=t.length>0?t.reduce((e,t)=>e+t.content.length,0)/t.length:0,s=t.length>0?this.getMostUsedWords(t.map(e=>e.content).join(" "),5):[];return{totalFavorites:t.length,favoritesByRole:a,averageFavoriteLength:r,mostFavoritedWords:s}}getEmptyStatistics(){return{totalMessages:0,totalWords:0,totalWordsAI:0,totalWordsUser:0,totalPromptTokens:0,totalCompletionTokens:0,totalCost:0,averageResponseTime:0,averageMessageLength:0,averageWordsPerMessage:0,averageSentencesPerMessage:0,estimatedReadingTime:0,totalCharactersAI:0,totalCharactersUser:0,totalCharacters:0,mostUsedWords:[]}}};function StatisticsModal({isOpen:e,onClose:t,messages:a,chatName:o="Todas as Conversas"}){var n;let[l,i]=(0,s.useState)(null),[d,c]=(0,s.useState)(!0),[m,u]=(0,s.useState)("overview");if((0,s.useEffect)(()=>{if(e&&a.length>0){c(!0);try{let e=$.calculateMessageStatistics(a);i(e)}catch(e){console.error("Erro ao calcular estat\xedsticas:",e)}finally{c(!1)}}},[e,a]),!e)return null;let formatNumber=e=>new Intl.NumberFormat("pt-BR").format(Math.round(e)),formatCurrency=e=>new Intl.NumberFormat("pt-BR",{style:"currency",currency:"USD",minimumFractionDigits:4}).format(e),getIconSvg=e=>{let t={message:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z",text:"M4 6h16M4 12h16M4 18h7",robot:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",input:"M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",output:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14",dollar:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1",clock:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",length:"M7 16l-4-4m0 0l4-4m-4 4h18",average:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",sentence:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253",read:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253",total:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"};return t[e]||t.message},getColorClasses=e=>({blue:"from-blue-500/20 to-blue-600/20 border-blue-500/30 text-blue-300",green:"from-green-500/20 to-green-600/20 border-green-500/30 text-green-300",purple:"from-purple-500/20 to-purple-600/20 border-purple-500/30 text-purple-300",orange:"from-orange-500/20 to-orange-600/20 border-orange-500/30 text-orange-300",red:"from-red-500/20 to-red-600/20 border-red-500/30 text-red-300",cyan:"from-cyan-500/20 to-cyan-600/20 border-cyan-500/30 text-cyan-300"})[e];return r.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border border-blue-700/30 rounded-2xl shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-blue-700/30",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold text-white flex items-center gap-3",children:[r.jsx("svg",{className:"w-8 h-8 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),"Estat\xedsticas"]}),r.jsx("p",{className:"text-blue-300 mt-1",children:o})]}),r.jsx("button",{onClick:t,className:"text-blue-300 hover:text-white transition-colors p-2 hover:bg-blue-800/30 rounded-lg",children:r.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),r.jsx("div",{className:"flex border-b border-blue-700/30",children:[{id:"overview",label:"Vis\xe3o Geral",icon:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},{id:"details",label:"Detalhes",icon:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},{id:"charts",label:"Gr\xe1ficos",icon:"M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"}].map(e=>(0,r.jsxs)("button",{onClick:()=>u(e.id),className:`flex items-center gap-2 px-6 py-3 font-medium transition-all ${m===e.id?"text-blue-300 border-b-2 border-blue-400 bg-blue-900/20":"text-blue-400 hover:text-blue-300 hover:bg-blue-900/10"}`,children:[r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:e.icon})}),e.label]},e.id))}),r.jsx("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-200px)]",children:d?(0,r.jsxs)("div",{className:"flex items-center justify-center py-12",children:[r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400"}),r.jsx("span",{className:"ml-3 text-blue-300",children:"Calculando estat\xedsticas..."})]}):l?(0,r.jsxs)(r.Fragment,{children:["overview"===m&&r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4",children:(l?[{title:"Total de Mensagens",value:formatNumber(l.totalMessages),icon:"message",color:"blue"},{title:"Total de Palavras",value:formatNumber(l.totalWords),icon:"text",color:"green"},{title:"Palavras da IA",value:formatNumber(l.totalWordsAI),icon:"robot",color:"purple"},{title:"Palavras do Usu\xe1rio",value:formatNumber(l.totalWordsUser),icon:"user",color:"orange"},{title:"Tempo M\xe9dio de Resposta",value:(n=l.averageResponseTime)<1e3?`${Math.round(n)}ms`:n<6e4?`${(n/1e3).toFixed(1)}s`:`${(n/6e4).toFixed(1)}min`,icon:"clock",color:"purple"},{title:"Comprimento M\xe9dio",value:formatNumber(l.averageMessageLength),subtitle:"caracteres",icon:"length",color:"orange"},{title:"Palavras por Mensagem",value:formatNumber(l.averageWordsPerMessage),icon:"average",color:"cyan"},{title:"Frases por Mensagem",value:formatNumber(l.averageSentencesPerMessage),icon:"sentence",color:"blue"},{title:"Tempo de Leitura",value:`${l.estimatedReadingTime}min`,icon:"read",color:"green"},{title:"Caracteres da IA",value:formatNumber(l.totalCharactersAI),icon:"robot",color:"purple"},{title:"Caracteres do Usu\xe1rio",value:formatNumber(l.totalCharactersUser),icon:"user",color:"orange"},{title:"Total de Caracteres",value:formatNumber(l.totalCharacters),icon:"total",color:"cyan"}]:[]).map((e,t)=>(0,r.jsxs)("div",{className:`bg-gradient-to-br ${getColorClasses(e.color)} border rounded-xl p-4 hover:scale-105 transition-transform duration-200`,children:[r.jsx("div",{className:"flex items-center justify-between mb-2",children:r.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:getIconSvg(e.icon)})})}),r.jsx("h3",{className:"text-sm font-medium text-white/80 mb-1",children:e.title}),r.jsx("p",{className:"text-2xl font-bold text-white",children:e.value}),e.subtitle&&r.jsx("p",{className:"text-xs text-white/60 mt-1",children:e.subtitle})]},t))}),"details"===m&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-900/20 border border-blue-700/30 rounded-xl p-6",children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center gap-2",children:[r.jsx("svg",{className:"w-6 h-6 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),"Palavras Mais Usadas"]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:l.mostUsedWords.slice(0,9).map((e,t)=>(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("span",{className:"text-white font-medium",children:["#",t+1]}),(0,r.jsxs)("span",{className:"text-blue-300 text-sm",children:[e.percentage.toFixed(1),"%"]})]}),r.jsx("p",{className:"text-lg font-semibold text-white",children:e.word}),(0,r.jsxs)("p",{className:"text-blue-400 text-sm",children:[formatNumber(e.count)," vezes"]})]},t))})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-blue-900/20 border border-blue-700/30 rounded-xl p-6",children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center gap-2",children:[r.jsx("svg",{className:"w-6 h-6 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),"Distribui\xe7\xe3o por Usu\xe1rio"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-blue-300",children:"Mensagens do Usu\xe1rio"}),r.jsx("span",{className:"text-white font-semibold",children:formatNumber(a.filter(e=>"user"===e.role).length)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-blue-300",children:"Mensagens da IA"}),r.jsx("span",{className:"text-white font-semibold",children:formatNumber(a.filter(e=>"assistant"===e.role).length)})]}),r.jsx("div",{className:"w-full bg-blue-800/30 rounded-full h-3",children:r.jsx("div",{className:"bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-500",style:{width:`${a.filter(e=>"user"===e.role).length/a.length*100}%`}})})]})]}),(0,r.jsxs)("div",{className:"bg-blue-900/20 border border-blue-700/30 rounded-xl p-6",children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center gap-2",children:[r.jsx("svg",{className:"w-6 h-6 text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),"An\xe1lise de Custos"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-blue-300",children:"Custo por Mensagem"}),r.jsx("span",{className:"text-white font-semibold",children:formatCurrency(l.totalCost/l.totalMessages)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-blue-300",children:"Custo por Token"}),r.jsx("span",{className:"text-white font-semibold",children:formatCurrency(l.totalCost/(l.totalPromptTokens+l.totalCompletionTokens))})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-blue-300",children:"Custo por Palavra"}),r.jsx("span",{className:"text-white font-semibold",children:formatCurrency(l.totalCost/l.totalWords)})]})]})]})]})]}),"charts"===m&&r.jsx("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx("svg",{className:"w-16 h-16 text-blue-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),r.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"Gr\xe1ficos em Desenvolvimento"}),r.jsx("p",{className:"text-blue-300",children:"Os gr\xe1ficos interativos estar\xe3o dispon\xedveis em breve para uma melhor visualiza\xe7\xe3o dos dados."})]})})]}):r.jsx("div",{className:"text-center py-12",children:r.jsx("p",{className:"text-blue-300",children:"Nenhuma estat\xedstica dispon\xedvel"})})})]})})}let R=new class{async sendMessage(e,t,a,r){try{this.abortController=new AbortController;let r={username:e.username,chatId:e.chatId,message:e.message,model:e.model,attachments:e.attachments||[],isRegeneration:e.isRegeneration||!1,webSearchEnabled:e.webSearchEnabled||!1,userMessageId:e.userMessageId};console.log("=== DEBUG: AI SERVICE REQUEST DATA ==="),console.log("Request data:",JSON.stringify(r,null,2)),console.log("Attachments length:",r.attachments.length),r.attachments.length>0&&console.log("First attachment:",JSON.stringify(r.attachments[0],null,2));let s=await fetch(this.functionUrl,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r),signal:this.abortController.signal});if(console.log("\uD83C\uDF10 URL:",s.url),console.groupEnd(),!s.ok){let e=await s.json();throw console.group("❌ AI SERVICE - ERRO NA RESPOSTA"),console.error("Status:",s.status),console.error("Status Text:",s.statusText),console.error("Error Data:",e),console.groupEnd(),Error(e.error||`HTTP ${s.status}: ${s.statusText}`)}if(!s.body)throw Error("Response body is not available");let o=s.body.getReader(),n=new TextDecoder,l="";try{for(;;){let{done:e,value:a}=await o.read();if(e)break;let r=n.decode(a,{stream:!0});l+=r,t(r)}a(l)}finally{o.releaseLock()}}catch(e){if(e instanceof Error){if("AbortError"===e.name)return;r(e.message)}else r("Erro desconhecido na comunica\xe7\xe3o com a IA")}finally{this.abortController=null}}cancelRequest(){this.abortController&&(this.abortController.abort(),this.abortController=null)}isRequestInProgress(){return null!==this.abortController}async loadChatMessages(e,t){try{let a=await fetch(`/api/chat/${e}/${t}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error(`Erro ao carregar chat: ${a.statusText}`);let r=await a.json();return r.messages||[]}catch(e){return console.error("Erro ao carregar mensagens do chat:",e),[]}}convertToAIFormat(e){return e.map(e=>({id:e.id,content:e.content,role:"user"===e.sender?"user":"assistant",timestamp:e.timestamp,isFavorite:e.isFavorite||!1,attachments:e.attachments||[]}))}convertFromAIFormat(e){return e.map(e=>({id:e.id,content:e.content,sender:"user"===e.role?"user":"ai",timestamp:e.timestamp,isFavorite:e.isFavorite||!1,attachments:e.attachments||[]}))}generateMessageId(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}validateConfig(e){if(!e.username?.trim())throw Error("Username \xe9 obrigat\xf3rio");if(!e.chatId?.trim())throw Error("Chat ID \xe9 obrigat\xf3rio");if(!e.message?.trim())throw Error("Mensagem \xe9 obrigat\xf3ria");if(e.message.length>1e4)throw Error("Mensagem muito longa (m\xe1ximo 10.000 caracteres)")}async sendMessageSafe(e,t,a,r){try{this.validateConfig(e),await this.sendMessage(e,t,a,r)}catch(e){e instanceof Error?r(e.message):r("Erro de valida\xe7\xe3o")}}async deleteMessage(e,t,a){try{let r=await fetch(`/api/chat/${e}/${t}/message/${a}`,{method:"DELETE",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error(`Erro ao deletar mensagem: ${r.statusText}`);return!0}catch(e){return console.error("Erro ao deletar mensagem:",e),!1}}async updateMessage(e,t,a,r){try{let s=await fetch(`/api/chat/${e}/${t}/message/${a}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:r})});if(!s.ok)throw Error(`Erro ao atualizar mensagem: ${s.statusText}`);return!0}catch(e){return console.error("Erro ao atualizar mensagem:",e),!1}}constructor(){this.functionUrl="https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI",this.abortController=null}};function ChatArea({currentChat:e,onChatCreated:t,onUpdateOpenRouterBalance:o}){let{user:l}=(0,n.useAuth)(),[m,u]=(0,s.useState)(""),[h,x]=(0,s.useState)(!1),[p,g]=(0,s.useState)([]),[b,f]=(0,s.useState)("meta-llama/llama-3.1-8b-instruct:free"),[v,w]=(0,s.useState)(e),[j,y]=(0,s.useState)(!1),[N,k]=(0,s.useState)(!1),[C,M]=(0,s.useState)(!1),[S,D]=(0,s.useState)(!1),[E,L]=(0,s.useState)(!1),[A,F]=(0,s.useState)(null),[I,T]=(0,s.useState)("Nova Conversa"),[P,z]=(0,s.useState)(!1),[B,$]=(0,s.useState)(void 0),W=(0,s.useRef)(null),[O,U]=(0,s.useState)(!1),[_,H]=(0,s.useState)(0);(0,s.useEffect)(()=>{let loadUsername=async()=>{if(l?.email){let e=await getUsernameFromFirestore();$(e)}};loadUsername()},[l?.email]);let getUsernameFromFirestore=async()=>{if(!l?.email)return"unknown";try{let e=(0,d.collection)(i.db,"usuarios"),t=(0,d.query)(e,(0,d.where)("email","==",l.email)),a=await (0,d.getDocs)(t);if(!a.empty){let e=a.docs[0],t=e.data();return t.username||l.email.split("@")[0]}return l.email.split("@")[0]}catch(e){return console.error("Erro ao buscar username:",e),l.email.split("@")[0]}},saveLastUsedModelForChat=async(e,t)=>{if(l&&t)try{let a=await getUsernameFromFirestore(),r=(0,d.doc)(i.db,"usuarios",a,"conversas",t);await (0,d.r7)(r,{lastUsedModel:e,lastModelUpdateAt:Date.now()})}catch(e){console.error("Error saving last used model for chat:",e)}},loadLastUsedModelForChat=async e=>{if(l&&e)try{let t=await getUsernameFromFirestore(),a=(0,d.doc)(i.db,"usuarios",t,"conversas",e),r=await (0,d.getDoc)(a);if(r.exists()){let e=r.data();if(e.lastUsedModel){let t=await isValidModel(e.lastUsedModel);t?f(e.lastUsedModel):(await (0,d.r7)(a,{lastUsedModel:""}),loadDefaultModelFromActiveEndpoint())}else loadDefaultModelFromActiveEndpoint()}}catch(e){console.error("Error loading last used model for chat:",e)}},loadDefaultModelFromActiveEndpoint=async()=>{if(l)try{let e=await getUsernameFromFirestore(),t=(0,d.doc)(i.db,"usuarios",e,"configuracoes","settings"),a=await (0,d.getDoc)(t);if(a.exists()){let e=a.data();if(e.lastUsedModel){f(e.lastUsedModel);return}if(e.endpoints){let t=Object.values(e.endpoints).find(e=>e.ativo);if(t&&t.modeloPadrao){f(t.modeloPadrao);return}}}f("meta-llama/llama-3.1-8b-instruct:free")}catch(e){console.error("Error loading default model from active endpoint:",e),f("meta-llama/llama-3.1-8b-instruct:free")}},isValidModel=async e=>!["qwen/qwen3-235b-a22b-thinking-2507"].includes(e),cleanupInvalidModelsFromAllChats=async()=>{if(l)try{let e=await getUsernameFromFirestore(),t=(0,d.collection)(i.db,"usuarios",e,"conversas"),a=await (0,d.getDocs)(t),r=[];for(let t of a.docs){let a=t.data();if(a.lastUsedModel){let s=await isValidModel(a.lastUsedModel);s||r.push((0,d.r7)((0,d.doc)(i.db,"usuarios",e,"conversas",t.id),{lastUsedModel:""}))}}r.length>0&&await Promise.all(r)}catch(e){console.error("Error cleaning invalid models from chats:",e)}},loadGlobalLastUsedModel=async()=>{if(l)try{let e=await getUsernameFromFirestore(),t=(0,d.doc)(i.db,"usuarios",e,"configuracoes","settings"),a=await (0,d.getDoc)(t);if(a.exists()){let e=a.data();if(e.lastUsedModel){let a=await isValidModel(e.lastUsedModel);a?f(e.lastUsedModel):(await (0,d.r7)(t,{lastUsedModel:null}),loadDefaultModelFromActiveEndpoint())}else loadDefaultModelFromActiveEndpoint()}else loadDefaultModelFromActiveEndpoint()}catch(e){console.error("Error loading global last used model:",e),loadDefaultModelFromActiveEndpoint()}},handleModelChange=e=>{f(e),v&&saveLastUsedModelForChat(e,v)},createAutoChat=async e=>{if(!l?.email)return null;try{let t=(0,d.collection)(i.db,"usuarios"),a=(0,d.query)(t,(0,d.where)("email","==",l.email)),r=await (0,d.getDocs)(a);if(r.empty)return null;let s=r.docs[0],o=s.data(),n=o.username,m=Date.now(),u=Math.random().toString(36).substring(2,8),h=`chat_${m}_${u}`,x=new Date().toISOString(),p="Nova Conversa";if(e.trim().length>0){let t=e.trim().split(" "),a=t.slice(0,Math.min(4,t.length)).join(" ");p=a.length>30?a.substring(0,30)+"...":a}let g={context:"",createdAt:x,folderId:null,frequencyPenalty:1,isFixed:!1,lastUpdatedAt:x,lastUsedModel:b,latexInstructions:!1,maxTokens:2048,name:p,password:"",repetitionPenalty:1,sessionTime:{lastSessionStart:x,lastUpdated:x,totalTime:0},systemPrompt:"",temperature:1,ultimaMensagem:e||"Anexo enviado",ultimaMensagemEm:x,updatedAt:x};await (0,d.pl)((0,d.doc)(i.db,"usuarios",n,"conversas",h),g);let f={id:h,name:p,messages:[],createdAt:x,lastUpdated:x},v=new Blob([JSON.stringify(f,null,2)],{type:"application/json"}),w=(0,c.iH)(i.tO,`usuarios/${n}/conversas/${h}/chat.json`);return await (0,c.KV)(w,v),console.log("Chat criado automaticamente:",h),T(p),h}catch(e){return console.error("Erro ao criar chat automaticamente:",e),null}},handleSendMessage=async(e,a)=>{let r=getAllChatAttachments().filter(e=>!1!==e.isActive),s=[...e||[],...r],n=s.filter((e,t,a)=>t===a.findIndex(t=>t.id===e.id));if(!m.trim()&&(!e||0===e.length)||h||E||!l?.email)return;let i={id:R.generateMessageId(),content:m.trim(),sender:"user",timestamp:new Date().toISOString(),attachments:e||[]},d=v;if(!d){let a=m.trim()||(e&&e.length>0?"Anexo enviado":"Nova conversa");(d=await createAutoChat(a))&&(w(d),loadChatName(d),t?.(d))}if(!d){console.error("N\xe3o foi poss\xedvel criar ou obter chat ID");return}g(e=>[...e,i]);let c=m.trim()||"";u(""),x(!0),L(!0);let p=R.generateMessageId();F(p);let f=await getUsernameFromFirestore();await R.sendMessageSafe({username:f,chatId:d,message:c,model:b,attachments:n,webSearchEnabled:a,userMessageId:i.id},e=>{g(t=>{let r=t.findIndex(e=>e.id===p);if(-1!==r)return t.map(t=>t.id===p?{...t,content:t.content+e}:t);{x(!1);let r={id:p,content:e,sender:"ai",timestamp:new Date().toISOString(),hasWebSearch:a};return[...t,r]}})},e=>{g(t=>t.map(t=>t.id===p?{...t,content:e}:t)),x(!1),L(!1),F(null),d&&saveLastUsedModelForChat(b,d),o&&setTimeout(()=>{o()},5e3)},e=>{console.error("Erro na IA:",e),g(t=>t.map(t=>t.id===p?{...t,content:`❌ Erro: ${e}`}:t)),x(!1),L(!1),F(null)})},loadChatName=async e=>{if(l?.email)try{let t=await getUsernameFromFirestore(),a=await (0,d.getDoc)((0,d.doc)(i.db,"usuarios",t,"conversas",e));if(a.exists()){let e=a.data(),t=e.name||"Conversa sem nome";T(t)}else T("Conversa n\xe3o encontrada")}catch(e){console.error("Erro ao carregar nome do chat:",e),T("Erro ao carregar nome")}},loadChatMessages=async e=>{if(l?.email){z(!0);try{let t=await getUsernameFromFirestore(),a=await R.loadChatMessages(t,e),r=R.convertFromAIFormat(a);g(r)}catch(e){console.error("❌ Erro ao carregar mensagens do chat:",e),g([])}finally{z(!1)}}};(0,s.useEffect)(()=>{l&&!e&&(cleanupInvalidModelsFromAllChats(),loadGlobalLastUsedModel())},[l,e]),(0,s.useEffect)(()=>{e&&e!==v?(w(e),z(!0),g([]),loadChatMessages(e),loadChatName(e),loadLastUsedModelForChat(e)):!e&&v&&(w(null),g([]),T("Nova Conversa"),z(!1),loadGlobalLastUsedModel())},[e,l?.email]);let handleDeleteMessage=async e=>{if(v&&l?.email){g(t=>t.filter(t=>t.id!==e));try{let t=await getUsernameFromFirestore(),a=await R.deleteMessage(t,v,e);a||(loadChatMessages(v),console.error("Falha ao deletar mensagem no servidor"))}catch(e){loadChatMessages(v),console.error("Erro ao deletar mensagem:",e)}}},handleRegenerateMessage=async e=>{if(v&&l?.email){console.log("\uD83D\uDD04 Recarregando mensagens antes da regenera\xe7\xe3o para garantir estado atualizado...");try{let t=await getUsernameFromFirestore(),a=await R.loadChatMessages(t,v),r=R.convertFromAIFormat(a);console.log("\uD83D\uDCE5 Mensagens recarregadas do Storage:",r.length);let s=r.findIndex(t=>t.id===e);if(-1===s){console.error("❌ Mensagem n\xe3o encontrada ap\xf3s recarregar:",e),x(!1),L(!1);return}let n=r[s];console.log("\uD83D\uDCDD Mensagem que ser\xe1 regenerada:",{id:n.id,content:n.content.substring(0,100)+"...",index:s});let l=r.slice(0,s+1);g(l),u(n.content),x(!0),L(!0);let i=R.generateMessageId();F(i),console.log(`🗑️ Deletando ${r.length-s-1} mensagens posteriores...`);for(let e=s+1;e<r.length;e++){let a=r[e];console.log("\uD83D\uDDD1️ Deletando mensagem:",a.id),await R.deleteMessage(t,v,a.id)}await R.sendMessageSafe({username:t,chatId:v,message:n.content,model:b,isRegeneration:!0,userMessageId:n.id},e=>{g(t=>{let a=t.findIndex(e=>e.id===i);if(-1!==a)return t.map(t=>t.id===i?{...t,content:t.content+e}:t);{x(!1);let a={id:i,content:e,sender:"ai",timestamp:new Date().toISOString(),hasWebSearch:!1};return[...t,a]}})},e=>{g(t=>t.map(t=>t.id===i?{...t,content:e}:t)),x(!1),L(!1),F(null),u(""),o&&setTimeout(()=>{o()},5e3)},e=>{console.error("Erro na regenera\xe7\xe3o:",e),g(t=>t.map(t=>t.id===i?{...t,content:`❌ Erro na regenera\xe7\xe3o: ${e}`}:t)),x(!1),L(!1),F(null),u("")})}catch(e){console.error("❌ Erro ao regenerar mensagem:",e),x(!1),L(!1),F(null),u(""),loadChatMessages(v)}}},handleEditMessage=async(e,t)=>{if(!v||!l?.email)return!1;console.log("✏️ Iniciando edi\xe7\xe3o de mensagem:",{messageId:e,chatId:v,newContentLength:t.length,newContentPreview:t.substring(0,100)+"..."}),g(a=>a.map(a=>a.id===e?{...a,content:t}:a));try{let a=await getUsernameFromFirestore();console.log("\uD83D\uDCE4 Enviando atualiza\xe7\xe3o para o servidor...");let r=await R.updateMessage(a,v,e,t);if(!r)return console.error("❌ Falha ao atualizar mensagem no servidor"),loadChatMessages(v),!1;return console.log("✅ Mensagem editada e salva com sucesso no Firebase Storage:",{messageId:e,timestamp:new Date().toISOString()}),!0}catch(e){return console.error("❌ Erro ao atualizar mensagem:",e),loadChatMessages(v),!1}},handleEditAndRegenerate=async(e,t)=>{if(v&&l?.email){console.log("✏️\uD83D\uDD04 Iniciando edi\xe7\xe3o e regenera\xe7\xe3o de mensagem:",{messageId:e,chatId:v,newContentLength:t.length,newContentPreview:t.substring(0,100)+"..."});try{let a=await handleEditMessage(e,t);if(!a){console.error("❌ Falha ao editar mensagem, cancelando regenera\xe7\xe3o");return}console.log("✅ Mensagem editada com sucesso, iniciando regenera\xe7\xe3o..."),await new Promise(e=>setTimeout(e,200)),console.log("\uD83D\uDD04 Recarregando mensagens antes da regenera\xe7\xe3o...");let r=await getUsernameFromFirestore(),s=await R.loadChatMessages(r,v),n=R.convertFromAIFormat(s);console.log("\uD83D\uDCE5 Mensagens recarregadas do Storage:",n.length);let l=n.findIndex(t=>t.id===e);if(-1===l){console.error("❌ Mensagem n\xe3o encontrada ap\xf3s recarregar:",e);return}let i=n[l];console.log("\uD83D\uDCDD Mensagem que ser\xe1 regenerada:",{id:i.id,content:i.content.substring(0,100)+"...",index:l});let d=l<n.length-1;console.log(`📊 Mensagens ap\xf3s esta: ${n.length-l-1}`);let c=n.slice(0,l+1);g(c),u(i.content),x(!0),L(!0);let m=R.generateMessageId();if(F(m),d){console.log(`🗑️ Deletando ${n.length-l-1} mensagens posteriores...`);for(let e=l+1;e<n.length;e++){let t=n[e];console.log("\uD83D\uDDD1️ Deletando mensagem:",t.id),await R.deleteMessage(r,v,t.id)}}await R.sendMessageSafe({username:r,chatId:v,message:i.content,model:b,isRegeneration:!0,userMessageId:i.id},e=>{g(t=>{let a=t.findIndex(e=>e.id===m);if(-1!==a)return t.map(t=>t.id===m?{...t,content:t.content+e}:t);{x(!1);let a={id:m,content:e,sender:"ai",timestamp:new Date().toISOString(),hasWebSearch:!1};return[...t,a]}})},e=>{g(t=>t.map(t=>t.id===m?{...t,content:e}:t)),x(!1),L(!1),F(null),u(""),o&&setTimeout(()=>{o()},5e3)},e=>{console.error("Erro na regenera\xe7\xe3o:",e),g(t=>t.map(t=>t.id===m?{...t,content:`❌ Erro na regenera\xe7\xe3o: ${e}`}:t)),x(!1),L(!1),F(null),u("")})}catch(e){console.error("❌ Erro ao editar e regenerar mensagem:",e),x(!1),L(!1),F(null),u(""),loadChatMessages(v)}}},convertToChatMessages=e=>e.map(e=>({id:e.id,content:e.content,role:"user"===e.sender?"user":"assistant",timestamp:new Date(e.timestamp).getTime(),isFavorite:e.isFavorite||!1,attachments:e.attachments||[]})),getAllChatAttachments=()=>{let e=[];p.forEach(t=>{t.attachments&&t.attachments.length>0&&e.push(...t.attachments)});let t=e.filter((e,t,a)=>t===a.findIndex(t=>t.id===e.id));return t},saveAttachmentStates=async e=>{if(B&&v)try{let t={id:v,name:I||"Chat",messages:e.map(e=>({id:e.id,content:e.content,role:"user"===e.sender?"user":"assistant",timestamp:e.timestamp,isFavorite:e.isFavorite,attachments:e.attachments})),createdAt:new Date().toISOString(),lastUpdated:new Date().toISOString()},a=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),r=(0,c.iH)(i.tO,`usuarios/${B}/conversas/${v}/chat.json`);await (0,c.KV)(r,a),console.log("✅ Estado dos anexos salvo no Firebase Storage"),console.log("\uD83D\uDCC1 Dados salvos:",{chatId:v,totalMessages:t.messages.length,messagesWithAttachments:t.messages.filter(e=>e.attachments&&e.attachments.length>0).length})}catch(e){console.error("❌ Erro ao salvar estado dos anexos:",e)}},getActiveAttachmentIds=()=>{let e=getAllChatAttachments();return e.filter(e=>!1!==e.isActive).map(e=>e.id)},handleDrop=async e=>{e.preventDefault(),e.stopPropagation(),U(!1),H(0);let r=Array.from(e.dataTransfer.files);if(0===r.length)return;if(!B){console.error("Username n\xe3o dispon\xedvel para upload de anexos");return}let s=v;if(!s){let e=1===r.length?`Arquivo anexado: ${r[0].name}`:`${r.length} arquivos anexados`;(s=await createAutoChat(e))&&(w(s),loadChatName(s),t?.(s))}if(!s){console.error("N\xe3o foi poss\xedvel criar ou obter chat ID para anexos");return}try{let{default:e}=await Promise.resolve().then(a.bind(a,42610)),t=await e.uploadMultipleAttachments(r,B,s),o=t.map(e=>e.metadata),n=new CustomEvent("dragDropAttachments",{detail:{attachments:o,chatId:s,username:B}});window.dispatchEvent(n),console.log(`✅ ${r.length} arquivo(s) adicionado(s) como anexo via drag-n-drop`)}catch(e){console.error("❌ Erro ao processar arquivos via drag-n-drop:",e)}};return(0,r.jsxs)("div",{className:"flex-1 flex flex-col h-screen relative",onDragEnter:e=>{e.preventDefault(),e.stopPropagation(),H(e=>e+1),e.dataTransfer.items&&e.dataTransfer.items.length>0&&U(!0)},onDragLeave:e=>{e.preventDefault(),e.stopPropagation(),H(e=>e-1),_<=1&&U(!1)},onDragOver:e=>{e.preventDefault(),e.stopPropagation(),e.dataTransfer.items&&e.dataTransfer.items.length>0&&(e.dataTransfer.dropEffect="copy")},onDrop:handleDrop,children:[O&&r.jsx("div",{className:"absolute inset-0 z-50 bg-blue-900/80 backdrop-blur-sm flex items-center justify-center",children:r.jsx("div",{className:"bg-blue-800/90 backdrop-blur-md rounded-2xl p-8 border-2 border-dashed border-blue-400 shadow-2xl",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"mb-4",children:r.jsx("svg",{className:"w-16 h-16 text-blue-300 mx-auto animate-bounce",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})})}),r.jsx("h3",{className:"text-xl font-semibold text-blue-100 mb-2",children:"Solte os arquivos aqui"}),r.jsx("p",{className:"text-blue-300 text-sm",children:"Os arquivos ser\xe3o adicionados como anexos \xe0 conversa"})]})})}),r.jsx(Upperbar,{currentChat:e,chatName:I,aiModel:b,onDownload:()=>{y(!0)},onAttachments:()=>{M(!0)},onStatistics:()=>{D(!0)},isLoading:h,attachmentsCount:getAllChatAttachments().length,aiMetadata:{usedCoT:!1}}),r.jsx("div",{ref:W,className:"flex-1 min-h-0",style:{height:"calc(100vh - 200px)"},children:r.jsx(ChatInterface,{messages:p,isLoading:h,isLoadingChat:P,isStreaming:E,streamingMessageId:A||void 0,onDeleteMessage:handleDeleteMessage,onRegenerateMessage:handleRegenerateMessage,onEditMessage:handleEditMessage,onEditAndRegenerate:handleEditAndRegenerate,onCopyMessage:e=>{navigator.clipboard.writeText(e).then(()=>{console.log("Mensagem copiada para a \xe1rea de transfer\xeancia")})}})}),r.jsx(InputBar,{message:m,setMessage:u,onSendMessage:handleSendMessage,isLoading:h,selectedModel:b,onModelChange:handleModelChange,onScrollToTop:()=>{let e=W.current?.querySelector(".overflow-y-auto");e&&e.scrollTo({top:0,behavior:"smooth"})},onScrollToBottom:()=>{let e=W.current?.querySelector(".overflow-y-auto");e&&e.scrollTo({top:e.scrollHeight,behavior:"smooth"})},isStreaming:E,onCancelStreaming:()=>{R.cancelRequest(),x(!1),L(!1),F(null)},onOpenModelModal:()=>k(!0),username:B,chatId:v||void 0,activeAttachmentsCount:getActiveAttachmentIds().length}),r.jsx(dashboard_DownloadModal,{isOpen:j,onClose:()=>y(!1),messages:convertToChatMessages(p),chatName:I}),r.jsx(dashboard_ModelSelectionModal,{isOpen:N,onClose:()=>k(!1),currentModel:b,onModelSelect:handleModelChange}),r.jsx(AttachmentsModal,{isOpen:C,onClose:()=>M(!1),attachments:getAllChatAttachments(),activeAttachments:getActiveAttachmentIds(),onToggleAttachment:e=>{g(t=>{let a=t.map(t=>{if(t.attachments&&t.attachments.length>0){let a=t.attachments.map(t=>{if(t.id===e){let e=!1!==t.isActive;return{...t,isActive:!e}}return t});return{...t,attachments:a}}return t});return saveAttachmentStates(a),a})}}),r.jsx(StatisticsModal,{isOpen:S,onClose:()=>D(!1),messages:convertToChatMessages(p),chatName:I})]})}var W=a(11766);function SettingsModal({isOpen:e,onClose:t,userData:a,onUserDataUpdate:o}){let{logout:l,user:h}=(0,n.useAuth)(),[x,p]=(0,s.useState)("geral"),[g,b]=(0,s.useState)(!1),f=(0,s.useRef)(null),[v,w]=(0,s.useState)({username:a.username,profileImage:a.profileImage||"",currentPassword:"",newPassword:"",confirmPassword:""}),[j,y]=(0,s.useState)({fonte:"Inter",tamanhoFonte:14,palavrasPorSessao:5e3}),[N,k]=(0,s.useState)([{nome:"OpenRouter",url:"https://openrouter.ai/api/v1/chat/completions",apiKey:"",modeloPadrao:"meta-llama/llama-3.1-8b-instruct:free",ativo:!1},{nome:"DeepSeek",url:"https://api.deepseek.com/v1/chat/completions",apiKey:"",modeloPadrao:"deepseek-chat",ativo:!1}]),[C,M]=(0,s.useState)([]),[S,D]=(0,s.useState)([]),[E,L]=(0,s.useState)([]),[A,F]=(0,s.useState)(!1),[I,T]=(0,s.useState)(!1),[P,z]=(0,s.useState)({titulo:"",conteudo:"",cor:"#3B82F6",categoria:null,chatId:null,global:!0}),[B,$]=(0,s.useState)({nome:"",descricao:"",cor:"#3B82F6"}),[R,O]=(0,s.useState)(!1),[U,_]=(0,s.useState)({nome:"",url:"",apiKey:"",modeloPadrao:"",ativo:!1}),[H,q]=(0,s.useState)(null),[V,K]=(0,s.useState)({nome:"",url:"",apiKey:"",modeloPadrao:"",ativo:!1});(0,s.useEffect)(()=>{let loadConfigurations=async()=>{if(a.username)try{console.log("Carregando configura\xe7\xf5es para:",a.username);let e=await (0,d.getDoc)((0,d.doc)(i.db,"usuarios",a.username,"configuracoes","settings"));if(e.exists()){let t=e.data();if(console.log("Configura\xe7\xf5es carregadas:",t),t.aparencia&&y(t.aparencia),t.endpoints){let e=Object.values(t.endpoints);k(e)}else k([{nome:"OpenRouter",url:"https://openrouter.ai/api/v1/chat/completions",apiKey:"",modeloPadrao:"meta-llama/llama-3.1-8b-instruct:free",ativo:!1},{nome:"DeepSeek",url:"https://api.deepseek.com/v1/chat/completions",apiKey:"",modeloPadrao:"deepseek-chat",ativo:!1}]);if(t.memorias){let e=Object.values(t.memorias);M(e)}if(t.categorias){let e=Object.values(t.categorias);D(e)}}else console.log("Nenhuma configura\xe7\xe3o encontrada, usando padr\xf5es"),y({fonte:"Inter",tamanhoFonte:14,palavrasPorSessao:5e3})}catch(e){console.error("Erro ao carregar configura\xe7\xf5es:",e)}};e&&a.username&&(w({username:a.username,profileImage:a.profileImage||"",currentPassword:"",newPassword:"",confirmPassword:""}),loadConfigurations(),loadChats())},[e,a.username,a.profileImage]);let loadChats=async()=>{if(a?.username)try{let e=(0,d.collection)(i.db,"usuarios",a.username,"conversas"),t=(0,d.query)(e,(0,d.Xo)("lastUpdatedAt","desc")),r=await (0,d.getDocs)(t),s=[];r.forEach(e=>{let t=e.data();s.push({id:e.id,name:t.name||"Conversa sem nome",lastMessage:t.ultimaMensagem||"Nenhuma mensagem ainda",lastMessageTime:t.ultimaMensagemEm||t.createdAt})}),L(s)}catch(e){console.error("Erro ao carregar chats:",e)}},deleteUserDocuments=async e=>{try{console.log("Iniciando exclus\xe3o de documentos para:",e);try{let t=(0,d.doc)(i.db,"usuarios",e,"configuracoes","settings"),a=await (0,d.getDoc)(t);a.exists()&&(await (0,d.oe)(t),console.log("Configura\xe7\xf5es deletadas"))}catch(e){console.log("Erro ao deletar configura\xe7\xf5es:",e)}try{let t=(0,d.collection)(i.db,"usuarios",e,"chats"),a=await (0,d.getDocs)(t),r=a.docs.map(e=>(0,d.oe)(e.ref));await Promise.all(r),console.log("Chats deletados")}catch(e){console.log("Erro ao deletar chats:",e)}let t=(0,d.doc)(i.db,"usuarios",e);await (0,d.oe)(t),console.log("Documento principal do usu\xe1rio deletado")}catch(e){throw console.error("Erro ao deletar documentos do usu\xe1rio:",e),e}},updateUsername=async(e,t=!0)=>{if(!a.username||!e||e===a.username)return t&&alert("Nome de usu\xe1rio inv\xe1lido ou igual ao atual."),!1;if(e.length<3)return t&&alert("Nome de usu\xe1rio deve ter pelo menos 3 caracteres."),!1;let r=!1;try{console.log("Atualizando username de",a.username,"para",e);let s=await (0,d.getDoc)((0,d.doc)(i.db,"usuarios",e));if(s.exists())return t&&alert("Este nome de usu\xe1rio j\xe1 est\xe1 em uso. Escolha outro."),!1;let n=(0,d.doc)(i.db,"usuarios",a.username),l=await (0,d.getDoc)(n);if(!l.exists())return t&&alert("Usu\xe1rio n\xe3o encontrado."),!1;let c=l.data();await (0,d.pl)((0,d.doc)(i.db,"usuarios",e),{...c,username:e,updatedAt:new Date().toISOString()}),r=!0,console.log("Novo documento criado para:",e);try{let t=await (0,d.getDoc)((0,d.doc)(i.db,"usuarios",a.username,"configuracoes","settings"));t.exists()&&(await (0,d.pl)((0,d.doc)(i.db,"usuarios",e,"configuracoes","settings"),t.data()),console.log("Configura\xe7\xf5es copiadas para novo username"));try{let t=(0,d.collection)(i.db,"usuarios",a.username,"chats"),r=await (0,d.getDocs)(t);for(let t of r.docs){let a=t.data();await (0,d.pl)((0,d.doc)(i.db,"usuarios",e,"chats",t.id),a)}r.docs.length>0&&console.log(`${r.docs.length} chats copiados para novo username`)}catch(e){console.log("Erro ao copiar chats:",e)}}catch(e){console.log("Erro ao copiar dados:",e)}return await deleteUserDocuments(a.username),console.log("Todos os documentos do usu\xe1rio antigo foram deletados"),o({...a,username:e}),t&&alert("Nome de usu\xe1rio atualizado com sucesso!"),!0}catch(a){if(console.error("Erro ao atualizar username:",a),r)try{await (0,d.oe)((0,d.doc)(i.db,"usuarios",e)),console.log("Rollback realizado - novo usu\xe1rio deletado")}catch(e){console.error("Erro no rollback:",e)}return t&&alert(`Erro ao atualizar nome de usu\xe1rio: ${a instanceof Error?a.message:"Erro desconhecido"}`),!1}},saveConfigurations=async()=>{if(!a.username){alert("Erro: usu\xe1rio n\xe3o identificado");return}try{if(b(!0),v.username!==a.username){let e=await updateUsername(v.username,!1);if(!e)return}let e=v.username!==a.username?v.username:a.username,t={aparencia:{fonte:j.fonte,tamanhoFonte:j.tamanhoFonte,palavrasPorSessao:j.palavrasPorSessao},endpoints:{},memorias:{},categorias:{},updatedAt:new Date().toISOString()};N.forEach((e,a)=>{t.endpoints[e.nome||`endpoint_${a}`]=e}),C.forEach((e,a)=>{t.memorias[`memoria_${a}`]=e}),S.forEach((e,a)=>{t.categorias[e.nome||`categoria_${a}`]=e}),console.log("Salvando configura\xe7\xf5es para:",e),console.log("Dados a serem salvos:",t);let r=(0,d.doc)(i.db,"usuarios",e,"configuracoes","settings");await (0,d.pl)(r,t),console.log("Configura\xe7\xf5es salvas com sucesso no Firestore"),alert("Configura\xe7\xf5es salvas com sucesso!")}catch(e){console.error("Erro ao salvar configura\xe7\xf5es:",e),alert(`Erro ao salvar configura\xe7\xf5es: ${e instanceof Error?e.message:"Erro desconhecido"}`)}finally{b(!1)}};if(!e)return null;let handleProfileImageUpload=async e=>{if(h)try{b(!0);let t=(0,c.iH)(i.tO,`usuarios/${a.username}/profile.jpg`);await (0,c.KV)(t,e);let r=await (0,c.Jt)(t);w(e=>({...e,profileImage:r}));let s=(0,d.doc)(i.db,"usuarios",a.username);await (0,d.r7)(s,{profileImage:r}),o({...a,profileImage:r}),alert("Foto de perfil atualizada com sucesso!")}catch(e){console.error("Erro ao fazer upload da imagem:",e),alert("Erro ao atualizar foto de perfil.")}finally{b(!1)}},handlePasswordChange=async()=>{if(!h||!v.currentPassword||!v.newPassword){alert("Preencha todos os campos de senha.");return}if(v.newPassword!==v.confirmPassword){alert("As senhas n\xe3o coincidem.");return}try{b(!0);let e=W.w9.credential(h.email,v.currentPassword);await (0,W.aF)(h,e),await (0,W.gQ)(h,v.newPassword),w(e=>({...e,currentPassword:"",newPassword:"",confirmPassword:""})),alert("Senha alterada com sucesso!")}catch(e){console.error("Erro ao alterar senha:",e),alert("Erro ao alterar senha. Verifique a senha atual.")}finally{b(!1)}},handleLogout=async()=>{confirm("Tem certeza que deseja sair?")&&(await l(),t())},handleToggleEndpoint=e=>{k(t=>t.map((t,a)=>a===e?{...t,ativo:!t.ativo}:t))},handleDeleteEndpoint=e=>{confirm("Tem certeza que deseja deletar este endpoint?")&&k(t=>t.filter((t,a)=>a!==e))},handleEditEndpoint=e=>{let t=N[e];K({...t}),q(e)},handleSaveEditEndpoint=()=>{if(null!==H){if(!V.apiKey||!V.modeloPadrao){alert("API Key e Modelo Padr\xe3o s\xe3o obrigat\xf3rios.");return}k(e=>e.map((e,t)=>t===H?{...V}:e)),q(null),K({nome:"",url:"",apiKey:"",modeloPadrao:"",ativo:!1}),alert("Endpoint atualizado com sucesso!")}},handleCancelEditEndpoint=()=>{q(null),K({nome:"",url:"",apiKey:"",modeloPadrao:"",ativo:!1})},handleTestEndpoint=async e=>{if(!e.apiKey){alert("API Key \xe9 necess\xe1ria para testar o endpoint.");return}try{b(!0);let t=await fetch(e.url,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e.apiKey}`},body:JSON.stringify({model:e.modeloPadrao||"gpt-3.5-turbo",messages:[{role:"user",content:"Test message"}],max_tokens:10})});t.ok?alert("✅ Endpoint testado com sucesso!"):alert("❌ Erro ao testar endpoint. Verifique as configura\xe7\xf5es.")}catch(e){console.error("Erro ao testar endpoint:",e),alert("❌ Erro ao conectar com o endpoint.")}finally{b(!1)}},handleDeleteMemory=e=>{confirm("Tem certeza que deseja deletar esta mem\xf3ria?")&&M(t=>t.filter((t,a)=>a!==e))},handleDeleteCategory=e=>{confirm("Tem certeza que deseja deletar esta categoria?")&&D(t=>t.filter((t,a)=>a!==e))},G=["#3B82F6","#EF4444","#10B981","#F59E0B","#8B5CF6","#EC4899","#06B6D4","#84CC16"];return r.jsx(u.M,{children:e&&r.jsx(m.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,r.jsxs)(m.E.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.95,opacity:0},className:"bg-gradient-to-br from-blue-900/95 to-blue-800/95 backdrop-blur-sm border border-white/20 rounded-2xl w-full max-w-6xl max-h-[95vh] overflow-hidden mx-4 lg:mx-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 lg:p-6 border-b border-white/20",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl lg:text-2xl font-bold text-white",children:"Configura\xe7\xf5es"}),(0,r.jsxs)("p",{className:"text-white/60 text-sm lg:hidden mt-1",children:["geral"===x&&"Informa\xe7\xf5es pessoais e senha","aparencia"===x&&"Personaliza\xe7\xe3o da interface","ia"===x&&"Endpoints de intelig\xeancia artificial","memoria"===x&&"Sistema de mem\xf3rias"]})]}),r.jsx("button",{onClick:t,className:"text-white/60 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg",children:r.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row",children:[r.jsx("div",{className:"w-full lg:w-64 bg-white/5 border-b lg:border-b-0 lg:border-r border-white/10",children:r.jsx("nav",{className:"p-2 lg:p-4 space-y-1 lg:space-y-2 overflow-x-auto lg:overflow-x-visible",children:(0,r.jsxs)("div",{className:"flex lg:flex-col space-x-2 lg:space-x-0 lg:space-y-2 min-w-max lg:min-w-0",children:[(0,r.jsxs)("button",{onClick:()=>p("geral"),className:`w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap ${"geral"===x?"bg-blue-600 text-white shadow-lg":"text-white/70 hover:text-white hover:bg-white/10"}`,children:[r.jsx("svg",{className:"w-4 lg:w-5 h-4 lg:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),r.jsx("span",{className:"font-medium text-sm lg:text-base",children:"Geral"})]}),(0,r.jsxs)("button",{onClick:()=>p("aparencia"),className:`w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap ${"aparencia"===x?"bg-blue-600 text-white shadow-lg":"text-white/70 hover:text-white hover:bg-white/10"}`,children:[r.jsx("svg",{className:"w-4 lg:w-5 h-4 lg:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"})}),r.jsx("span",{className:"font-medium text-sm lg:text-base",children:"Apar\xeancia"})]}),(0,r.jsxs)("button",{onClick:()=>p("ia"),className:`w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap ${"ia"===x?"bg-blue-600 text-white shadow-lg":"text-white/70 hover:text-white hover:bg-white/10"}`,children:[r.jsx("svg",{className:"w-4 lg:w-5 h-4 lg:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),r.jsx("span",{className:"font-medium text-sm lg:text-base",children:"IA"})]}),(0,r.jsxs)("button",{onClick:()=>p("memoria"),className:`w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap ${"memoria"===x?"bg-blue-600 text-white shadow-lg":"text-white/70 hover:text-white hover:bg-white/10"}`,children:[r.jsx("svg",{className:"w-4 lg:w-5 h-4 lg:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),r.jsx("span",{className:"font-medium text-sm lg:text-base",children:"Mem\xf3ria"})]})]})})}),r.jsx("div",{className:"flex-1 p-4 lg:p-6 overflow-y-auto max-h-[calc(95vh-200px)]",children:(0,r.jsxs)(u.M,{mode:"wait",children:["geral"===x&&r.jsx(m.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-8",children:(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-2xl font-bold text-white mb-6",children:"Configura\xe7\xf5es Gerais"}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[r.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Foto de Perfil"}),(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[r.jsx("div",{className:"relative",children:v.profileImage?r.jsx("img",{src:v.profileImage,alt:"Profile",className:"w-20 h-20 rounded-full object-cover border-2 border-white/20"}):r.jsx("div",{className:"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center",children:r.jsx("span",{className:"text-white font-bold text-2xl",children:a.username.charAt(0).toUpperCase()})})}),(0,r.jsxs)("div",{children:[r.jsx("button",{onClick:()=>f.current?.click(),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium",children:"Alterar Foto"}),r.jsx("p",{className:"text-white/60 text-sm mt-2",children:"JPG, PNG ou GIF. M\xe1ximo 5MB."})]})]}),r.jsx("input",{ref:f,type:"file",accept:"image/*",onChange:e=>{let t=e.target.files?.[0];t&&handleProfileImageUpload(t)},className:"hidden"})]}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[r.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Nome de Usu\xe1rio"}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("input",{type:"text",value:v.username,onChange:e=>w(t=>({...t,username:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Digite seu nome de usu\xe1rio"}),v.username!==a.username&&r.jsx("div",{className:"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3",children:r.jsx("p",{className:"text-yellow-300 text-sm",children:'⚠️ Nome de usu\xe1rio alterado. Clique em "Salvar Configura\xe7\xf5es" para aplicar as mudan\xe7as.'})})]})]}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6",children:[r.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Alterar Senha"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Senha Atual"}),r.jsx("input",{type:"password",value:v.currentPassword,onChange:e=>w(t=>({...t,currentPassword:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Digite sua senha atual"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Nova Senha"}),r.jsx("input",{type:"password",value:v.newPassword,onChange:e=>w(t=>({...t,newPassword:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Digite sua nova senha"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Confirmar Nova Senha"}),r.jsx("input",{type:"password",value:v.confirmPassword,onChange:e=>w(t=>({...t,confirmPassword:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Confirme sua nova senha"})]}),r.jsx("button",{onClick:handlePasswordChange,disabled:g,className:"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium",children:g?"Alterando...":"Alterar Senha"})]})]})]})},"geral"),"aparencia"===x&&r.jsx(m.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-8",children:(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-2xl font-bold text-white mb-6",children:"Configura\xe7\xf5es de Apar\xeancia"}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[r.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Fonte do Chat"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Fam\xedlia da Fonte"}),(0,r.jsxs)("select",{value:j.fonte,onChange:e=>y(t=>({...t,fonte:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[r.jsx("option",{value:"Inter",className:"bg-gray-800",children:"Inter"}),r.jsx("option",{value:"Roboto",className:"bg-gray-800",children:"Roboto"}),r.jsx("option",{value:"JetBrains Mono",className:"bg-gray-800",children:"JetBrains Mono"}),r.jsx("option",{value:"Lato",className:"bg-gray-800",children:"Lato"}),r.jsx("option",{value:"Fira Code",className:"bg-gray-800",children:"Fira Code"}),r.jsx("option",{value:"Merriweather",className:"bg-gray-800",children:"Merriweather"}),r.jsx("option",{value:"Open Sans",className:"bg-gray-800",children:"Open Sans"}),r.jsx("option",{value:"Source Sans Pro",className:"bg-gray-800",children:"Source Sans Pro"}),r.jsx("option",{value:"Poppins",className:"bg-gray-800",children:"Poppins"}),r.jsx("option",{value:"Nunito",className:"bg-gray-800",children:"Nunito"})]})]}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-lg p-4",children:[r.jsx("p",{className:"text-white/80 text-sm mb-2",children:"Pr\xe9-visualiza\xe7\xe3o:"}),r.jsx("div",{className:"text-white p-3 bg-white/5 rounded border border-white/10",style:{fontFamily:j.fonte,fontSize:`${j.tamanhoFonte}px`},children:"Esta \xe9 uma mensagem de exemplo para visualizar a fonte selecionada. Lorem ipsum dolor sit amet, consectetur adipiscing elit."})]})]})]}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[r.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Tamanho da Fonte"}),r.jsx("div",{className:"space-y-4",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:["Tamanho: ",j.tamanhoFonte,"px"]}),r.jsx("input",{type:"range",min:"10",max:"24",value:j.tamanhoFonte,onChange:e=>y(t=>({...t,tamanhoFonte:parseInt(e.target.value)})),className:"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-white/60 mt-1",children:[r.jsx("span",{children:"10px"}),r.jsx("span",{children:"24px"})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6",children:[r.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Sess\xf5es de Chat"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h5",{className:"text-white font-medium",children:"Divis\xe3o Autom\xe1tica"}),r.jsx("p",{className:"text-white/60 text-sm",children:"Dividir chats longos em sess\xf5es baseadas na contagem de palavras"})]}),r.jsx("button",{className:"bg-blue-600 relative inline-flex h-6 w-11 items-center rounded-full transition-colors",children:r.jsx("span",{className:"translate-x-6 inline-block h-4 w-4 transform rounded-full bg-white transition"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:["Palavras por Sess\xe3o: ",j.palavrasPorSessao.toLocaleString()]}),r.jsx("input",{type:"range",min:"1000",max:"20000",step:"500",value:j.palavrasPorSessao,onChange:e=>y(t=>({...t,palavrasPorSessao:parseInt(e.target.value)})),className:"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-white/60 mt-1",children:[r.jsx("span",{children:"1.000"}),r.jsx("span",{children:"20.000"})]})]}),r.jsx("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3",children:(0,r.jsxs)("p",{className:"text-blue-300 text-sm",children:["\uD83D\uDCA1 ",r.jsx("strong",{children:"Dica:"})," Sess\xf5es menores carregam mais r\xe1pido, mas podem fragmentar conversas longas. Recomendamos entre 3.000-8.000 palavras para melhor experi\xeancia."]})})]})]})]})},"aparencia"),"ia"===x&&r.jsx(m.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-8",children:(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-2xl font-bold text-white mb-6",children:"Intelig\xeancia Artificial"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("p",{className:"text-white/80",children:"Gerencie seus endpoints de IA personalizados"}),(0,r.jsxs)("button",{onClick:()=>O(!R),className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2",children:[r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),r.jsx("span",{children:"Adicionar Endpoint"})]})]}),R&&(0,r.jsxs)(m.E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[r.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Novo Endpoint"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Nome do Endpoint *"}),r.jsx("input",{type:"text",value:U.nome,onChange:e=>_(t=>({...t,nome:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ex: Meu Endpoint"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"URL do Endpoint *"}),r.jsx("input",{type:"url",value:U.url,onChange:e=>_(t=>({...t,url:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"https://api.exemplo.com/v1/chat/completions"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"API Key *"}),r.jsx("input",{type:"password",value:U.apiKey,onChange:e=>_(t=>({...t,apiKey:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"sk-..."})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Modelo Padr\xe3o"}),r.jsx("input",{type:"text",value:U.modeloPadrao,onChange:e=>_(t=>({...t,modeloPadrao:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"gpt-3.5-turbo"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-4",children:[r.jsx("button",{onClick:()=>O(!1),className:"px-4 py-2 text-white/70 hover:text-white transition-colors",children:"Cancelar"}),r.jsx("button",{onClick:()=>{if(!U.nome||!U.url||!U.apiKey){alert("Preencha todos os campos obrigat\xf3rios.");return}k(e=>[...e,{...U}]),_({nome:"",url:"",apiKey:"",modeloPadrao:"",ativo:!1}),O(!1),alert("Endpoint adicionado com sucesso!")},className:"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium",children:"Adicionar"})]})]}),r.jsx("div",{className:"space-y-4",children:N.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:`w-3 h-3 rounded-full ${e.ativo?"bg-green-500":"bg-gray-500"}`}),r.jsx("h4",{className:"text-lg font-semibold text-white",children:e.nome}),("OpenRouter"===e.nome||"DeepSeek"===e.nome)&&r.jsx("span",{className:"bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full",children:"Pr\xe9-configurado"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("button",{onClick:()=>handleToggleEndpoint(t),className:`px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200 ${e.ativo?"bg-green-600 hover:bg-green-700 text-white":"bg-gray-600 hover:bg-gray-700 text-white"}`,children:e.ativo?"Ativo":"Inativo"}),r.jsx("button",{onClick:()=>handleTestEndpoint(e),disabled:g||!e.apiKey,className:"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200",children:"Testar"}),r.jsx("button",{onClick:()=>handleEditEndpoint(t),className:"bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200",children:"Editar"}),"OpenRouter"!==e.nome&&"DeepSeek"!==e.nome&&r.jsx("button",{onClick:()=>handleDeleteEndpoint(t),className:"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200",children:"Deletar"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[r.jsx("span",{className:"text-white/60",children:"URL:"}),r.jsx("p",{className:"text-white font-mono text-xs break-all",children:e.url})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"text-white/60",children:"Modelo:"}),r.jsx("p",{className:"text-white",children:e.modeloPadrao||"N\xe3o especificado"})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"text-white/60",children:"API Key:"}),r.jsx("p",{className:"text-white font-mono text-xs",children:e.apiKey?"••••••••••••"+e.apiKey.slice(-4):"N\xe3o configurada"})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"text-white/60",children:"Status:"}),r.jsx("p",{className:`font-medium ${e.ativo?"text-green-400":"text-gray-400"}`,children:e.ativo?"Ativo":"Inativo"})]})]}),H===t&&(0,r.jsxs)(m.E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"mt-4 pt-4 border-t border-white/10",children:[r.jsx("h5",{className:"text-white font-semibold mb-4",children:"Editar Endpoint"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"API Key *"}),r.jsx("input",{type:"password",value:V.apiKey,onChange:e=>K(t=>({...t,apiKey:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",placeholder:"Cole sua API Key aqui..."})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Modelo Padr\xe3o *"}),r.jsx("input",{type:"text",value:V.modeloPadrao,onChange:e=>K(t=>({...t,modeloPadrao:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",placeholder:"Ex: gpt-4, claude-3-sonnet, etc."})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2 mt-4",children:[r.jsx("button",{onClick:handleCancelEditEndpoint,className:"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",children:"Cancelar"}),r.jsx("button",{onClick:handleSaveEditEndpoint,className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",children:"Salvar"})]})]}),("OpenRouter"===e.nome||"DeepSeek"===e.nome)&&!e.apiKey&&H!==t&&(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t border-white/10",children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Configure sua API Key:"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("input",{type:"password",placeholder:"Cole sua API Key aqui...",className:"flex-1 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",onChange:e=>{let a=e.target.value;k(e=>e.map((e,r)=>r===t?{...e,apiKey:a}:e))}}),r.jsx("button",{onClick:()=>handleToggleEndpoint(t),className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",children:"Salvar"})]})]})]},t))})]})},"ia"),"memoria"===x&&r.jsx(m.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-8",children:(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-2xl font-bold text-white mb-6",children:"Sistema de Mem\xf3ria"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-3 mb-6",children:[(0,r.jsxs)("button",{onClick:()=>T(!I),className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2",children:[r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),r.jsx("span",{children:"Nova Categoria"})]}),(0,r.jsxs)("button",{onClick:()=>F(!A),className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2",children:[r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),r.jsx("span",{children:"Nova Mem\xf3ria"})]})]}),I&&(0,r.jsxs)(m.E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[r.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Nova Categoria"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Nome da Categoria *"}),r.jsx("input",{type:"text",value:B.nome,onChange:e=>$(t=>({...t,nome:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ex: Trabalho, Pessoal, Projetos..."})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Descri\xe7\xe3o"}),r.jsx("textarea",{value:B.descricao,onChange:e=>$(t=>({...t,descricao:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:3,placeholder:"Descreva o prop\xf3sito desta categoria..."})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Cor da Categoria"}),r.jsx("div",{className:"flex space-x-2",children:G.map(e=>r.jsx("button",{onClick:()=>$(t=>({...t,cor:e})),className:`w-8 h-8 rounded-full border-2 transition-all duration-200 ${B.cor===e?"border-white scale-110":"border-white/30"}`,style:{backgroundColor:e}},e))})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-4",children:[r.jsx("button",{onClick:()=>T(!1),className:"px-4 py-2 text-white/70 hover:text-white transition-colors",children:"Cancelar"}),r.jsx("button",{onClick:()=>{if(!B.nome){alert("Nome da categoria \xe9 obrigat\xf3rio.");return}D(e=>[...e,{...B}]),$({nome:"",descricao:"",cor:"#3B82F6"}),T(!1),alert("Categoria criada com sucesso!")},className:"bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium",children:"Criar Categoria"})]})]}),A&&(0,r.jsxs)(m.E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[r.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Nova Mem\xf3ria"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"T\xedtulo da Mem\xf3ria *"}),r.jsx("input",{type:"text",value:P.titulo,onChange:e=>z(t=>({...t,titulo:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ex: Informa\xe7\xf5es importantes sobre..."})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Conte\xfado *"}),r.jsx("textarea",{value:P.conteudo,onChange:e=>z(t=>({...t,conteudo:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:4,placeholder:"Digite o conte\xfado da mem\xf3ria..."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Categoria"}),(0,r.jsxs)("select",{value:P.categoria||"",onChange:e=>z(t=>({...t,categoria:e.target.value||null})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[r.jsx("option",{value:"",className:"bg-gray-800",children:"Sem categoria"}),S.map((e,t)=>r.jsx("option",{value:e.nome,className:"bg-gray-800",children:e.nome},t))]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Cor da Mem\xf3ria"}),r.jsx("div",{className:"flex space-x-2",children:G.slice(0,4).map(e=>r.jsx("button",{onClick:()=>z(t=>({...t,cor:e})),className:`w-8 h-8 rounded-full border-2 transition-all duration-200 ${P.cor===e?"border-white scale-110":"border-white/30"}`,style:{backgroundColor:e}},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx("label",{className:"block text-white/80 text-sm font-medium",children:"Escopo da Mem\xf3ria"}),(0,r.jsxs)("select",{value:P.global?"global":P.chatId||"",onChange:e=>{let t=e.target.value;"global"===t?z(e=>({...e,global:!0,chatId:null})):z(e=>({...e,global:!1,chatId:t}))},className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[r.jsx("option",{value:"global",className:"bg-gray-800 text-white",children:"\uD83C\uDF10 Global (todos os chats)"}),r.jsx("optgroup",{label:"Chats Espec\xedficos",className:"bg-gray-800",children:E.map(e=>(0,r.jsxs)("option",{value:e.id,className:"bg-gray-800 text-white",children:["\uD83D\uDCAC ",e.name]},e.id))})]}),r.jsx("p",{className:"text-white/50 text-xs",children:P.global?"Esta mem\xf3ria ficar\xe1 dispon\xedvel em todos os chats":"Esta mem\xf3ria ficar\xe1 dispon\xedvel apenas no chat selecionado"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-4",children:[r.jsx("button",{onClick:()=>F(!1),className:"px-4 py-2 text-white/70 hover:text-white transition-colors",children:"Cancelar"}),r.jsx("button",{onClick:()=>{if(!P.titulo||!P.conteudo){alert("T\xedtulo e conte\xfado s\xe3o obrigat\xf3rios.");return}M(e=>[...e,{...P}]),z({titulo:"",conteudo:"",cor:"#3B82F6",categoria:null,chatId:null,global:!0}),F(!1),alert("Mem\xf3ria criada com sucesso!")},className:"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium",children:"Criar Mem\xf3ria"})]})]}),S.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Categorias"}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:S.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:e.cor}}),r.jsx("h5",{className:"text-white font-medium",children:e.nome})]}),r.jsx("button",{onClick:()=>handleDeleteCategory(t),className:"text-red-400 hover:text-red-300 transition-colors",children:r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]}),e.descricao&&r.jsx("p",{className:"text-white/60 text-sm",children:e.descricao})]},t))})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"text-lg font-semibold text-white mb-4",children:["Mem\xf3rias (",C.length,")"]}),0===C.length?(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-8 text-center",children:[r.jsx("svg",{className:"w-12 h-12 text-white/40 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),r.jsx("p",{className:"text-white/60",children:"Nenhuma mem\xf3ria criada ainda"}),r.jsx("p",{className:"text-white/40 text-sm mt-1",children:'Clique em "Nova Mem\xf3ria" para come\xe7ar'})]}):r.jsx("div",{className:"space-y-4",children:C.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-4 h-4 rounded-full flex-shrink-0",style:{backgroundColor:e.cor}}),(0,r.jsxs)("div",{children:[r.jsx("h5",{className:"text-white font-semibold",children:e.titulo}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[e.categoria&&r.jsx("span",{className:"bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full",children:e.categoria}),r.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${e.global?"bg-green-500/20 text-green-300":"bg-orange-500/20 text-orange-300"}`,children:e.global?"\uD83C\uDF10 Global":`💬 ${E.find(t=>t.id===e.chatId)?.name||"Chat Espec\xedfico"}`})]})]})]}),r.jsx("button",{onClick:()=>handleDeleteMemory(t),className:"text-red-400 hover:text-red-300 transition-colors",children:r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]}),r.jsx("p",{className:"text-white/80 text-sm leading-relaxed",children:e.conteudo})]},t))})]})]})},"memoria")]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-t border-white/20",children:[r.jsx("div",{className:"flex items-center space-x-3",children:r.jsx("button",{onClick:handleLogout,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium",children:"Sair da Conta"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("button",{onClick:t,className:"px-6 py-2 text-white/70 hover:text-white transition-colors font-medium",children:"Cancelar"}),r.jsx("button",{onClick:saveConfigurations,disabled:g,className:"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium",children:g?"Salvando...":"Salvar Configura\xe7\xf5es"})]})]})]})})})}function Dashboard(){let{user:e,loading:t}=(0,n.useAuth)(),o=(0,l.useRouter)(),[d,c]=(0,s.useState)(null),[m,u]=(0,s.useState)(!0),[h,p]=(0,s.useState)(!1),[g,b]=(0,s.useState)(!1),[f,v]=(0,s.useState)(!1),[w,j]=(0,s.useState)(null),y=(0,s.useRef)(null);return((0,s.useEffect)(()=>{t||e||o.push("/login")},[e,t,o]),(0,s.useEffect)(()=>{let fetchUserData=async()=>{if(e)try{let{collection:t,query:r,where:s,getDocs:o}=await Promise.resolve().then(a.bind(a,29904)),n=t(i.db,"usuarios"),l=r(n,s("email","==",e.email)),d=await o(l);if(d.empty)c({username:e.email?.split("@")[0]||"Usu\xe1rio",email:e.email||"",balance:0,createdAt:new Date().toISOString()});else{let t=d.docs[0],a=t.data();c({username:a.username||e.email?.split("@")[0]||"Usu\xe1rio",email:a.email||e.email||"",balance:a.balance||0,createdAt:a.createdAt||new Date().toISOString()})}}catch(t){console.error("Erro ao buscar dados do usu\xe1rio:",t),c({username:e.email?.split("@")[0]||"Usu\xe1rio",email:e.email||"",balance:0,createdAt:new Date().toISOString()})}finally{u(!1)}};e&&!t&&fetchUserData()},[e,t]),t||m)?r.jsx("div",{className:"min-h-screen bg-gradient-rafthor flex items-center justify-center",children:r.jsx("div",{className:"text-white text-xl",children:"Carregando..."})}):e&&d?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-rafthor flex",children:[r.jsx(x,{ref:y,userData:d,isOpen:h,isCollapsed:g,onToggle:()=>{window.innerWidth<1024?p(!h):b(!g)},onSettingsOpen:()=>v(!0),onChatSelect:j,currentChat:w,showCloseButton:!0}),(0,r.jsxs)("div",{className:`flex-1 flex flex-col h-screen overflow-hidden transition-all duration-300 ${g?"lg:ml-0":"lg:ml-80"}`,children:[r.jsx("div",{className:"lg:hidden bg-white/10 backdrop-blur-sm border-b border-white/20 p-4",children:r.jsx("button",{onClick:()=>p(!0),className:"text-white hover:text-white/80 transition-colors",children:r.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}),r.jsx(ChatArea,{currentChat:w,onChatCreated:e=>{j(e),y.current?.reloadChats()},onUpdateOpenRouterBalance:()=>{y.current&&y.current.updateOpenRouterBalance()}})]}),r.jsx(SettingsModal,{isOpen:f,onClose:()=>v(!1),userData:d,onUserDataUpdate:c}),h&&r.jsx("div",{className:"lg:hidden fixed inset-0 bg-black/50 z-40",onClick:()=>p(!1)})]}):null}},42610:(e,t,a)=>{"use strict";a.d(t,{default:()=>n});var r=a(31640),s=a(72373);let o=new class{validateFile(e){if(e.size>this.MAX_FILE_SIZE)return{isValid:!1,error:"Arquivo muito grande. M\xe1ximo permitido: 10MB"};let t=this.SUPPORTED_IMAGE_TYPES.includes(e.type),a=e.type===this.SUPPORTED_PDF_TYPE;return t||a?{isValid:!0}:{isValid:!1,error:"Tipo de arquivo n\xe3o suportado. Use PNG, JPEG, WebP ou PDF"}}async fileToBase64(e){return new Promise((t,a)=>{let r=new FileReader;r.onload=()=>{let e=r.result,a=e.split(",")[1];t(a)},r.onerror=a,r.readAsDataURL(e)})}generateAttachmentId(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}async uploadAttachment(e){let{file:t,username:a,chatId:o}=e,n=this.validateFile(t);if(!n.isValid)throw Error(n.error);try{let e;let n=this.generateAttachmentId(),l=this.SUPPORTED_IMAGE_TYPES.includes(t.type),i=l?"image":"pdf",d=`usuarios/${a}/conversas/${o}/anexos/${n}_${t.name}`,c=(0,r.iH)(s.tO,d);await (0,r.KV)(c,t);let m=await (0,r.Jt)(c),u={id:n,type:i,filename:t.name,url:m,size:t.size,uploadedAt:Date.now(),storagePath:d,isActive:!0};return"pdf"===i&&(e=await this.fileToBase64(t),u.base64Data=e),{metadata:u,base64Data:e}}catch(e){throw console.error("Erro ao fazer upload do anexo:",e),Error("Falha no upload do arquivo. Tente novamente.")}}async uploadMultipleAttachments(e,t,a){let r=[];for(let s of e)try{let e=await this.uploadAttachment({file:s,username:t,chatId:a});r.push(e)}catch(e){console.error(`Erro ao processar arquivo ${s.name}:`,e)}return r}prepareAttachmentsForOpenRouter(e){let t=[];for(let a of e)if("image"===a.type)t.push({type:"image_url",image_url:{url:a.url}});else if("pdf"===a.type&&a.base64Data){let e=`data:application/pdf;base64,${a.base64Data}`;t.push({type:"file",file:{filename:a.filename,file_data:e}})}return t}preparePDFPlugins(e){let t=e.some(e=>"pdf"===e.type);return t?[{id:"file-parser",pdf:{engine:"mistral-ocr"}}]:[]}async cleanupTemporaryAttachments(e){console.log("Cleanup de anexos tempor\xe1rios:",e.length)}constructor(){this.MAX_FILE_SIZE=10485760,this.SUPPORTED_IMAGE_TYPES=["image/png","image/jpeg","image/webp"],this.SUPPORTED_PDF_TYPE="application/pdf"}},n=o},42918:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>n,__esModule:()=>o,default:()=>i});var r=a(95153);let s=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\SiteRafthor\rafthor\src\app\dashboard\page.tsx`),{__esModule:o,$$typeof:n}=s,l=s.default,i=l},18172:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[332,855,698],()=>__webpack_exec__(81852));module.exports=a})();