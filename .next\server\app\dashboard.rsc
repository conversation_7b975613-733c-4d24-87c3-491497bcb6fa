1:HL["/_next/static/css/fd86b0f4f492ef19.css","style",{"crossOrigin":""}]
0:["vCEnXNQq-hiW9sMtdept3",[[["",{"children":["dashboard",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],"$L2",[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/fd86b0f4f492ef19.css","precedence":"next","crossOrigin":""}]],"$L3"]]]]
4:HL["/_next/static/css/027be9f4bf311447.css","style",{"crossOrigin":""}]
5:I[3549,["609","static/chunks/7508b87c-ffc4fff5fd435c06.js","15","static/chunks/261b60bd-f7f6f74e679d6a91.js","14","static/chunks/14-51e4ad3e2a6b4d22.js","185","static/chunks/app/layout-c8afc13170522ef0.js"],"AuthProvider"]
6:I[6954,[],""]
7:I[7264,[],""]
9:I[8297,[],""]
a:I[8329,["609","static/chunks/7508b87c-ffc4fff5fd435c06.js","15","static/chunks/261b60bd-f7f6f74e679d6a91.js","954","static/chunks/d3ac728e-9e72e1d80e89da72.js","14","static/chunks/14-51e4ad3e2a6b4d22.js","933","static/chunks/933-f3010743e2abf4fb.js","702","static/chunks/app/dashboard/page-31453819721b447d.js"],""]
2:[null,["$","html",null,{"lang":"pt-BR","children":["$","body",null,{"className":"__className_e8ce0c","children":["$","$L5",null,{"children":["$","$L6",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","template":["$","$L7",null,{}],"templateStyles":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[],"childProp":{"current":["$","$L6",null,{"parallelRouterKey":"children","segmentPath":["children","dashboard","children"],"loading":"$undefined","loadingStyles":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","template":["$","$L7",null,{}],"templateStyles":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","childProp":{"current":["$L8",["$","$L9",null,{"propsForComponent":{"params":{}},"Component":"$a","isStaticGeneration":true}],null],"segment":"__PAGE__"},"styles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/027be9f4bf311447.css","precedence":"next","crossOrigin":""}]]}],"segment":"dashboard"},"styles":[]}]}]}]}],null]
3:[["$","meta","0",{"charSet":"utf-8"}],["$","title","1",{"children":"Rafthor - AI Chatbot Platform"}],["$","meta","2",{"name":"description","content":"Uma plataforma de chatbot com múltiplas IAs"}],["$","meta","3",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
8:null
