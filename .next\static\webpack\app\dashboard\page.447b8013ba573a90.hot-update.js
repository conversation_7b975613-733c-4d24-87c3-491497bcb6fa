"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Upperbar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/Upperbar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Upperbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_SessionContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/SessionContext */ \"(app-pages-browser)/./src/contexts/SessionContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Hook para tempo de sessão\nconst useSessionTime = (chatId)=>{\n    _s();\n    const [sessionTime, setSessionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0s\");\n    const [startTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Função para formatar tempo de forma inteligente\n    const formatTime = (timeInMs)=>{\n        const totalSeconds = Math.floor(timeInMs / 1000);\n        const totalMinutes = Math.floor(totalSeconds / 60);\n        const totalHours = Math.floor(totalMinutes / 60);\n        const totalDays = Math.floor(totalHours / 24);\n        const totalMonths = Math.floor(totalDays / 30);\n        const seconds = totalSeconds % 60;\n        const minutes = totalMinutes % 60;\n        const hours = totalHours % 24;\n        const days = totalDays % 30;\n        // Menos de 1 minuto: apenas segundos\n        if (totalMinutes === 0) {\n            return \"\".concat(totalSeconds, \"s\");\n        }\n        // Menos de 1 hora: MM:SS\n        if (totalHours === 0) {\n            return \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        // Menos de 1 dia: HH:MM:SS\n        if (totalDays === 0) {\n            return \"\".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        // Menos de 1 mês: Xd HH:MM\n        if (totalMonths === 0) {\n            return \"\".concat(days, \"d \").concat(hours.toString().padStart(2, \"0\"), \":\").concat(minutes.toString().padStart(2, \"0\"));\n        }\n        // 1 mês ou mais: Xm Xd HH:MM\n        return \"\".concat(totalMonths, \"m \").concat(days, \"d \").concat(hours.toString().padStart(2, \"0\"), \":\").concat(minutes.toString().padStart(2, \"0\"));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (chatId) {\n            intervalRef.current = setInterval(()=>{\n                const elapsed = Date.now() - startTime;\n                const formattedTime = formatTime(elapsed);\n                setSessionTime(formattedTime);\n            }, 1000);\n        }\n        return ()=>{\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n                intervalRef.current = null;\n            }\n        };\n    }, [\n        chatId,\n        startTime\n    ]);\n    return sessionTime;\n};\n_s(useSessionTime, \"D9h1eTTjB/UJUr/8kCtwXx6uawQ=\");\nfunction Upperbar(param) {\n    let { currentChat, chatName, aiModel, onDownload, onAttachments, onStatistics, isLoading = false, attachmentsCount = 0, aiMetadata } = param;\n    _s1();\n    const { sessionTime, isSessionActive } = (0,_contexts_SessionContext__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const handleAttachments = ()=>{\n        if (onAttachments) {\n            onAttachments();\n        } else {\n            console.log(\"Anexos clicado\");\n        }\n    };\n    const handleStats = ()=>{\n        if (onStatistics) {\n            onStatistics();\n        } else {\n            console.log(\"Estat\\xedsticas clicado\");\n        }\n    };\n    const handleDownload = ()=>{\n        if (onDownload) {\n            onDownload();\n        } else {\n            console.log(\"Download clicado\");\n        }\n    };\n    const currentModel = aiModel || \"GPT-4.1 Nano\";\n    const displayChatName = chatName || (currentChat ? \"Chat \".concat(currentChat) : \"Nova Conversa\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-14 sm:h-16 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border-b border-blue-700/30 shadow-xl relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex items-center justify-between px-3 sm:px-4 lg:px-6 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-900/30 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-400 rounded-full shadow-lg shadow-blue-400/50 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm font-medium text-blue-100 truncate\",\n                                        children: currentModel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    (aiMetadata === null || aiMetadata === void 0 ? void 0 : aiMetadata.usedCoT) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-1.5 sm:px-2 py-0.5 sm:py-1 bg-cyan-600/20 text-cyan-300 text-xs rounded-full border border-cyan-500/30 hidden sm:block\",\n                                        children: \"CoT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-px h-4 sm:h-6 bg-blue-600/30 hidden sm:block\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1.5 sm:space-x-2 bg-blue-900/20 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 hidden sm:flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 sm:w-4 sm:h-4 text-blue-300\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm text-blue-200 font-mono\",\n                                        children: isSessionActive ? sessionTime : \"0s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-1/2 transform -translate-x-1/2 hidden sm:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2 shadow-lg max-w-xs lg:max-w-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full shadow-lg flex-shrink-0 \".concat(isLoading ? \"bg-yellow-400 shadow-yellow-400/50 animate-pulse\" : \"bg-cyan-400 shadow-cyan-400/50\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-sm sm:text-lg font-semibold text-white truncate\",\n                                    children: displayChatName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 sm:space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAttachments,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105 relative\",\n                                title: \"Anexos\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    attachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -top-1 -right-1 bg-cyan-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium shadow-lg\",\n                                        children: attachmentsCount > 99 ? \"99+\" : attachmentsCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStats,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105\",\n                                title: \"Estat\\xedsticas\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDownload,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/30 hover:scale-105 border border-blue-500/30\",\n                                title: \"Download\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s1(Upperbar, \"7umSWl/5wagY9n5lKW5UrpaIucY=\", false, function() {\n    return [\n        _contexts_SessionContext__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = Upperbar;\nvar _c;\n$RefreshReg$(_c, \"Upperbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\n"));

/***/ })

});