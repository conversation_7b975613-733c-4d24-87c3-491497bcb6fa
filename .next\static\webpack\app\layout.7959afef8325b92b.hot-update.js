/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CAppearanceContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CAuthContext.tsx&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CAppearanceContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CAuthContext.tsx&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AppearanceContext.tsx */ \"(app-pages-browser)/./src/contexts/AppearanceContext.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CAppearanceContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CAuthContext.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"48b9368aa14c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/N2ZkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQ4YjkzNjhhYTE0Y1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AppearanceContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/AppearanceContext.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppearanceProvider: function() { return /* binding */ AppearanceProvider; },\n/* harmony export */   useAppearance: function() { return /* binding */ useAppearance; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAppearance,AppearanceProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst defaultSettings = {\n    fonte: \"Inter\",\n    tamanhoFonte: 14,\n    palavrasPorSessao: 5000\n};\nconst AppearanceContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    settings: defaultSettings,\n    loading: true,\n    refreshSettings: async ()=>{}\n});\nconst useAppearance = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppearanceContext);\n    if (!context) {\n        throw new Error(\"useAppearance must be used within an AppearanceProvider\");\n    }\n    return context;\n};\n_s(useAppearance, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AppearanceProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSettings);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) throw new Error(\"Usu\\xe1rio n\\xe3o autenticado\");\n        const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n        const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n        const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n        const querySnapshot = await getDocs(q);\n        if (querySnapshot.empty) {\n            throw new Error(\"Usu\\xe1rio n\\xe3o encontrado\");\n        }\n        const userDoc = querySnapshot.docs[0];\n        const userData = userDoc.data();\n        return userData.username;\n    };\n    const loadSettings = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) {\n            setLoading(false);\n            return;\n        }\n        try {\n            const username = await getUsernameFromFirestore();\n            const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(configRef);\n            if (configDoc.exists()) {\n                const config = configDoc.data();\n                if (config.aparencia) {\n                    setSettings(config.aparencia);\n                } else {\n                    setSettings(defaultSettings);\n                }\n            } else {\n                setSettings(defaultSettings);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar configura\\xe7\\xf5es de apar\\xeancia:\", error);\n            setSettings(defaultSettings);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const refreshSettings = async ()=>{\n        setLoading(true);\n        await loadSettings();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSettings();\n    }, [\n        user\n    ]);\n    const value = {\n        settings,\n        loading,\n        refreshSettings\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppearanceContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\contexts\\\\AppearanceContext.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AppearanceProvider, \"r6/9LFxsQKNwixrIUf43J2KqRVM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = AppearanceProvider;\nvar _c;\n$RefreshReg$(_c, \"AppearanceProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AppearanceContext.tsx\n"));

/***/ })

});