(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[555],{4350:function(e,t,a){Promise.resolve().then(a.bind(a,1070))},1070:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return ImportChatPage}});var s=a(7437),r=a(2265),o=a(3549),n=a(4033),i=a(4086),l=a(5813),c=a(6831);function ImportChatPage(){let{user:e,loading:t}=(0,o.useAuth)(),a=(0,n.useRouter)(),[d,u]=(0,r.useState)(!1),[m,h]=(0,r.useState)(""),[x,p]=(0,r.useState)([]),[g,f]=(0,r.useState)(""),[b,v]=(0,r.useState)(null),getUsernameFromFirestore=async()=>{if(!(null==e?void 0:e.email))return"unknown";try{let t=(0,i.collection)(c.db,"usuarios"),a=(0,i.query)(t,(0,i.where)("email","==",e.email)),s=await (0,i.getDocs)(a);if(!s.empty){let t=s.docs[0],a=t.data();return a.username||e.email.split("@")[0]}return e.email.split("@")[0]}catch(t){return console.error("Erro ao buscar username:",t),e.email.split("@")[0]}};(0,r.useEffect)(()=>{let loadUsername=async()=>{if(null==e?void 0:e.email){let e=await getUsernameFromFirestore();f(e)}};loadUsername()},[null==e?void 0:e.email]),(0,r.useEffect)(()=>{t||e||a.push("/login")},[e,t,a]);let generateChatId=()=>Math.random().toString(36).substr(2,9)+Date.now().toString(36),importSingleChat=async e=>{try{if("application/json"!==e.type)return{filename:e.name,chatName:"",chatId:"",status:"error",message:"Arquivo deve ser JSON"};let t=await e.text(),a=JSON.parse(t),s=generateChatId(),r=new Date().toISOString(),o={context:a.context||"",createdAt:r,folderId:null,frequencyPenalty:a.frequency_penalty||1,isFixed:!1,lastUpdatedAt:r,lastUsedModel:a.lastUsedModel||"",latexInstructions:a.latexInstructions||!1,maxTokens:a.maxTokens||2048,name:a.name||"Chat Importado",password:"",repetitionPenalty:a.repetition_penalty||1,sessionTime:{lastSessionStart:null,lastUpdated:null,totalTime:0},systemPrompt:a.system_prompt||"",temperature:a.temperature||1,ultimaMensagem:a.messages.length>0?a.messages[a.messages.length-1].content.substring(0,100)+"...":"Chat importado",ultimaMensagemEm:a.messages.length>0?new Date(a.messages[a.messages.length-1].timestamp).toISOString():r,updatedAt:r};await (0,i.pl)((0,i.doc)(c.db,"usuarios",g,"conversas",s),o);let n={id:s,name:a.name||"Chat Importado",messages:a.messages.map(e=>({id:e.id,content:e.content,role:e.role,timestamp:e.timestamp,isFavorite:!1,attachments:[],...e.usage&&{usage:e.usage},...e.responseTime&&{responseTime:e.responseTime}})),createdAt:r,lastUpdated:r},d=new Blob([JSON.stringify(n,null,2)],{type:"application/json"}),u=(0,l.iH)(c.tO,"usuarios/".concat(g,"/conversas/").concat(s,"/chat.json"));return await (0,l.KV)(u,d),{filename:e.name,chatName:a.name||"Chat Importado",chatId:s,status:"success",message:"Importado com sucesso"}}catch(t){return console.error("Erro ao importar chat:",t),{filename:e.name,chatName:"",chatId:"",status:"error",message:t instanceof Error?t.message:"Erro desconhecido"}}},handleImportChats=async()=>{if(!b||0===b.length||!g)return;u(!0),p([]),h("\uD83D\uDD04 Iniciando importa\xe7\xe3o...");let e=[],t=b.length;for(let a=0;a<t;a++){let s=b[a];h("\uD83D\uDD04 Importando ".concat(a+1,"/").concat(t,": ").concat(s.name));let r=await importSingleChat(s);e.push(r),p([...e])}let a=e.filter(e=>"success"===e.status).length,s=e.filter(e=>"error"===e.status).length;h("✅ Importa\xe7\xe3o conclu\xedda! ".concat(a," sucesso(s), ").concat(s," erro(s)")),u(!1),v(null);let r=document.getElementById("chat-files");r&&(r.value="")};return t?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-rafthor flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-white text-xl",children:"Carregando..."})}):e?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-rafthor",children:(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-white mb-4",children:"Importar Chat do Rafthor Anterior"}),(0,s.jsx)("p",{className:"text-white/80 text-lg",children:"Fa\xe7a upload do arquivo chat.json para importar suas conversas"}),(0,s.jsxs)("p",{className:"text-white/60 text-sm mt-2",children:["Usu\xe1rio: ",g]})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)("svg",{className:"mx-auto h-16 w-16 text-white/60",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})})}),(0,s.jsx)("label",{htmlFor:"chat-files",className:"cursor-pointer",children:(0,s.jsx)("div",{className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors duration-200 inline-block",children:d?"Importando...":"Selecionar arquivos chat.json"})}),(0,s.jsx)("input",{id:"chat-files",type:"file",accept:".json",multiple:!0,onChange:e=>{let t=e.target.files;t&&t.length>0&&(v(t),p([]),h("\uD83D\uDCC1 ".concat(t.length,' arquivo(s) selecionado(s). Clique em "Importar" para continuar.')))},disabled:d,className:"hidden"}),(0,s.jsx)("p",{className:"text-white/60 text-sm mt-4",children:"Selecione um ou m\xfaltiplos arquivos .json exportados do Rafthor anterior"}),b&&b.length>0&&(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)("button",{onClick:handleImportChats,disabled:d,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors duration-200",children:d?"Importando...":"Importar ".concat(b.length," arquivo(s)")})})]}),m&&(0,s.jsx)("div",{className:"mt-6 p-4 bg-white/5 rounded-lg border border-white/10",children:(0,s.jsx)("p",{className:"text-white text-center",children:m})}),x.length>0&&(0,s.jsxs)("div",{className:"mt-6 space-y-2",children:[(0,s.jsx)("h4",{className:"text-white font-semibold mb-3",children:"\uD83D\uDCCA Resultados da Importa\xe7\xe3o:"}),(0,s.jsx)("div",{className:"max-h-60 overflow-y-auto space-y-2",children:x.map((e,t)=>(0,s.jsx)("div",{className:"p-3 rounded-lg border ".concat("success"===e.status?"bg-green-500/10 border-green-500/30 text-green-300":"bg-red-500/10 border-red-500/30 text-red-300"),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"font-medium",children:e.filename}),e.chatName&&(0,s.jsxs)("p",{className:"text-sm opacity-80",children:["Chat: ",e.chatName]}),(0,s.jsx)("p",{className:"text-sm opacity-80",children:e.message})]}),(0,s.jsx)("div",{className:"ml-4",children:"success"===e.status?"✅":"❌"})]})},t))})]})]}),(0,s.jsxs)("div",{className:"mt-8 bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10",children:[(0,s.jsx)("h3",{className:"text-white font-semibold mb-3",children:"\uD83D\uDCCB Instru\xe7\xf5es:"}),(0,s.jsxs)("ul",{className:"text-white/80 space-y-2 text-sm",children:[(0,s.jsx)("li",{children:"• Selecione um ou m\xfaltiplos arquivos chat.json do seu Rafthor anterior"}),(0,s.jsx)("li",{children:"• Voc\xea pode selecionar v\xe1rios arquivos de uma vez usando Ctrl+clique"}),(0,s.jsx)("li",{children:"• Cada chat ser\xe1 importado com um novo ID para evitar conflitos"}),(0,s.jsx)("li",{children:"• Todas as mensagens e configura\xe7\xf5es ser\xe3o preservadas"}),(0,s.jsx)("li",{children:"• Ap\xf3s a importa\xe7\xe3o, voc\xea pode acessar os chats no dashboard"}),(0,s.jsx)("li",{children:"• O processo mostrar\xe1 o resultado de cada arquivo individualmente"})]})]}),(0,s.jsx)("div",{className:"text-center mt-8",children:(0,s.jsx)("button",{onClick:()=>a.push("/dashboard"),className:"bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/30",children:"← Voltar ao Dashboard"})})]})})}):null}},3549:function(e,t,a){"use strict";a.r(t),a.d(t,{AuthProvider:function(){return AuthProvider},useAuth:function(){return useAuth}});var s=a(7437),r=a(2265),o=a(8081),n=a(6831);let i=(0,r.createContext)({user:null,loading:!0,logout:async()=>{}}),useAuth=()=>{let e=(0,r.useContext)(i);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},AuthProvider=e=>{let{children:t}=e,[a,l]=(0,r.useState)(null),[c,d]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,o.Aj)(n.I8,e=>{l(e),d(!1)});return()=>e()},[]);let logout=async()=>{try{await (0,o.w7)(n.I8)}catch(e){console.error("Erro ao fazer logout:",e)}};return(0,s.jsx)(i.Provider,{value:{user:a,loading:c,logout},children:t})}},6831:function(e,t,a){"use strict";a.d(t,{I8:function(){return d},db:function(){return u},tO:function(){return m}});var s=a(994),r=a(8081),o=a(4086),n=a(5813),i=a(3216);let l={apiKey:"AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8",authDomain:"rafthor-0001.firebaseapp.com",projectId:"rafthor-0001",storageBucket:"rafthor-0001.firebasestorage.app",messagingSenderId:"863587500028",appId:"1:863587500028:web:ea161ddd3a1a024a7f3c79"};if(!l.apiKey||l.apiKey.length<30)throw Error("Firebase API Key inv\xe1lida ou n\xe3o configurada");let c=(0,s.ZF)(l),d=(0,r.v0)(c),u=(0,o.ad)(c),m=(0,n.cF)(c);(0,i.$C)(c)},4033:function(e,t,a){e.exports=a(94)}},function(e){e.O(0,[609,15,14,971,472,744],function(){return e(e.s=4350)}),_N_E=e.O()}]);