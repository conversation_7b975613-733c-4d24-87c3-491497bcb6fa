{"version": 4, "routes": {"/login": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/login", "dataRoute": "/login.rsc"}, "/import-chat": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/import-chat", "dataRoute": "/import-chat.rsc"}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/dashboard": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/dashboard", "dataRoute": "/dashboard.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "4fcd41b6d1269027e3ae798ccb0c5f78", "previewModeSigningKey": "7395ed42dbeb9bb45d208169907ef61787ac332c6e26cf28931456b9e1bbc048", "previewModeEncryptionKey": "d03e997e9c6a7dceeb852d0046dee62517773c79669e97196d68e7df5baf7a5f"}}