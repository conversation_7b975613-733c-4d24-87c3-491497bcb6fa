"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/SessionContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/SessionContext.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: function() { return /* binding */ SessionProvider; },\n/* harmony export */   useSession: function() { return /* binding */ useSession; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AppearanceContext */ \"(app-pages-browser)/./src/contexts/AppearanceContext.tsx\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ useSession,SessionProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst SessionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    currentSessionWords: 0,\n    sessionStartTime: null,\n    sessionTime: \"0s\",\n    isSessionActive: false,\n    startSession: ()=>{},\n    endSession: ()=>{},\n    addWordsToSession: ()=>{},\n    shouldCreateNewSession: ()=>false\n});\nconst useSession = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SessionContext);\n    if (!context) {\n        throw new Error(\"useSession must be used within a SessionProvider\");\n    }\n    return context;\n};\n_s(useSession, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst SessionProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { settings } = (0,_contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__.useAppearance)();\n    const [currentSessionWords, setCurrentSessionWords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [sessionStartTime, setSessionStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessionTime, setSessionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0s\");\n    const [isSessionActive, setIsSessionActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentChatId, setCurrentChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const formatTime = (milliseconds)=>{\n        const seconds = Math.floor(milliseconds / 1000);\n        const minutes = Math.floor(seconds / 60);\n        const hours = Math.floor(minutes / 60);\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes % 60, \"m \").concat(seconds % 60, \"s\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"m \").concat(seconds % 60, \"s\");\n        } else {\n            return \"\".concat(seconds, \"s\");\n        }\n    };\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) throw new Error(\"Usu\\xe1rio n\\xe3o autenticado\");\n        const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n        const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.db, \"usuarios\");\n        const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n        const querySnapshot = await getDocs(q);\n        if (querySnapshot.empty) {\n            throw new Error(\"Usu\\xe1rio n\\xe3o encontrado\");\n        }\n        const userDoc = querySnapshot.docs[0];\n        const userData = userDoc.data();\n        return userData.username;\n    };\n    const updateChatSessionTime = async (chatId, totalTime)=>{\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.db, \"usuarios\", username, \"conversas\", chatId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)(chatRef, {\n                \"sessionTime.totalTime\": totalTime,\n                \"sessionTime.lastUpdated\": new Date().toISOString()\n            });\n        } catch (error) {\n            console.error(\"Erro ao atualizar tempo de sess\\xe3o:\", error);\n        }\n    };\n    const startSession = (chatId)=>{\n        const now = new Date();\n        setSessionStartTime(now);\n        setCurrentSessionWords(0);\n        setIsSessionActive(true);\n        setCurrentChatId(chatId);\n        // Iniciar timer\n        intervalRef.current = setInterval(()=>{\n            if (sessionStartTime) {\n                const elapsed = Date.now() - sessionStartTime.getTime();\n                setSessionTime(formatTime(elapsed));\n            }\n        }, 1000);\n    };\n    const endSession = async ()=>{\n        if (sessionStartTime && currentChatId) {\n            const totalTime = Date.now() - sessionStartTime.getTime();\n            await updateChatSessionTime(currentChatId, totalTime);\n        }\n        setSessionStartTime(null);\n        setCurrentSessionWords(0);\n        setIsSessionActive(false);\n        setCurrentChatId(null);\n        setSessionTime(\"0s\");\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n            intervalRef.current = null;\n        }\n    };\n    const addWordsToSession = (words)=>{\n        setCurrentSessionWords((prev)=>prev + words);\n    };\n    const shouldCreateNewSession = ()=>{\n        return currentSessionWords >= ((settings === null || settings === void 0 ? void 0 : settings.palavrasPorSessao) || 5000);\n    };\n    // Limpar timer quando o componente for desmontado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n            }\n        };\n    }, []);\n    // Atualizar timer quando a sessão iniciar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sessionStartTime && isSessionActive) {\n            const updateTimer = ()=>{\n                const elapsed = Date.now() - sessionStartTime.getTime();\n                setSessionTime(formatTime(elapsed));\n            };\n            intervalRef.current = setInterval(updateTimer, 1000);\n            updateTimer(); // Atualizar imediatamente\n            return ()=>{\n                if (intervalRef.current) {\n                    clearInterval(intervalRef.current);\n                }\n            };\n        }\n    }, [\n        sessionStartTime,\n        isSessionActive\n    ]);\n    const value = {\n        currentSessionWords,\n        sessionStartTime,\n        sessionTime,\n        isSessionActive,\n        startSession,\n        endSession,\n        addWordsToSession,\n        shouldCreateNewSession\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\contexts\\\\SessionContext.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(SessionProvider, \"KK8L80HbLM3aCo/wAS+QGnmZNJE=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__.useAppearance\n    ];\n});\n_c = SessionProvider;\nvar _c;\n$RefreshReg$(_c, \"SessionProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/SessionContext.tsx\n"));

/***/ })

});