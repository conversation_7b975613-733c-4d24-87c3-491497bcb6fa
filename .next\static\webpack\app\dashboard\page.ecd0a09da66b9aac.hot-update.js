"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_SessionContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/SessionContext */ \"(app-pages-browser)/./src/contexts/SessionContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _StatisticsModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./StatisticsModal */ \"(app-pages-browser)/./src/components/dashboard/StatisticsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated, onUpdateOpenRouterBalance } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { startSession, endSession, addWordsToSession, shouldCreateNewSession, isSessionActive } = (0,_contexts_SessionContext__WEBPACK_IMPORTED_MODULE_6__.useSession)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStatisticsModalOpen, setIsStatisticsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Estados para drag-n-drop\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragCounter, setDragCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado para um chat específico\n    const saveLastUsedModelForChat = async (modelId, chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            // Atualizar o lastUsedModel no documento do chat\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                lastUsedModel: modelId,\n                lastModelUpdateAt: Date.now()\n            });\n        } catch (error) {\n            console.error(\"Error saving last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado de um chat específico\n    const loadLastUsedModelForChat = async (chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(chatRef);\n            if (chatDoc.exists()) {\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    // Verificar se o modelo salvo ainda é válido\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (isValid) {\n                        setSelectedModel(data.lastUsedModel);\n                    } else {\n                        // Limpar o modelo inválido do chat\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                            lastUsedModel: \"\"\n                        });\n                        // Carregar o modelo padrão do endpoint ativo\n                        loadDefaultModelFromActiveEndpoint();\n                    }\n                } else {\n                    // Se o chat não tem modelo salvo, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o modelo padrão do endpoint ativo\n    const loadDefaultModelFromActiveEndpoint = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                // Primeiro, tentar carregar o último modelo usado globalmente\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    return;\n                }\n                // Se não há último modelo usado, buscar o modelo padrão do endpoint ativo\n                if (data.endpoints) {\n                    const activeEndpoint = Object.values(data.endpoints).find((endpoint)=>endpoint.ativo);\n                    if (activeEndpoint && activeEndpoint.modeloPadrao) {\n                        setSelectedModel(activeEndpoint.modeloPadrao);\n                        return;\n                    }\n                }\n            }\n            // Fallback para o modelo padrão hardcoded\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n        } catch (error) {\n            console.error(\"Error loading default model from active endpoint:\", error);\n            // Fallback para o modelo padrão hardcoded em caso de erro\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n        }\n    };\n    // Função para validar se um modelo ainda existe/é válido\n    const isValidModel = async (modelId)=>{\n        // Lista de modelos conhecidos como inválidos ou removidos\n        const invalidModels = [\n            \"qwen/qwen3-235b-a22b-thinking-2507\"\n        ];\n        return !invalidModels.includes(modelId);\n    };\n    // Função para limpar modelos inválidos de todos os chats do usuário\n    const cleanupInvalidModelsFromAllChats = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\");\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsRef);\n            const updatePromises = [];\n            for (const chatDoc of chatsSnapshot.docs){\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (!isValid) {\n                        updatePromises.push((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatDoc.id), {\n                            lastUsedModel: \"\"\n                        }));\n                    }\n                }\n            }\n            if (updatePromises.length > 0) {\n                await Promise.all(updatePromises);\n            }\n        } catch (error) {\n            console.error(\"Error cleaning invalid models from chats:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado globalmente (fallback)\n    const loadGlobalLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    // Verificar se o modelo salvo ainda é válido\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (isValid) {\n                        setSelectedModel(data.lastUsedModel);\n                    } else {\n                        // Limpar o modelo inválido das configurações\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                            lastUsedModel: null\n                        });\n                        // Carregar o modelo padrão do endpoint ativo\n                        loadDefaultModelFromActiveEndpoint();\n                    }\n                } else {\n                    // Se não há último modelo usado, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                }\n            } else {\n                // Se não há configurações, carregar o modelo padrão do endpoint ativo\n                loadDefaultModelFromActiveEndpoint();\n            }\n        } catch (error) {\n            console.error(\"Error loading global last used model:\", error);\n            // Fallback para carregar o modelo padrão do endpoint ativo\n            loadDefaultModelFromActiveEndpoint();\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        // Salvar no chat específico se houver um chat ativo\n        if (actualChatId) {\n            saveLastUsedModelForChat(modelId, actualChatId);\n        }\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            // Definir o nome do chat imediatamente após criação\n            setChatName(finalChatName);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments, webSearchEnabled)=>{\n        // Obter anexos históricos ativos\n        const historicalAttachments = getAllChatAttachments().filter((att)=>att.isActive !== false);\n        // Combinar anexos novos com anexos históricos ativos\n        const allAttachmentsToSend = [\n            ...attachments || [],\n            ...historicalAttachments // Anexos históricos ativos\n        ];\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // O nome já foi definido na função createAutoChat, mas vamos garantir carregando também\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        // Contar palavras da mensagem do usuário e adicionar à sessão\n        const wordCount = currentMessage.split(/\\s+/).filter((word)=>word.length > 0).length;\n        addWordsToSession(wordCount);\n        // Verificar se deve criar uma nova sessão\n        if (shouldCreateNewSession()) {\n            console.log(\"Limite de palavras por sess\\xe3o atingido. Nova sess\\xe3o ser\\xe1 criada.\");\n        // Aqui você pode implementar a lógica para criar uma nova sessão\n        // Por exemplo, salvar a sessão atual e iniciar uma nova\n        }\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: uniqueAttachments,\n            webSearchEnabled: webSearchEnabled,\n            userMessageId: userMessage.id\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString(),\n                        hasWebSearch: webSearchEnabled\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            // Salvar o modelo usado no chat específico\n            if (chatIdToUse) {\n                saveLastUsedModelForChat(selectedModel, chatIdToUse);\n            }\n            // Atualizar saldo do OpenRouter após a resposta com delay de 5 segundos\n            if (onUpdateOpenRouterBalance) {\n                setTimeout(()=>{\n                    onUpdateOpenRouterBalance();\n                }, 5000);\n            }\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                const chatName = chatData.name || \"Conversa sem nome\";\n                setChatName(chatName);\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].loadChatMessages(username, chatId);\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].convertFromAIFormat(chatMessages);\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"❌ Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado globalmente quando o componente montar (apenas se não há chat)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && !currentChat) {\n            // Limpar modelos inválidos uma vez quando o usuário faz login\n            cleanupInvalidModelsFromAllChats();\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        user,\n        currentChat\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n            // Carregar o modelo específico do chat\n            loadLastUsedModelForChat(currentChat);\n            // Iniciar sessão se não estiver ativa\n            if (!isSessionActive) {\n                startSession(currentChat);\n            }\n        } else if (!currentChat && actualChatId) {\n            // Só resetar se realmente não há chat e havia um chat antes\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n            // Carregar modelo global quando não há chat específico\n            loadGlobalLastUsedModel();\n            // Encerrar sessão se estiver ativa\n            if (isSessionActive) {\n                endSession();\n            }\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email,\n        isSessionActive,\n        startSession,\n        endSession\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // ✅ CORREÇÃO: Recarregar mensagens do Firebase Storage para garantir estado atualizado\n        console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o para garantir estado atualizado...\");\n        try {\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                setIsLoading(false);\n                setIsStreaming(false);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)\n            console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n            for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                const msgToDelete = convertedFreshMessages[i];\n                console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true,\n                userMessageId: messageToRegenerate.id\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return false;\n        console.log(\"✏️ Iniciando edi\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            console.log(\"\\uD83D\\uDCE4 Enviando atualiza\\xe7\\xe3o para o servidor...\");\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                console.error(\"❌ Falha ao atualizar mensagem no servidor\");\n                loadChatMessages(actualChatId);\n                return false;\n            } else {\n                console.log(\"✅ Mensagem editada e salva com sucesso no Firebase Storage:\", {\n                    messageId,\n                    timestamp: new Date().toISOString()\n                });\n                return true;\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            console.error(\"❌ Erro ao atualizar mensagem:\", error);\n            loadChatMessages(actualChatId);\n            return false;\n        }\n    };\n    const handleEditAndRegenerate = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        console.log(\"✏️\\uD83D\\uDD04 Iniciando edi\\xe7\\xe3o e regenera\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        try {\n            // 1. Primeiro, salvar a edição\n            const editSuccess = await handleEditMessage(messageId, newContent);\n            if (!editSuccess) {\n                console.error(\"❌ Falha ao editar mensagem, cancelando regenera\\xe7\\xe3o\");\n                return;\n            }\n            console.log(\"✅ Mensagem editada com sucesso, iniciando regenera\\xe7\\xe3o...\");\n            // 2. Aguardar um pouco para garantir que a edição foi salva\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            // 3. Recarregar mensagens do Firebase Storage para garantir estado atualizado\n            console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o...\");\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // 4. Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // 5. Verificar se há mensagens após esta mensagem\n            const hasMessagesAfter = messageIndex < convertedFreshMessages.length - 1;\n            console.log(\"\\uD83D\\uDCCA Mensagens ap\\xf3s esta: \".concat(convertedFreshMessages.length - messageIndex - 1));\n            // 6. Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // 7. Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // 8. Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // 9. Deletar apenas as mensagens POSTERIORES do Firebase Storage (se houver)\n            if (hasMessagesAfter) {\n                console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n                for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                    const msgToDelete = convertedFreshMessages[i];\n                    console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                    await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n                }\n            }\n            // 10. Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true,\n                userMessageId: messageToRegenerate.id\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao editar e regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: scrollContainer.scrollHeight,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para abrir o modal de estatísticas\n    const handleStatisticsModal = ()=>{\n        setIsStatisticsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para salvar o estado dos anexos no Firebase Storage\n    const saveAttachmentStates = async (updatedMessages)=>{\n        if (!currentUsername || !actualChatId) return;\n        try {\n            // Preparar dados do chat para salvar\n            const chatData = {\n                id: actualChatId,\n                name: chatName || \"Chat\",\n                messages: updatedMessages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                        timestamp: msg.timestamp,\n                        isFavorite: msg.isFavorite,\n                        attachments: msg.attachments\n                    })),\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(currentUsername, \"/conversas/\").concat(actualChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(chatJsonRef, chatJsonBlob);\n            console.log(\"✅ Estado dos anexos salvo no Firebase Storage\");\n            console.log(\"\\uD83D\\uDCC1 Dados salvos:\", {\n                chatId: actualChatId,\n                totalMessages: chatData.messages.length,\n                messagesWithAttachments: chatData.messages.filter((msg)=>msg.attachments && msg.attachments.length > 0).length\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao salvar estado dos anexos:\", error);\n        }\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            const updatedMessages = prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            // Se isActive não está definido, considerar como true (ativo por padrão)\n                            const currentState = attachment.isActive !== false;\n                            return {\n                                ...attachment,\n                                isActive: !currentState\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n            // Salvar o estado atualizado no Firebase Storage\n            saveAttachmentStates(updatedMessages);\n            return updatedMessages;\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        // Para anexos novos (sem isActive definido), incluir por padrão\n        // Para anexos existentes, verificar se isActive não é false\n        return attachments.filter((attachment)=>{\n            // Se isActive não está definido (anexo novo), incluir\n            if (attachment.isActive === undefined) return true;\n            // Se isActive está definido, incluir apenas se não for false\n            return attachment.isActive !== false;\n        });\n    };\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    // Funções para drag-n-drop\n    const handleDragEnter = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragCounter((prev)=>prev + 1);\n        if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n            setIsDragOver(true);\n        }\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragCounter((prev)=>prev - 1);\n        // Só remove o overlay quando o contador chega a 0\n        // Isso evita flickering quando o drag passa por elementos filhos\n        if (dragCounter <= 1) {\n            setIsDragOver(false);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        // Permitir drop apenas se há arquivos sendo arrastados\n        if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n            e.dataTransfer.dropEffect = \"copy\";\n        }\n    };\n    const handleDrop = async (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setIsDragOver(false);\n        setDragCounter(0);\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length === 0) return;\n        // Verificar se temos username necessário\n        if (!currentUsername) {\n            console.error(\"Username n\\xe3o dispon\\xedvel para upload de anexos\");\n            return;\n        }\n        // Se não há chat atual, criar um automaticamente para poder fazer upload dos anexos\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = files.length === 1 ? \"Arquivo anexado: \".concat(files[0].name) : \"\".concat(files.length, \" arquivos anexados\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID para anexos\");\n            return;\n        }\n        try {\n            // Importar o attachmentService dinamicamente\n            const { default: attachmentService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\"));\n            // Fazer upload dos arquivos\n            const uploadedAttachments = await attachmentService.uploadMultipleAttachments(files, currentUsername, chatIdToUse);\n            // Em vez de enviar a mensagem, vamos notificar o InputBar sobre os novos anexos\n            // Isso será feito através de um evento customizado\n            const attachmentMetadata = uploadedAttachments.map((att)=>att.metadata);\n            // Disparar evento customizado para o InputBar capturar\n            const event = new CustomEvent(\"dragDropAttachments\", {\n                detail: {\n                    attachments: attachmentMetadata,\n                    chatId: chatIdToUse,\n                    username: currentUsername\n                }\n            });\n            window.dispatchEvent(event);\n            console.log(\"✅ \".concat(files.length, \" arquivo(s) adicionado(s) como anexo via drag-n-drop\"));\n        } catch (error) {\n            console.error(\"❌ Erro ao processar arquivos via drag-n-drop:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen relative\",\n        onDragEnter: handleDragEnter,\n        onDragLeave: handleDragLeave,\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            isDragOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-50 bg-blue-900/80 backdrop-blur-sm flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-800/90 backdrop-blur-md rounded-2xl p-8 border-2 border-dashed border-blue-400 shadow-2xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-16 h-16 text-blue-300 mx-auto animate-bounce\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                        lineNumber: 1263,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                    lineNumber: 1262,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1261,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-blue-100 mb-2\",\n                                children: \"Solte os arquivos aqui\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1266,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-300 text-sm\",\n                                children: \"Os arquivos ser\\xe3o adicionados como anexos \\xe0 conversa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1269,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                        lineNumber: 1260,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1259,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1258,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                onDownload: handleDownloadModal,\n                onAttachments: handleAttachmentsModal,\n                onStatistics: handleStatisticsModal,\n                isLoading: isLoading,\n                attachmentsCount: getAllChatAttachments().length,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    isStreaming: isStreaming,\n                    streamingMessageId: streamingMessageId || undefined,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onEditAndRegenerate: handleEditAndRegenerate,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1294,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1293,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined,\n                activeAttachmentsCount: getActiveAttachmentIds().length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1309,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1327,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1335,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1343,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatisticsModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isOpen: isStatisticsModalOpen,\n                onClose: ()=>setIsStatisticsModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1352,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 1249,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"Y7/u+DJcoCi3H2BpoU4vl2Nrtxk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _contexts_SessionContext__WEBPACK_IMPORTED_MODULE_6__.useSession\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});