"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/MarkdownRenderer.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remark-math */ \"(app-pages-browser)/./node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rehype-katex */ \"(app-pages-browser)/./node_modules/rehype-katex/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rehype-highlight */ \"(app-pages-browser)/./node_modules/rehype-highlight/lib/index.js\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _styles_markdown_minimal_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/markdown-minimal.css */ \"(app-pages-browser)/./src/styles/markdown-minimal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Importar estilos do KaTeX e Markdown\n\n\n// Componente otimizado para renderização de markdown durante streaming\nconst OptimizedMarkdown = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { content, isStreaming } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n        remarkPlugins: [\n            remark_gfm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            remark_math__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        ],\n        rehypePlugins: [\n            rehype_katex__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            [\n                rehype_highlight__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                {\n                    detect: true,\n                    ignoreMissing: true\n                }\n            ]\n        ],\n        components: {\n            // Customizar renderização de código\n            code (param) {\n                let { node, inline, className, children, ...props } = param;\n                const match = /language-(\\w+)/.exec(className || \"\");\n                const language = match ? match[1] : \"text\";\n                return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"code-block-container group\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"code-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full bg-red-400/80\"\n                                                }, void 0, false, void 0, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full bg-yellow-400/80\"\n                                                }, void 0, false, void 0, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full bg-green-400/80\"\n                                                }, void 0, false, void 0, void 0)\n                                            ]\n                                        }, void 0, true, void 0, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-slate-300 capitalize\",\n                                            children: language\n                                        }, void 0, false, void 0, void 0)\n                                    ]\n                                }, void 0, true, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"copy-button opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                    onClick: ()=>navigator.clipboard.writeText(String(children)),\n                                    title: \"Copiar c\\xf3digo\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                        }, void 0, false, void 0, void 0)\n                                    }, void 0, false, void 0, void 0)\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"code-content\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: className,\n                                ...props,\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0)\n                    ]\n                }, void 0, true, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                    className: \"inline-code\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de links\n            a (param) {\n                let { children, href, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: href,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"markdown-link\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de tabelas\n            table (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"table-wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"markdown-table\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            th (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                    className: \"table-header\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            td (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                    className: \"table-cell\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de blockquotes\n            blockquote (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                    className: \"markdown-blockquote\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"quote-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de listas\n            ul (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"markdown-list unordered\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            ol (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                    className: \"markdown-list ordered\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            li (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"list-item\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de títulos\n            h1 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"markdown-heading h1\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h2 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"markdown-heading h2\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h3 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"markdown-heading h3\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h4 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                    className: \"markdown-heading h4\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h5 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                    className: \"markdown-heading h5\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h6 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                    className: \"markdown-heading h6\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de parágrafos\n            p (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"markdown-paragraph\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de separadores\n            hr (param) {\n                let { ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                    className: \"markdown-divider\",\n                    ...props\n                }, void 0, false, void 0, void 0);\n            }\n        },\n        children: content\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n}, (prevProps, nextProps)=>{\n    // Durante streaming, só re-renderizar se o conteúdo mudou significativamente\n    if (nextProps.isStreaming) {\n        // Durante streaming, re-renderizar apenas a cada 100 caracteres para melhor performance\n        const prevLength = prevProps.content.length;\n        const nextLength = nextProps.content.length;\n        const shouldUpdate = nextLength - prevLength >= 100 || nextLength < prevLength;\n        return !shouldUpdate;\n    }\n    // Fora do streaming, comportamento normal\n    return prevProps.content === nextProps.content;\n});\n_c = OptimizedMarkdown;\nconst MarkdownRenderer = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c1 = _s((param)=>{\n    let { content, className = \"\", hasWebSearch = false, webSearchAnnotations = [], isStreaming = false } = param;\n    _s();\n    // Função para detectar se o conteúdo contém citações de web search\n    const detectWebSearch = (text)=>{\n        // Detecta links no formato [dominio.com] que são característicos do web search\n        const webSearchPattern = /\\[[\\w.-]+\\.[\\w]+\\]/g;\n        const matches = text.match(webSearchPattern);\n        return matches !== null && matches.length > 0;\n    };\n    // Função para processar o conteúdo (OpenRouter já retorna links formatados corretamente)\n    const processWebSearchLinks = (text)=>{\n        // Como o OpenRouter já retorna os links no formato markdown correto,\n        // não precisamos processar nada. Apenas retornamos o texto original.\n        return text;\n    };\n    // Função para contar e extrair informações sobre as fontes\n    const getWebSearchInfo = (text, annotations)=>{\n        if (annotations.length > 0) {\n            // Usar annotations se disponíveis\n            const uniqueDomains = new Set(annotations.map((annotation)=>{\n                try {\n                    return new URL(annotation.url).hostname.replace(\"www.\", \"\");\n                } catch (e) {\n                    return annotation.url;\n                }\n            }));\n            return {\n                sourceCount: annotations.length,\n                sources: Array.from(uniqueDomains)\n            };\n        }\n        // Fallback: detectar pelos padrões no texto (formato markdown link)\n        const webSearchPattern = /\\[[\\w.-]+\\.[\\w]+\\]\\([^)]+\\)/g;\n        const matches = text.match(webSearchPattern) || [];\n        const sourceSet = new Set(matches.map((match)=>{\n            // Extrair o domínio do formato [dominio.com](url)\n            const domainMatch = match.match(/\\[([\\w.-]+\\.[\\w]+)\\]/);\n            return domainMatch ? domainMatch[1] : match;\n        }));\n        const uniqueSources = Array.from(sourceSet);\n        return {\n            sourceCount: matches.length,\n            sources: uniqueSources\n        };\n    };\n    // Usar useMemo para otimizar processamento durante streaming\n    const { isWebSearchMessage, webSearchInfo, processedContent } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const isWebSearch = hasWebSearch || detectWebSearch(content);\n        const searchInfo = isWebSearch ? getWebSearchInfo(content, webSearchAnnotations) : {\n            sourceCount: 0,\n            sources: []\n        };\n        const processed = isWebSearch ? processWebSearchLinks(content) : content;\n        return {\n            isWebSearchMessage: isWebSearch,\n            webSearchInfo: searchInfo,\n            processedContent: processed\n        };\n    }, [\n        content,\n        hasWebSearch,\n        webSearchAnnotations\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rafthor-markdown \".concat(className, \" \").concat(isStreaming ? \"streaming\" : \"\"),\n        children: [\n            isWebSearchMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"web-search-indicator\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"search-badge\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"search-icon\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M9 12l2 2 4-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Busca na Web\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"source-count\",\n                                children: webSearchInfo.sourceCount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, undefined),\n                    webSearchInfo.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"source-list\",\n                        children: [\n                            webSearchInfo.sources.slice(0, 3).map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"source-tag\",\n                                    children: source\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 17\n                                }, undefined)),\n                            webSearchInfo.sources.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"source-tag more\",\n                                children: [\n                                    \"+\",\n                                    webSearchInfo.sources.length - 3\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                lineNumber: 316,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OptimizedMarkdown, {\n                content: processedContent,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 313,\n        columnNumber: 5\n    }, undefined);\n}, \"dFNNXoCuWiDJiwthbe1hRtm0Vi0=\")), \"dFNNXoCuWiDJiwthbe1hRtm0Vi0=\");\n_c2 = MarkdownRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MarkdownRenderer);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"OptimizedMarkdown\");\n$RefreshReg$(_c1, \"MarkdownRenderer$React.memo\");\n$RefreshReg$(_c2, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\n"));

/***/ })

});