"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Upperbar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/Upperbar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Upperbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_SessionContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/SessionContext */ \"(app-pages-browser)/./src/contexts/SessionContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Hook para tempo de sessão\nconst useSessionTime = (chatId)=>{\n    _s();\n    const [sessionTime, setSessionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0s\");\n    const [startTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Função para formatar tempo de forma inteligente\n    const formatTime = (timeInMs)=>{\n        const totalSeconds = Math.floor(timeInMs / 1000);\n        const totalMinutes = Math.floor(totalSeconds / 60);\n        const totalHours = Math.floor(totalMinutes / 60);\n        const totalDays = Math.floor(totalHours / 24);\n        const totalMonths = Math.floor(totalDays / 30);\n        const seconds = totalSeconds % 60;\n        const minutes = totalMinutes % 60;\n        const hours = totalHours % 24;\n        const days = totalDays % 30;\n        // Menos de 1 minuto: apenas segundos\n        if (totalMinutes === 0) {\n            return \"\".concat(totalSeconds, \"s\");\n        }\n        // Menos de 1 hora: MM:SS\n        if (totalHours === 0) {\n            return \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        // Menos de 1 dia: HH:MM:SS\n        if (totalDays === 0) {\n            return \"\".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        // Menos de 1 mês: Xd HH:MM\n        if (totalMonths === 0) {\n            return \"\".concat(days, \"d \").concat(hours.toString().padStart(2, \"0\"), \":\").concat(minutes.toString().padStart(2, \"0\"));\n        }\n        // 1 mês ou mais: Xm Xd HH:MM\n        return \"\".concat(totalMonths, \"m \").concat(days, \"d \").concat(hours.toString().padStart(2, \"0\"), \":\").concat(minutes.toString().padStart(2, \"0\"));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (chatId) {\n            intervalRef.current = setInterval(()=>{\n                const elapsed = Date.now() - startTime;\n                const formattedTime = formatTime(elapsed);\n                setSessionTime(formattedTime);\n            }, 1000);\n        }\n        return ()=>{\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n                intervalRef.current = null;\n            }\n        };\n    }, [\n        chatId,\n        startTime\n    ]);\n    return sessionTime;\n};\n_s(useSessionTime, \"D9h1eTTjB/UJUr/8kCtwXx6uawQ=\");\nfunction Upperbar(param) {\n    let { currentChat, chatName, aiModel, onDownload, onAttachments, onStatistics, isLoading = false, attachmentsCount = 0, aiMetadata } = param;\n    _s1();\n    const { sessionTime, currentSessionWords, isSessionActive } = (0,_contexts_SessionContext__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const handleAttachments = ()=>{\n        if (onAttachments) {\n            onAttachments();\n        } else {\n            console.log(\"Anexos clicado\");\n        }\n    };\n    const handleStats = ()=>{\n        if (onStatistics) {\n            onStatistics();\n        } else {\n            console.log(\"Estat\\xedsticas clicado\");\n        }\n    };\n    const handleDownload = ()=>{\n        if (onDownload) {\n            onDownload();\n        } else {\n            console.log(\"Download clicado\");\n        }\n    };\n    const currentModel = aiModel || \"GPT-4.1 Nano\";\n    const displayChatName = chatName || (currentChat ? \"Chat \".concat(currentChat) : \"Nova Conversa\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-14 sm:h-16 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border-b border-blue-700/30 shadow-xl relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex items-center justify-between px-3 sm:px-4 lg:px-6 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-900/30 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-400 rounded-full shadow-lg shadow-blue-400/50 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm font-medium text-blue-100 truncate\",\n                                        children: currentModel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    (aiMetadata === null || aiMetadata === void 0 ? void 0 : aiMetadata.usedCoT) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-1.5 sm:px-2 py-0.5 sm:py-1 bg-cyan-600/20 text-cyan-300 text-xs rounded-full border border-cyan-500/30 hidden sm:block\",\n                                        children: \"CoT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-px h-4 sm:h-6 bg-blue-600/30 hidden sm:block\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1.5 sm:space-x-2 bg-blue-900/20 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 hidden sm:flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 sm:w-4 sm:h-4 text-blue-300\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm text-blue-200 font-mono\",\n                                        children: isSessionActive ? sessionTime : \"0s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-1/2 transform -translate-x-1/2 hidden sm:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2 shadow-lg max-w-xs lg:max-w-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full shadow-lg flex-shrink-0 \".concat(isLoading ? \"bg-yellow-400 shadow-yellow-400/50 animate-pulse\" : \"bg-cyan-400 shadow-cyan-400/50\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-sm sm:text-lg font-semibold text-white truncate\",\n                                    children: displayChatName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 sm:space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAttachments,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105 relative\",\n                                title: \"Anexos\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    attachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -top-1 -right-1 bg-cyan-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium shadow-lg\",\n                                        children: attachmentsCount > 99 ? \"99+\" : attachmentsCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStats,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105\",\n                                title: \"Estat\\xedsticas\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDownload,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/30 hover:scale-105 border border-blue-500/30\",\n                                title: \"Download\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s1(Upperbar, \"nu5VaeafEV+8BRwPenx5qXJtGu0=\", false, function() {\n    return [\n        _contexts_SessionContext__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = Upperbar;\nvar _c;\n$RefreshReg$(_c, \"Upperbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\n"));

/***/ })

});