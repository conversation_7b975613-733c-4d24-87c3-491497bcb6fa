"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_SessionContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/SessionContext */ \"(app-pages-browser)/./src/contexts/SessionContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _StatisticsModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./StatisticsModal */ \"(app-pages-browser)/./src/components/dashboard/StatisticsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated, onUpdateOpenRouterBalance } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { startSession, endSession, addWordsToSession, shouldCreateNewSession, isSessionActive } = (0,_contexts_SessionContext__WEBPACK_IMPORTED_MODULE_6__.useSession)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStatisticsModalOpen, setIsStatisticsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Estados para drag-n-drop\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragCounter, setDragCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado para um chat específico\n    const saveLastUsedModelForChat = async (modelId, chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            // Atualizar o lastUsedModel no documento do chat\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                lastUsedModel: modelId,\n                lastModelUpdateAt: Date.now()\n            });\n        } catch (error) {\n            console.error(\"Error saving last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado de um chat específico\n    const loadLastUsedModelForChat = async (chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(chatRef);\n            if (chatDoc.exists()) {\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    // Verificar se o modelo salvo ainda é válido\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (isValid) {\n                        setSelectedModel(data.lastUsedModel);\n                    } else {\n                        // Limpar o modelo inválido do chat\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                            lastUsedModel: \"\"\n                        });\n                        // Carregar o modelo padrão do endpoint ativo\n                        loadDefaultModelFromActiveEndpoint();\n                    }\n                } else {\n                    // Se o chat não tem modelo salvo, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o modelo padrão do endpoint ativo\n    const loadDefaultModelFromActiveEndpoint = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                // Primeiro, tentar carregar o último modelo usado globalmente\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    return;\n                }\n                // Se não há último modelo usado, buscar o modelo padrão do endpoint ativo\n                if (data.endpoints) {\n                    const activeEndpoint = Object.values(data.endpoints).find((endpoint)=>endpoint.ativo);\n                    if (activeEndpoint && activeEndpoint.modeloPadrao) {\n                        setSelectedModel(activeEndpoint.modeloPadrao);\n                        return;\n                    }\n                }\n            }\n            // Fallback para o modelo padrão hardcoded\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n        } catch (error) {\n            console.error(\"Error loading default model from active endpoint:\", error);\n            // Fallback para o modelo padrão hardcoded em caso de erro\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n        }\n    };\n    // Função para validar se um modelo ainda existe/é válido\n    const isValidModel = async (modelId)=>{\n        // Lista de modelos conhecidos como inválidos ou removidos\n        const invalidModels = [\n            \"qwen/qwen3-235b-a22b-thinking-2507\"\n        ];\n        return !invalidModels.includes(modelId);\n    };\n    // Função para limpar modelos inválidos de todos os chats do usuário\n    const cleanupInvalidModelsFromAllChats = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\");\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsRef);\n            const updatePromises = [];\n            for (const chatDoc of chatsSnapshot.docs){\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (!isValid) {\n                        updatePromises.push((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatDoc.id), {\n                            lastUsedModel: \"\"\n                        }));\n                    }\n                }\n            }\n            if (updatePromises.length > 0) {\n                await Promise.all(updatePromises);\n            }\n        } catch (error) {\n            console.error(\"Error cleaning invalid models from chats:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado globalmente (fallback)\n    const loadGlobalLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    // Verificar se o modelo salvo ainda é válido\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (isValid) {\n                        setSelectedModel(data.lastUsedModel);\n                    } else {\n                        // Limpar o modelo inválido das configurações\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                            lastUsedModel: null\n                        });\n                        // Carregar o modelo padrão do endpoint ativo\n                        loadDefaultModelFromActiveEndpoint();\n                    }\n                } else {\n                    // Se não há último modelo usado, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                }\n            } else {\n                // Se não há configurações, carregar o modelo padrão do endpoint ativo\n                loadDefaultModelFromActiveEndpoint();\n            }\n        } catch (error) {\n            console.error(\"Error loading global last used model:\", error);\n            // Fallback para carregar o modelo padrão do endpoint ativo\n            loadDefaultModelFromActiveEndpoint();\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        // Salvar no chat específico se houver um chat ativo\n        if (actualChatId) {\n            saveLastUsedModelForChat(modelId, actualChatId);\n        }\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            // Definir o nome do chat imediatamente após criação\n            setChatName(finalChatName);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments, webSearchEnabled)=>{\n        // Obter anexos históricos ativos\n        const historicalAttachments = getAllChatAttachments().filter((att)=>att.isActive !== false);\n        // Combinar anexos novos com anexos históricos ativos\n        const allAttachmentsToSend = [\n            ...attachments || [],\n            ...historicalAttachments // Anexos históricos ativos\n        ];\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // O nome já foi definido na função createAutoChat, mas vamos garantir carregando também\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        // Contar palavras da mensagem do usuário e adicionar à sessão\n        const wordCount = currentMessage.split(/\\s+/).filter((word)=>word.length > 0).length;\n        addWordsToSession(wordCount);\n        // Verificar se deve criar uma nova sessão\n        if (shouldCreateNewSession()) {\n            console.log(\"Limite de palavras por sess\\xe3o atingido. Nova sess\\xe3o ser\\xe1 criada.\");\n        // Aqui você pode implementar a lógica para criar uma nova sessão\n        // Por exemplo, salvar a sessão atual e iniciar uma nova\n        }\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: uniqueAttachments,\n            webSearchEnabled: webSearchEnabled,\n            userMessageId: userMessage.id\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString(),\n                        hasWebSearch: webSearchEnabled\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            // Contar palavras da resposta da IA e adicionar à sessão\n            const aiWordCount = fullResponse.split(/\\s+/).filter((word)=>word.length > 0).length;\n            addWordsToSession(aiWordCount);\n            // Salvar o modelo usado no chat específico\n            if (chatIdToUse) {\n                saveLastUsedModelForChat(selectedModel, chatIdToUse);\n            }\n            // Atualizar saldo do OpenRouter após a resposta com delay de 5 segundos\n            if (onUpdateOpenRouterBalance) {\n                setTimeout(()=>{\n                    onUpdateOpenRouterBalance();\n                }, 5000);\n            }\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                const chatName = chatData.name || \"Conversa sem nome\";\n                setChatName(chatName);\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].loadChatMessages(username, chatId);\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].convertFromAIFormat(chatMessages);\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"❌ Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado globalmente quando o componente montar (apenas se não há chat)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && !currentChat) {\n            // Limpar modelos inválidos uma vez quando o usuário faz login\n            cleanupInvalidModelsFromAllChats();\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        user,\n        currentChat\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n            // Carregar o modelo específico do chat\n            loadLastUsedModelForChat(currentChat);\n            // Iniciar sessão se não estiver ativa\n            if (!isSessionActive) {\n                startSession(currentChat);\n            }\n        } else if (!currentChat && actualChatId) {\n            // Só resetar se realmente não há chat e havia um chat antes\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n            // Carregar modelo global quando não há chat específico\n            loadGlobalLastUsedModel();\n            // Encerrar sessão se estiver ativa\n            if (isSessionActive) {\n                endSession();\n            }\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email,\n        isSessionActive,\n        startSession,\n        endSession\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // ✅ CORREÇÃO: Recarregar mensagens do Firebase Storage para garantir estado atualizado\n        console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o para garantir estado atualizado...\");\n        try {\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                setIsLoading(false);\n                setIsStreaming(false);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)\n            console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n            for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                const msgToDelete = convertedFreshMessages[i];\n                console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true,\n                userMessageId: messageToRegenerate.id\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return false;\n        console.log(\"✏️ Iniciando edi\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            console.log(\"\\uD83D\\uDCE4 Enviando atualiza\\xe7\\xe3o para o servidor...\");\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                console.error(\"❌ Falha ao atualizar mensagem no servidor\");\n                loadChatMessages(actualChatId);\n                return false;\n            } else {\n                console.log(\"✅ Mensagem editada e salva com sucesso no Firebase Storage:\", {\n                    messageId,\n                    timestamp: new Date().toISOString()\n                });\n                return true;\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            console.error(\"❌ Erro ao atualizar mensagem:\", error);\n            loadChatMessages(actualChatId);\n            return false;\n        }\n    };\n    const handleEditAndRegenerate = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        console.log(\"✏️\\uD83D\\uDD04 Iniciando edi\\xe7\\xe3o e regenera\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        try {\n            // 1. Primeiro, salvar a edição\n            const editSuccess = await handleEditMessage(messageId, newContent);\n            if (!editSuccess) {\n                console.error(\"❌ Falha ao editar mensagem, cancelando regenera\\xe7\\xe3o\");\n                return;\n            }\n            console.log(\"✅ Mensagem editada com sucesso, iniciando regenera\\xe7\\xe3o...\");\n            // 2. Aguardar um pouco para garantir que a edição foi salva\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            // 3. Recarregar mensagens do Firebase Storage para garantir estado atualizado\n            console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o...\");\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // 4. Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // 5. Verificar se há mensagens após esta mensagem\n            const hasMessagesAfter = messageIndex < convertedFreshMessages.length - 1;\n            console.log(\"\\uD83D\\uDCCA Mensagens ap\\xf3s esta: \".concat(convertedFreshMessages.length - messageIndex - 1));\n            // 6. Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // 7. Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // 8. Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // 9. Deletar apenas as mensagens POSTERIORES do Firebase Storage (se houver)\n            if (hasMessagesAfter) {\n                console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n                for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                    const msgToDelete = convertedFreshMessages[i];\n                    console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                    await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n                }\n            }\n            // 10. Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true,\n                userMessageId: messageToRegenerate.id\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao editar e regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: scrollContainer.scrollHeight,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para abrir o modal de estatísticas\n    const handleStatisticsModal = ()=>{\n        setIsStatisticsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para salvar o estado dos anexos no Firebase Storage\n    const saveAttachmentStates = async (updatedMessages)=>{\n        if (!currentUsername || !actualChatId) return;\n        try {\n            // Preparar dados do chat para salvar\n            const chatData = {\n                id: actualChatId,\n                name: chatName || \"Chat\",\n                messages: updatedMessages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                        timestamp: msg.timestamp,\n                        isFavorite: msg.isFavorite,\n                        attachments: msg.attachments\n                    })),\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(currentUsername, \"/conversas/\").concat(actualChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(chatJsonRef, chatJsonBlob);\n            console.log(\"✅ Estado dos anexos salvo no Firebase Storage\");\n            console.log(\"\\uD83D\\uDCC1 Dados salvos:\", {\n                chatId: actualChatId,\n                totalMessages: chatData.messages.length,\n                messagesWithAttachments: chatData.messages.filter((msg)=>msg.attachments && msg.attachments.length > 0).length\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao salvar estado dos anexos:\", error);\n        }\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            const updatedMessages = prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            // Se isActive não está definido, considerar como true (ativo por padrão)\n                            const currentState = attachment.isActive !== false;\n                            return {\n                                ...attachment,\n                                isActive: !currentState\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n            // Salvar o estado atualizado no Firebase Storage\n            saveAttachmentStates(updatedMessages);\n            return updatedMessages;\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        // Para anexos novos (sem isActive definido), incluir por padrão\n        // Para anexos existentes, verificar se isActive não é false\n        return attachments.filter((attachment)=>{\n            // Se isActive não está definido (anexo novo), incluir\n            if (attachment.isActive === undefined) return true;\n            // Se isActive está definido, incluir apenas se não for false\n            return attachment.isActive !== false;\n        });\n    };\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    // Funções para drag-n-drop\n    const handleDragEnter = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragCounter((prev)=>prev + 1);\n        if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n            setIsDragOver(true);\n        }\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragCounter((prev)=>prev - 1);\n        // Só remove o overlay quando o contador chega a 0\n        // Isso evita flickering quando o drag passa por elementos filhos\n        if (dragCounter <= 1) {\n            setIsDragOver(false);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        // Permitir drop apenas se há arquivos sendo arrastados\n        if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n            e.dataTransfer.dropEffect = \"copy\";\n        }\n    };\n    const handleDrop = async (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setIsDragOver(false);\n        setDragCounter(0);\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length === 0) return;\n        // Verificar se temos username necessário\n        if (!currentUsername) {\n            console.error(\"Username n\\xe3o dispon\\xedvel para upload de anexos\");\n            return;\n        }\n        // Se não há chat atual, criar um automaticamente para poder fazer upload dos anexos\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = files.length === 1 ? \"Arquivo anexado: \".concat(files[0].name) : \"\".concat(files.length, \" arquivos anexados\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID para anexos\");\n            return;\n        }\n        try {\n            // Importar o attachmentService dinamicamente\n            const { default: attachmentService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\"));\n            // Fazer upload dos arquivos\n            const uploadedAttachments = await attachmentService.uploadMultipleAttachments(files, currentUsername, chatIdToUse);\n            // Em vez de enviar a mensagem, vamos notificar o InputBar sobre os novos anexos\n            // Isso será feito através de um evento customizado\n            const attachmentMetadata = uploadedAttachments.map((att)=>att.metadata);\n            // Disparar evento customizado para o InputBar capturar\n            const event = new CustomEvent(\"dragDropAttachments\", {\n                detail: {\n                    attachments: attachmentMetadata,\n                    chatId: chatIdToUse,\n                    username: currentUsername\n                }\n            });\n            window.dispatchEvent(event);\n            console.log(\"✅ \".concat(files.length, \" arquivo(s) adicionado(s) como anexo via drag-n-drop\"));\n        } catch (error) {\n            console.error(\"❌ Erro ao processar arquivos via drag-n-drop:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen relative\",\n        onDragEnter: handleDragEnter,\n        onDragLeave: handleDragLeave,\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            isDragOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-50 bg-blue-900/80 backdrop-blur-sm flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-800/90 backdrop-blur-md rounded-2xl p-8 border-2 border-dashed border-blue-400 shadow-2xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-16 h-16 text-blue-300 mx-auto animate-bounce\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                        lineNumber: 1267,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1265,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-blue-100 mb-2\",\n                                children: \"Solte os arquivos aqui\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1270,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-300 text-sm\",\n                                children: \"Os arquivos ser\\xe3o adicionados como anexos \\xe0 conversa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1273,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                        lineNumber: 1264,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1263,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1262,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                onDownload: handleDownloadModal,\n                onAttachments: handleAttachmentsModal,\n                onStatistics: handleStatisticsModal,\n                isLoading: isLoading,\n                attachmentsCount: getAllChatAttachments().length,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1282,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    isStreaming: isStreaming,\n                    streamingMessageId: streamingMessageId || undefined,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onEditAndRegenerate: handleEditAndRegenerate,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1298,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1297,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined,\n                activeAttachmentsCount: getActiveAttachmentIds().length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1313,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1331,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1347,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatisticsModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isOpen: isStatisticsModalOpen,\n                onClose: ()=>setIsStatisticsModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1356,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 1253,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"Y7/u+DJcoCi3H2BpoU4vl2Nrtxk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _contexts_SessionContext__WEBPACK_IMPORTED_MODULE_6__.useSession\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});