'use client';

import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useAppearance } from '@/contexts/AppearanceContext';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface SessionContextType {
  currentSessionWords: number;
  sessionStartTime: Date | null;
  sessionTime: string;
  isSessionActive: boolean;
  startSession: (chatId: string) => void;
  endSession: () => void;
  addWordsToSession: (words: number) => void;
  shouldCreateNewSession: () => boolean;
}

const SessionContext = createContext<SessionContextType>({
  currentSessionWords: 0,
  sessionStartTime: null,
  sessionTime: '0s',
  isSessionActive: false,
  startSession: () => {},
  endSession: () => {},
  addWordsToSession: () => {},
  shouldCreateNewSession: () => false
});

export const useSession = () => {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error('useSession must be used within a SessionProvider');
  }
  return context;
};

export const SessionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const { settings } = useAppearance();
  const [currentSessionWords, setCurrentSessionWords] = useState(0);
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);
  const [sessionTime, setSessionTime] = useState('0s');
  const [isSessionActive, setIsSessionActive] = useState(false);
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  const formatTime = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const getUsernameFromFirestore = async (): Promise<string> => {
    if (!user?.email) throw new Error('Usuário não autenticado');

    const { collection, query, where, getDocs } = await import('firebase/firestore');
    const usuariosRef = collection(db, 'usuarios');
    const q = query(usuariosRef, where('email', '==', user.email));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      throw new Error('Usuário não encontrado');
    }

    const userDoc = querySnapshot.docs[0];
    const userData = userDoc.data();
    return userData.username;
  };

  const updateChatSessionTime = async (chatId: string, totalTime: number) => {
    try {
      const username = await getUsernameFromFirestore();
      const chatRef = doc(db, 'usuarios', username, 'conversas', chatId);
      
      await updateDoc(chatRef, {
        'sessionTime.totalTime': totalTime,
        'sessionTime.lastUpdated': new Date().toISOString()
      });
    } catch (error) {
      console.error('Erro ao atualizar tempo de sessão:', error);
    }
  };

  const startSession = (chatId: string) => {
    const now = new Date();
    setSessionStartTime(now);
    setCurrentSessionWords(0);
    setIsSessionActive(true);
    setCurrentChatId(chatId);

    // Iniciar timer
    intervalRef.current = setInterval(() => {
      if (sessionStartTime) {
        const elapsed = Date.now() - sessionStartTime.getTime();
        setSessionTime(formatTime(elapsed));
      }
    }, 1000);
  };

  const endSession = async () => {
    if (sessionStartTime && currentChatId) {
      const totalTime = Date.now() - sessionStartTime.getTime();
      await updateChatSessionTime(currentChatId, totalTime);
    }

    setSessionStartTime(null);
    setCurrentSessionWords(0);
    setIsSessionActive(false);
    setCurrentChatId(null);
    setSessionTime('0s');

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const addWordsToSession = (words: number) => {
    setCurrentSessionWords(prev => prev + words);
  };

  const shouldCreateNewSession = (): boolean => {
    return currentSessionWords >= settings.palavrasPorSessao;
  };

  // Limpar timer quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Atualizar timer quando a sessão iniciar
  useEffect(() => {
    if (sessionStartTime && isSessionActive) {
      const updateTimer = () => {
        const elapsed = Date.now() - sessionStartTime.getTime();
        setSessionTime(formatTime(elapsed));
      };

      intervalRef.current = setInterval(updateTimer, 1000);
      updateTimer(); // Atualizar imediatamente

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [sessionStartTime, isSessionActive]);

  const value = {
    currentSessionWords,
    sessionStartTime,
    sessionTime,
    isSessionActive,
    startSession,
    endSession,
    addWordsToSession,
    shouldCreateNewSession
  };

  return (
    <SessionContext.Provider value={value}>
      {children}
    </SessionContext.Provider>
  );
};
