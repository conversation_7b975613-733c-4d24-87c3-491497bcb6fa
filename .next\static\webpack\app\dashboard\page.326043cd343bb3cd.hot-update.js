"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/SettingsModal.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/SettingsModal.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AppearanceContext */ \"(app-pages-browser)/./src/contexts/AppearanceContext.tsx\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SettingsModal(param) {\n    let { isOpen, onClose, userData, onUserDataUpdate } = param;\n    _s();\n    const { logout, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { settings: contextSettings, refreshSettings } = (0,_contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__.useAppearance)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"geral\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Estados para cada aba\n    const [generalData, setGeneralData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: userData.username,\n        profileImage: userData.profileImage || \"\",\n        currentPassword: \"\",\n        newPassword: \"\",\n        confirmPassword: \"\"\n    });\n    const [appearanceSettings, setAppearanceSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        fonte: contextSettings.fonte,\n        tamanhoFonte: contextSettings.tamanhoFonte,\n        palavrasPorSessao: contextSettings.palavrasPorSessao\n    });\n    const [aiEndpoints, setAiEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            nome: \"OpenRouter\",\n            url: \"https://openrouter.ai/api/v1/chat/completions\",\n            apiKey: \"\",\n            modeloPadrao: \"meta-llama/llama-3.1-8b-instruct:free\",\n            ativo: false\n        },\n        {\n            nome: \"DeepSeek\",\n            url: \"https://api.deepseek.com/v1/chat/completions\",\n            apiKey: \"\",\n            modeloPadrao: \"deepseek-chat\",\n            ativo: false\n        }\n    ]);\n    const [memories, setMemories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [memoryCategories, setMemoryCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddMemory, setShowAddMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddCategory, setShowAddCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newMemory, setNewMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        titulo: \"\",\n        conteudo: \"\",\n        cor: \"#3B82F6\",\n        categoria: null,\n        chatId: null,\n        global: true\n    });\n    const [newCategory, setNewCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        nome: \"\",\n        descricao: \"\",\n        cor: \"#3B82F6\"\n    });\n    const [showAddEndpoint, setShowAddEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newEndpoint, setNewEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        nome: \"\",\n        url: \"\",\n        apiKey: \"\",\n        modeloPadrao: \"\",\n        ativo: false\n    });\n    const [editingEndpoint, setEditingEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editEndpointData, setEditEndpointData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        nome: \"\",\n        url: \"\",\n        apiKey: \"\",\n        modeloPadrao: \"\",\n        ativo: false\n    });\n    // Carregar configurações do Firestore\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadConfigurations = async ()=>{\n            if (!userData.username) return;\n            try {\n                console.log(\"Carregando configura\\xe7\\xf5es para:\", userData.username);\n                const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\"));\n                if (configDoc.exists()) {\n                    const config = configDoc.data();\n                    console.log(\"Configura\\xe7\\xf5es carregadas:\", config);\n                    if (config.aparencia) {\n                        setAppearanceSettings(config.aparencia);\n                    }\n                    if (config.endpoints) {\n                        const endpointsArray = Object.values(config.endpoints);\n                        setAiEndpoints(endpointsArray);\n                    } else {\n                        // Manter endpoints padrão se não houver configuração salva\n                        setAiEndpoints([\n                            {\n                                nome: \"OpenRouter\",\n                                url: \"https://openrouter.ai/api/v1/chat/completions\",\n                                apiKey: \"\",\n                                modeloPadrao: \"meta-llama/llama-3.1-8b-instruct:free\",\n                                ativo: false\n                            },\n                            {\n                                nome: \"DeepSeek\",\n                                url: \"https://api.deepseek.com/v1/chat/completions\",\n                                apiKey: \"\",\n                                modeloPadrao: \"deepseek-chat\",\n                                ativo: false\n                            }\n                        ]);\n                    }\n                    if (config.memorias) {\n                        const memoriasArray = Object.values(config.memorias);\n                        setMemories(memoriasArray);\n                    }\n                    if (config.categorias) {\n                        const categoriasArray = Object.values(config.categorias);\n                        setMemoryCategories(categoriasArray);\n                    }\n                } else {\n                    console.log(\"Nenhuma configura\\xe7\\xe3o encontrada, usando padr\\xf5es\");\n                    // Configurações padrão se não existir documento\n                    setAppearanceSettings({\n                        fonte: \"Inter\",\n                        tamanhoFonte: 14,\n                        palavrasPorSessao: 5000\n                    });\n                }\n            } catch (error) {\n                console.error(\"Erro ao carregar configura\\xe7\\xf5es:\", error);\n            }\n        };\n        if (isOpen && userData.username) {\n            // Reset do estado do formulário geral quando abrir o modal\n            setGeneralData({\n                username: userData.username,\n                profileImage: userData.profileImage || \"\",\n                currentPassword: \"\",\n                newPassword: \"\",\n                confirmPassword: \"\"\n            });\n            loadConfigurations();\n            loadChats();\n        }\n    }, [\n        isOpen,\n        userData.username,\n        userData.profileImage\n    ]);\n    // Função para carregar chats do usuário\n    const loadChats = async ()=>{\n        if (!(userData === null || userData === void 0 ? void 0 : userData.username)) return;\n        try {\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", userData.username, \"conversas\");\n            const chatsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)(chatsRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.orderBy)(\"lastUpdatedAt\", \"desc\"));\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(chatsQuery);\n            const loadedChats = [];\n            chatsSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                loadedChats.push({\n                    id: doc.id,\n                    name: data.name || \"Conversa sem nome\",\n                    lastMessage: data.ultimaMensagem || \"Nenhuma mensagem ainda\",\n                    lastMessageTime: data.ultimaMensagemEm || data.createdAt\n                });\n            });\n            setChats(loadedChats);\n        } catch (error) {\n            console.error(\"Erro ao carregar chats:\", error);\n        }\n    };\n    // Função auxiliar para deletar todos os dados do Storage de um usuário\n    const deleteUserStorageData = async (username)=>{\n        try {\n            console.log(\"Iniciando exclus\\xe3o de dados do Storage para:\", username);\n            // Deletar toda a pasta do usuário no Storage\n            const userStorageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.storage, \"usuarios/\".concat(username));\n            const userStorageList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.listAll)(userStorageRef);\n            // Função recursiva para deletar pastas e arquivos\n            const deleteRecursively = async (folderRef)=>{\n                const folderList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.listAll)(folderRef);\n                // Deletar todos os arquivos na pasta atual\n                const fileDeletePromises = folderList.items.map((item)=>(0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.deleteObject)(item));\n                await Promise.all(fileDeletePromises);\n                // Deletar recursivamente todas as subpastas\n                const folderDeletePromises = folderList.prefixes.map((prefix)=>deleteRecursively(prefix));\n                await Promise.all(folderDeletePromises);\n            };\n            await deleteRecursively(userStorageRef);\n            console.log(\"Todos os dados do Storage deletados para:\", username);\n        } catch (error) {\n            console.log(\"Erro ao deletar dados do Storage ou pasta n\\xe3o encontrada:\", error);\n        }\n    };\n    // Função auxiliar para deletar recursivamente todos os documentos de um usuário\n    const deleteUserDocuments = async (username)=>{\n        try {\n            console.log(\"Iniciando exclus\\xe3o de documentos para:\", username);\n            // Deletar subcoleção de configurações\n            try {\n                const configDoc = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n                const configSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(configDoc);\n                if (configSnapshot.exists()) {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.deleteDoc)(configDoc);\n                    console.log(\"Configura\\xe7\\xf5es deletadas\");\n                }\n            } catch (error) {\n                console.log(\"Erro ao deletar configura\\xe7\\xf5es:\", error);\n            }\n            // Deletar outras subcoleções se existirem (chats, histórico, etc.)\n            try {\n                const chatsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", username, \"chats\");\n                const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(chatsCollection);\n                const deletePromises = chatsSnapshot.docs.map((doc)=>(0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.deleteDoc)(doc.ref));\n                await Promise.all(deletePromises);\n                console.log(\"Chats deletados\");\n            } catch (error) {\n                console.log(\"Erro ao deletar chats:\", error);\n            }\n            // Deletar documento principal do usuário\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", username);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.deleteDoc)(userDocRef);\n            console.log(\"Documento principal do usu\\xe1rio deletado\");\n        } catch (error) {\n            console.error(\"Erro ao deletar documentos do usu\\xe1rio:\", error);\n            throw error;\n        }\n    };\n    // Atualizar username no documento principal\n    const updateUsername = async function(newUsername) {\n        let showSuccessAlert = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        if (!userData.username || !newUsername || newUsername === userData.username) {\n            if (showSuccessAlert) alert(\"Nome de usu\\xe1rio inv\\xe1lido ou igual ao atual.\");\n            return false;\n        }\n        if (newUsername.length < 3) {\n            if (showSuccessAlert) alert(\"Nome de usu\\xe1rio deve ter pelo menos 3 caracteres.\");\n            return false;\n        }\n        let newUserCreated = false;\n        try {\n            console.log(\"Atualizando username de\", userData.username, \"para\", newUsername);\n            // Verificar se o novo username já existe\n            const newUserDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", newUsername));\n            if (newUserDoc.exists()) {\n                if (showSuccessAlert) alert(\"Este nome de usu\\xe1rio j\\xe1 est\\xe1 em uso. Escolha outro.\");\n                return false;\n            }\n            // Buscar o documento atual pelo username antigo\n            const oldUserDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", userData.username);\n            const oldUserDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(oldUserDocRef);\n            if (!oldUserDoc.exists()) {\n                if (showSuccessAlert) alert(\"Usu\\xe1rio n\\xe3o encontrado.\");\n                return false;\n            }\n            const currentData = oldUserDoc.data();\n            // Criar novo documento com o novo username\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", newUsername), {\n                ...currentData,\n                username: newUsername,\n                updatedAt: new Date().toISOString()\n            });\n            newUserCreated = true;\n            console.log(\"Novo documento criado para:\", newUsername);\n            // Copiar todas as configurações e subcoleções\n            try {\n                // Copiar configurações principais\n                const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\"));\n                if (configDoc.exists()) {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", newUsername, \"configuracoes\", \"settings\"), configDoc.data());\n                    console.log(\"Configura\\xe7\\xf5es copiadas para novo username\");\n                }\n                // Copiar chats se existirem\n                try {\n                    const chatsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", userData.username, \"chats\");\n                    const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(chatsCollection);\n                    for (const chatDoc of chatsSnapshot.docs){\n                        const chatData = chatDoc.data();\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", newUsername, \"chats\", chatDoc.id), chatData);\n                    }\n                    if (chatsSnapshot.docs.length > 0) {\n                        console.log(\"\".concat(chatsSnapshot.docs.length, \" chats copiados para novo username\"));\n                    }\n                } catch (chatsError) {\n                    console.log(\"Erro ao copiar chats:\", chatsError);\n                }\n            } catch (configError) {\n                console.log(\"Erro ao copiar dados:\", configError);\n            }\n            // Deletar todos os documentos do usuário antigo\n            await deleteUserDocuments(userData.username);\n            console.log(\"Todos os documentos do usu\\xe1rio antigo foram deletados\");\n            // Atualizar estado local\n            onUserDataUpdate({\n                ...userData,\n                username: newUsername\n            });\n            if (showSuccessAlert) alert(\"Nome de usu\\xe1rio atualizado com sucesso!\");\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar username:\", error);\n            // Se houve erro e o novo usuário foi criado, tentar fazer rollback\n            if (newUserCreated) {\n                try {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", newUsername));\n                    console.log(\"Rollback realizado - novo usu\\xe1rio deletado\");\n                } catch (rollbackError) {\n                    console.error(\"Erro no rollback:\", rollbackError);\n                }\n            }\n            if (showSuccessAlert) alert(\"Erro ao atualizar nome de usu\\xe1rio: \".concat(error instanceof Error ? error.message : \"Erro desconhecido\"));\n            return false;\n        }\n    };\n    // Salvar configurações no Firestore\n    const saveConfigurations = async ()=>{\n        if (!userData.username) {\n            alert(\"Erro: usu\\xe1rio n\\xe3o identificado\");\n            return;\n        }\n        try {\n            setLoading(true);\n            // Verificar se o username foi alterado e atualizá-lo primeiro\n            if (generalData.username !== userData.username) {\n                const usernameUpdated = await updateUsername(generalData.username, false);\n                if (!usernameUpdated) {\n                    // Se falhou ao atualizar o username, interromper o processo\n                    return;\n                }\n            }\n            // Determinar qual username usar (o novo se foi alterado)\n            const currentUsername = generalData.username !== userData.username ? generalData.username : userData.username;\n            const configData = {\n                aparencia: {\n                    fonte: appearanceSettings.fonte,\n                    tamanhoFonte: appearanceSettings.tamanhoFonte,\n                    palavrasPorSessao: appearanceSettings.palavrasPorSessao\n                },\n                endpoints: {},\n                memorias: {},\n                categorias: {},\n                updatedAt: new Date().toISOString()\n            };\n            // Converter arrays para objetos\n            aiEndpoints.forEach((endpoint, index)=>{\n                configData.endpoints[endpoint.nome || \"endpoint_\".concat(index)] = endpoint;\n            });\n            memories.forEach((memory, index)=>{\n                configData.memorias[\"memoria_\".concat(index)] = memory;\n            });\n            memoryCategories.forEach((category, index)=>{\n                configData.categorias[category.nome || \"categoria_\".concat(index)] = category;\n            });\n            console.log(\"Salvando configura\\xe7\\xf5es para:\", currentUsername);\n            console.log(\"Dados a serem salvos:\", configData);\n            // Usar setDoc com merge para não sobrescrever outros dados\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", currentUsername, \"configuracoes\", \"settings\");\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.setDoc)(docRef, configData);\n            console.log(\"Configura\\xe7\\xf5es salvas com sucesso no Firestore\");\n            // Atualizar as configurações de aparência no contexto\n            await refreshSettings();\n            alert(\"Configura\\xe7\\xf5es salvas com sucesso!\");\n        } catch (error) {\n            console.error(\"Erro ao salvar configura\\xe7\\xf5es:\", error);\n            alert(\"Erro ao salvar configura\\xe7\\xf5es: \".concat(error instanceof Error ? error.message : \"Erro desconhecido\"));\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    // Funções utilitárias\n    const handleProfileImageUpload = async (file)=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const imageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.storage, \"usuarios/\".concat(userData.username, \"/profile.jpg\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.uploadBytes)(imageRef, file);\n            const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_5__.getDownloadURL)(imageRef);\n            setGeneralData((prev)=>({\n                    ...prev,\n                    profileImage: downloadURL\n                }));\n            // Atualizar no Firestore\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_7__.db, \"usuarios\", userData.username);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)(userDocRef, {\n                profileImage: downloadURL\n            });\n            onUserDataUpdate({\n                ...userData,\n                profileImage: downloadURL\n            });\n            alert(\"Foto de perfil atualizada com sucesso!\");\n        } catch (error) {\n            console.error(\"Erro ao fazer upload da imagem:\", error);\n            alert(\"Erro ao atualizar foto de perfil.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePasswordChange = async ()=>{\n        if (!user || !generalData.currentPassword || !generalData.newPassword) {\n            alert(\"Preencha todos os campos de senha.\");\n            return;\n        }\n        if (generalData.newPassword !== generalData.confirmPassword) {\n            alert(\"As senhas n\\xe3o coincidem.\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const credential = firebase_auth__WEBPACK_IMPORTED_MODULE_6__.EmailAuthProvider.credential(user.email, generalData.currentPassword);\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_6__.reauthenticateWithCredential)(user, credential);\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_6__.updatePassword)(user, generalData.newPassword);\n            setGeneralData((prev)=>({\n                    ...prev,\n                    currentPassword: \"\",\n                    newPassword: \"\",\n                    confirmPassword: \"\"\n                }));\n            alert(\"Senha alterada com sucesso!\");\n        } catch (error) {\n            console.error(\"Erro ao alterar senha:\", error);\n            alert(\"Erro ao alterar senha. Verifique a senha atual.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogout = async ()=>{\n        if (confirm(\"Tem certeza que deseja sair?\")) {\n            await logout();\n            onClose();\n        }\n    };\n    // Funções para gerenciar endpoints de IA\n    const handleAddEndpoint = ()=>{\n        if (!newEndpoint.nome || !newEndpoint.url || !newEndpoint.apiKey) {\n            alert(\"Preencha todos os campos obrigat\\xf3rios.\");\n            return;\n        }\n        setAiEndpoints((prev)=>[\n                ...prev,\n                {\n                    ...newEndpoint\n                }\n            ]);\n        setNewEndpoint({\n            nome: \"\",\n            url: \"\",\n            apiKey: \"\",\n            modeloPadrao: \"\",\n            ativo: false\n        });\n        setShowAddEndpoint(false);\n        alert(\"Endpoint adicionado com sucesso!\");\n    };\n    const handleToggleEndpoint = (index)=>{\n        setAiEndpoints((prev)=>prev.map((endpoint, i)=>i === index ? {\n                    ...endpoint,\n                    ativo: !endpoint.ativo\n                } : endpoint));\n    };\n    const handleDeleteEndpoint = (index)=>{\n        if (confirm(\"Tem certeza que deseja deletar este endpoint?\")) {\n            setAiEndpoints((prev)=>prev.filter((_, i)=>i !== index));\n        }\n    };\n    const handleEditEndpoint = (index)=>{\n        const endpoint = aiEndpoints[index];\n        setEditEndpointData({\n            ...endpoint\n        });\n        setEditingEndpoint(index);\n    };\n    const handleSaveEditEndpoint = ()=>{\n        if (editingEndpoint === null) return;\n        if (!editEndpointData.apiKey || !editEndpointData.modeloPadrao) {\n            alert(\"API Key e Modelo Padr\\xe3o s\\xe3o obrigat\\xf3rios.\");\n            return;\n        }\n        setAiEndpoints((prev)=>prev.map((endpoint, i)=>i === editingEndpoint ? {\n                    ...editEndpointData\n                } : endpoint));\n        setEditingEndpoint(null);\n        setEditEndpointData({\n            nome: \"\",\n            url: \"\",\n            apiKey: \"\",\n            modeloPadrao: \"\",\n            ativo: false\n        });\n        alert(\"Endpoint atualizado com sucesso!\");\n    };\n    const handleCancelEditEndpoint = ()=>{\n        setEditingEndpoint(null);\n        setEditEndpointData({\n            nome: \"\",\n            url: \"\",\n            apiKey: \"\",\n            modeloPadrao: \"\",\n            ativo: false\n        });\n    };\n    const handleTestEndpoint = async (endpoint)=>{\n        if (!endpoint.apiKey) {\n            alert(\"API Key \\xe9 necess\\xe1ria para testar o endpoint.\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const response = await fetch(endpoint.url, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(endpoint.apiKey)\n                },\n                body: JSON.stringify({\n                    model: endpoint.modeloPadrao || \"gpt-3.5-turbo\",\n                    messages: [\n                        {\n                            role: \"user\",\n                            content: \"Test message\"\n                        }\n                    ],\n                    max_tokens: 10\n                })\n            });\n            if (response.ok) {\n                alert(\"✅ Endpoint testado com sucesso!\");\n            } else {\n                alert(\"❌ Erro ao testar endpoint. Verifique as configura\\xe7\\xf5es.\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao testar endpoint:\", error);\n            alert(\"❌ Erro ao conectar com o endpoint.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Funções para gerenciar memórias\n    const handleAddCategory = ()=>{\n        if (!newCategory.nome) {\n            alert(\"Nome da categoria \\xe9 obrigat\\xf3rio.\");\n            return;\n        }\n        setMemoryCategories((prev)=>[\n                ...prev,\n                {\n                    ...newCategory\n                }\n            ]);\n        setNewCategory({\n            nome: \"\",\n            descricao: \"\",\n            cor: \"#3B82F6\"\n        });\n        setShowAddCategory(false);\n        alert(\"Categoria criada com sucesso!\");\n    };\n    const handleAddMemory = ()=>{\n        if (!newMemory.titulo || !newMemory.conteudo) {\n            alert(\"T\\xedtulo e conte\\xfado s\\xe3o obrigat\\xf3rios.\");\n            return;\n        }\n        setMemories((prev)=>[\n                ...prev,\n                {\n                    ...newMemory\n                }\n            ]);\n        setNewMemory({\n            titulo: \"\",\n            conteudo: \"\",\n            cor: \"#3B82F6\",\n            categoria: null,\n            chatId: null,\n            global: true\n        });\n        setShowAddMemory(false);\n        alert(\"Mem\\xf3ria criada com sucesso!\");\n    };\n    const handleDeleteMemory = (index)=>{\n        if (confirm(\"Tem certeza que deseja deletar esta mem\\xf3ria?\")) {\n            setMemories((prev)=>prev.filter((_, i)=>i !== index));\n        }\n    };\n    const handleDeleteCategory = (index)=>{\n        if (confirm(\"Tem certeza que deseja deletar esta categoria?\")) {\n            setMemoryCategories((prev)=>prev.filter((_, i)=>i !== index));\n        }\n    };\n    const colors = [\n        \"#3B82F6\",\n        \"#EF4444\",\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#8B5CF6\",\n        \"#EC4899\",\n        \"#06B6D4\",\n        \"#84CC16\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    scale: 0.95,\n                    opacity: 0\n                },\n                animate: {\n                    scale: 1,\n                    opacity: 1\n                },\n                exit: {\n                    scale: 0.95,\n                    opacity: 0\n                },\n                className: \"bg-gradient-to-br from-blue-900/95 to-blue-800/95 backdrop-blur-sm border border-white/20 rounded-2xl w-full max-w-6xl max-h-[95vh] overflow-hidden mx-4 lg:mx-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 lg:p-6 border-b border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl lg:text-2xl font-bold text-white\",\n                                        children: \"Configura\\xe7\\xf5es\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-sm lg:hidden mt-1\",\n                                        children: [\n                                            activeTab === \"geral\" && \"Informa\\xe7\\xf5es pessoais e senha\",\n                                            activeTab === \"aparencia\" && \"Personaliza\\xe7\\xe3o da interface\",\n                                            activeTab === \"ia\" && \"Endpoints de intelig\\xeancia artificial\",\n                                            activeTab === \"memoria\" && \"Sistema de mem\\xf3rias\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 738,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-white/60 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 747,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                        lineNumber: 737,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full lg:w-64 bg-white/5 border-b lg:border-b-0 lg:border-r border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"p-2 lg:p-4 space-y-1 lg:space-y-2 overflow-x-auto lg:overflow-x-visible\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex lg:flex-col space-x-2 lg:space-x-0 lg:space-y-2 min-w-max lg:min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"geral\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"geral\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"Geral\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"aparencia\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"aparencia\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"Apar\\xeancia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"ia\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"ia\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 800,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"IA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 792,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"memoria\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"memoria\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 815,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"Mem\\xf3ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 759,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 lg:p-6 overflow-y-auto max-h-[calc(95vh-200px)]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: [\n                                        activeTab === \"geral\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Configura\\xe7\\xf5es Gerais\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 837,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Foto de Perfil\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: generalData.profileImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: generalData.profileImage,\n                                                                            alt: \"Profile\",\n                                                                            className: \"w-20 h-20 rounded-full object-cover border-2 border-white/20\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 845,\n                                                                            columnNumber: 33\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-white font-bold text-2xl\",\n                                                                                children: userData.username.charAt(0).toUpperCase()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 852,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 851,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 843,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    var _fileInputRef_current;\n                                                                                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                                                },\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                                children: \"Alterar Foto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 859,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/60 text-sm mt-2\",\n                                                                                children: \"JPG, PNG ou GIF. M\\xe1ximo 5MB.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 866,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 858,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                ref: fileInputRef,\n                                                                type: \"file\",\n                                                                accept: \"image/*\",\n                                                                onChange: (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) handleProfileImageUpload(file);\n                                                                },\n                                                                className: \"hidden\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 871,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 840,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Nome de Usu\\xe1rio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: generalData.username,\n                                                                        onChange: (e)=>setGeneralData((prev)=>({\n                                                                                    ...prev,\n                                                                                    username: e.target.value\n                                                                                })),\n                                                                        className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        placeholder: \"Digite seu nome de usu\\xe1rio\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 887,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    generalData.username !== userData.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-yellow-300 text-sm\",\n                                                                            children: '⚠️ Nome de usu\\xe1rio alterado. Clique em \"Salvar Configura\\xe7\\xf5es\" para aplicar as mudan\\xe7as.'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 898,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 897,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 886,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Alterar Senha\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 908,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Senha Atual\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 911,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: generalData.currentPassword,\n                                                                                onChange: (e)=>setGeneralData((prev)=>({\n                                                                                            ...prev,\n                                                                                            currentPassword: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Digite sua senha atual\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 914,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 910,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Nova Senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 925,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: generalData.newPassword,\n                                                                                onChange: (e)=>setGeneralData((prev)=>({\n                                                                                            ...prev,\n                                                                                            newPassword: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Digite sua nova senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 928,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 924,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Confirmar Nova Senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 939,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: generalData.confirmPassword,\n                                                                                onChange: (e)=>setGeneralData((prev)=>({\n                                                                                            ...prev,\n                                                                                            confirmPassword: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Confirme sua nova senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 942,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 938,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handlePasswordChange,\n                                                                        disabled: loading,\n                                                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: loading ? \"Alterando...\" : \"Alterar Senha\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 952,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 909,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 907,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"geral\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 829,\n                                            columnNumber: 21\n                                        }, this),\n                                        activeTab === \"aparencia\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Configura\\xe7\\xf5es de Apar\\xeancia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 975,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Fonte do Chat\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 979,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Fam\\xedlia da Fonte\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 982,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                value: appearanceSettings.fonte,\n                                                                                onChange: (e)=>setAppearanceSettings((prev)=>({\n                                                                                            ...prev,\n                                                                                            fonte: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Inter\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Inter\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 992,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Roboto\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Roboto\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 993,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"JetBrains Mono\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"JetBrains Mono\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 994,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Lato\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Lato\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 995,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Fira Code\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Fira Code\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 996,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Merriweather\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Merriweather\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 997,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Open Sans\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Open Sans\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 998,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Source Sans Pro\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Source Sans Pro\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 999,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Poppins\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Poppins\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1000,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Nunito\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Nunito\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1001,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 985,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 981,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 border border-white/10 rounded-lg p-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/80 text-sm mb-2\",\n                                                                                children: \"Pr\\xe9-visualiza\\xe7\\xe3o:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1006,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-white p-3 bg-white/5 rounded border border-white/10\",\n                                                                                style: {\n                                                                                    fontFamily: appearanceSettings.fonte,\n                                                                                    fontSize: \"\".concat(appearanceSettings.tamanhoFonte, \"px\")\n                                                                                },\n                                                                                children: \"Esta \\xe9 uma mensagem de exemplo para visualizar a fonte selecionada. Lorem ipsum dolor sit amet, consectetur adipiscing elit.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1007,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1005,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 980,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 978,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Tamanho da Fonte\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1020,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                            children: [\n                                                                                \"Tamanho: \",\n                                                                                appearanceSettings.tamanhoFonte,\n                                                                                \"px\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1023,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"range\",\n                                                                            min: \"10\",\n                                                                            max: \"24\",\n                                                                            value: appearanceSettings.tamanhoFonte,\n                                                                            onChange: (e)=>setAppearanceSettings((prev)=>({\n                                                                                        ...prev,\n                                                                                        tamanhoFonte: parseInt(e.target.value)\n                                                                                    })),\n                                                                            className: \"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1026,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between text-xs text-white/60 mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"10px\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 1035,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"24px\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 1036,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1034,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                    lineNumber: 1022,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1021,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1019,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Sess\\xf5es de Chat\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1044,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                        className: \"text-white font-medium\",\n                                                                                        children: \"Divis\\xe3o Autom\\xe1tica\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1048,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white/60 text-sm\",\n                                                                                        children: \"Dividir chats longos em sess\\xf5es baseadas na contagem de palavras\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1049,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1047,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"bg-blue-600 relative inline-flex h-6 w-11 items-center rounded-full transition-colors\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"translate-x-6 inline-block h-4 w-4 transform rounded-full bg-white transition\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 1056,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1053,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1046,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: [\n                                                                                    \"Palavras por Sess\\xe3o: \",\n                                                                                    appearanceSettings.palavrasPorSessao.toLocaleString()\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1061,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"range\",\n                                                                                min: \"1000\",\n                                                                                max: \"20000\",\n                                                                                step: \"500\",\n                                                                                value: appearanceSettings.palavrasPorSessao,\n                                                                                onChange: (e)=>setAppearanceSettings((prev)=>({\n                                                                                            ...prev,\n                                                                                            palavrasPorSessao: parseInt(e.target.value)\n                                                                                        })),\n                                                                                className: \"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1064,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs text-white/60 mt-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"1.000\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1074,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"20.000\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1075,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1073,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1060,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-blue-300 text-sm\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCA1 \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Dica:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 1081,\n                                                                                    columnNumber: 36\n                                                                                }, this),\n                                                                                \" Sess\\xf5es menores carregam mais r\\xe1pido, mas podem fragmentar conversas longas. Recomendamos entre 3.000-8.000 palavras para melhor experi\\xeancia.\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1080,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1079,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1045,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1043,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 974,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"aparencia\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 967,\n                                            columnNumber: 21\n                                        }, this),\n                                        activeTab === \"ia\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Intelig\\xeancia Artificial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1100,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80\",\n                                                                children: \"Gerencie seus endpoints de IA personalizados\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1104,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowAddEndpoint(!showAddEndpoint),\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M12 4v16m8-8H4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1113,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1112,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Adicionar Endpoint\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1115,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1107,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1103,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    showAddEndpoint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: \"auto\"\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Novo Endpoint\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1127,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Nome do Endpoint *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1130,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newEndpoint.nome,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            nome: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Ex: Meu Endpoint\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1133,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1129,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"URL do Endpoint *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1144,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"url\",\n                                                                                value: newEndpoint.url,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            url: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"https://api.exemplo.com/v1/chat/completions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1147,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1143,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"API Key *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1158,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: newEndpoint.apiKey,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            apiKey: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"sk-...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1161,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1157,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Modelo Padr\\xe3o\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1172,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newEndpoint.modeloPadrao,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            modeloPadrao: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"gpt-3.5-turbo\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1175,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1171,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1128,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3 mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowAddEndpoint(false),\n                                                                        className: \"px-4 py-2 text-white/70 hover:text-white transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1187,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleAddEndpoint,\n                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: \"Adicionar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1193,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1186,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1121,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: aiEndpoints.map((endpoint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-3 h-3 rounded-full \".concat(endpoint.ativo ? \"bg-green-500\" : \"bg-gray-500\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1210,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-lg font-semibold text-white\",\n                                                                                        children: endpoint.nome\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1211,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    (endpoint.nome === \"OpenRouter\" || endpoint.nome === \"DeepSeek\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full\",\n                                                                                        children: \"Pr\\xe9-configurado\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1213,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1209,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleToggleEndpoint(index),\n                                                                                        className: \"px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200 \".concat(endpoint.ativo ? \"bg-green-600 hover:bg-green-700 text-white\" : \"bg-gray-600 hover:bg-gray-700 text-white\"),\n                                                                                        children: endpoint.ativo ? \"Ativo\" : \"Inativo\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1219,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleTestEndpoint(endpoint),\n                                                                                        disabled: loading || !endpoint.apiKey,\n                                                                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Testar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1229,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleEditEndpoint(index),\n                                                                                        className: \"bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Editar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1237,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    endpoint.nome !== \"OpenRouter\" && endpoint.nome !== \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteEndpoint(index),\n                                                                                        className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Deletar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1245,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1218,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1208,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"URL:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1258,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white font-mono text-xs break-all\",\n                                                                                        children: endpoint.url\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1259,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1257,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"Modelo:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1262,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white\",\n                                                                                        children: endpoint.modeloPadrao || \"N\\xe3o especificado\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1263,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1261,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"API Key:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1266,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white font-mono text-xs\",\n                                                                                        children: endpoint.apiKey ? \"••••••••••••\" + endpoint.apiKey.slice(-4) : \"N\\xe3o configurada\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1267,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1265,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"Status:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1272,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium \".concat(endpoint.ativo ? \"text-green-400\" : \"text-gray-400\"),\n                                                                                        children: endpoint.ativo ? \"Ativo\" : \"Inativo\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1273,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1271,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1256,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    editingEndpoint === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                                        initial: {\n                                                                            opacity: 0,\n                                                                            height: 0\n                                                                        },\n                                                                        animate: {\n                                                                            opacity: 1,\n                                                                            height: \"auto\"\n                                                                        },\n                                                                        exit: {\n                                                                            opacity: 0,\n                                                                            height: 0\n                                                                        },\n                                                                        className: \"mt-4 pt-4 border-t border-white/10\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                className: \"text-white font-semibold mb-4\",\n                                                                                children: \"Editar Endpoint\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1287,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                                children: \"API Key *\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1290,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"password\",\n                                                                                                value: editEndpointData.apiKey,\n                                                                                                onChange: (e)=>setEditEndpointData((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            apiKey: e.target.value\n                                                                                                        })),\n                                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n                                                                                                placeholder: \"Cole sua API Key aqui...\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1293,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1289,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                                children: \"Modelo Padr\\xe3o *\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1304,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: editEndpointData.modeloPadrao,\n                                                                                                onChange: (e)=>setEditEndpointData((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            modeloPadrao: e.target.value\n                                                                                                        })),\n                                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n                                                                                                placeholder: \"Ex: gpt-4, claude-3-sonnet, etc.\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1307,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1303,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1288,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-end space-x-2 mt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: handleCancelEditEndpoint,\n                                                                                        className: \"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Cancelar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1319,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: handleSaveEditEndpoint,\n                                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Salvar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1326,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1318,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1281,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    (endpoint.nome === \"OpenRouter\" || endpoint.nome === \"DeepSeek\") && !endpoint.apiKey && editingEndpoint !== index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-4 pt-4 border-t border-white/10\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Configure sua API Key:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1340,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"password\",\n                                                                                        placeholder: \"Cole sua API Key aqui...\",\n                                                                                        className: \"flex-1 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n                                                                                        onChange: (e)=>{\n                                                                                            const newKey = e.target.value;\n                                                                                            setAiEndpoints((prev)=>prev.map((ep, i)=>i === index ? {\n                                                                                                        ...ep,\n                                                                                                        apiKey: newKey\n                                                                                                    } : ep));\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1344,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleToggleEndpoint(index),\n                                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Salvar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1357,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1343,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1339,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1207,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1205,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 1099,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"ia\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 1092,\n                                            columnNumber: 21\n                                        }, this),\n                                        activeTab === \"memoria\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Sistema de Mem\\xf3ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1383,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-3 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowAddCategory(!showAddCategory),\n                                                                className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1393,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1392,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Nova Categoria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1396,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1387,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowAddMemory(!showAddMemory),\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1404,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1403,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Nova Mem\\xf3ria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1407,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1398,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1386,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    showAddCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: \"auto\"\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Nova Categoria\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1419,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Nome da Categoria *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1422,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newCategory.nome,\n                                                                                onChange: (e)=>setNewCategory((prev)=>({\n                                                                                            ...prev,\n                                                                                            nome: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Ex: Trabalho, Pessoal, Projetos...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1425,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1421,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Descri\\xe7\\xe3o\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1436,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                value: newCategory.descricao,\n                                                                                onChange: (e)=>setNewCategory((prev)=>({\n                                                                                            ...prev,\n                                                                                            descricao: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                                                                                rows: 3,\n                                                                                placeholder: \"Descreva o prop\\xf3sito desta categoria...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1439,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1435,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Cor da Categoria\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1450,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: colors.map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>setNewCategory((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    cor: color\n                                                                                                })),\n                                                                                        className: \"w-8 h-8 rounded-full border-2 transition-all duration-200 \".concat(newCategory.cor === color ? \"border-white scale-110\" : \"border-white/30\"),\n                                                                                        style: {\n                                                                                            backgroundColor: color\n                                                                                        }\n                                                                                    }, color, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1455,\n                                                                                        columnNumber: 37\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1453,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1449,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1420,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3 mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowAddCategory(false),\n                                                                        className: \"px-4 py-2 text-white/70 hover:text-white transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1468,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleAddCategory,\n                                                                        className: \"bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: \"Criar Categoria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1474,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1467,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1413,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    showAddMemory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: \"auto\"\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Nova Mem\\xf3ria\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1493,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"T\\xedtulo da Mem\\xf3ria *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1496,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newMemory.titulo,\n                                                                                onChange: (e)=>setNewMemory((prev)=>({\n                                                                                            ...prev,\n                                                                                            titulo: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Ex: Informa\\xe7\\xf5es importantes sobre...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1499,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1495,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Conte\\xfado *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1510,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                value: newMemory.conteudo,\n                                                                                onChange: (e)=>setNewMemory((prev)=>({\n                                                                                            ...prev,\n                                                                                            conteudo: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                                                                                rows: 4,\n                                                                                placeholder: \"Digite o conte\\xfado da mem\\xf3ria...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1513,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1509,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                        children: \"Categoria\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1525,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                        value: newMemory.categoria || \"\",\n                                                                                        onChange: (e)=>setNewMemory((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    categoria: e.target.value || null\n                                                                                                })),\n                                                                                        className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"\",\n                                                                                                className: \"bg-gray-800\",\n                                                                                                children: \"Sem categoria\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1535,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            memoryCategories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: category.nome,\n                                                                                                    className: \"bg-gray-800\",\n                                                                                                    children: category.nome\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                    lineNumber: 1537,\n                                                                                                    columnNumber: 39\n                                                                                                }, this))\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1528,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1524,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                        children: \"Cor da Mem\\xf3ria\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1544,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex space-x-2\",\n                                                                                        children: colors.slice(0, 4).map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>setNewMemory((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            cor: color\n                                                                                                        })),\n                                                                                                className: \"w-8 h-8 rounded-full border-2 transition-all duration-200 \".concat(newMemory.cor === color ? \"border-white scale-110\" : \"border-white/30\"),\n                                                                                                style: {\n                                                                                                    backgroundColor: color\n                                                                                                }\n                                                                                            }, color, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1549,\n                                                                                                columnNumber: 39\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1547,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1543,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1523,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium\",\n                                                                                children: \"Escopo da Mem\\xf3ria\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1562,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                value: newMemory.global ? \"global\" : newMemory.chatId || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    const value = e.target.value;\n                                                                                    if (value === \"global\") {\n                                                                                        setNewMemory((prev)=>({\n                                                                                                ...prev,\n                                                                                                global: true,\n                                                                                                chatId: null\n                                                                                            }));\n                                                                                    } else {\n                                                                                        setNewMemory((prev)=>({\n                                                                                                ...prev,\n                                                                                                global: false,\n                                                                                                chatId: value\n                                                                                            }));\n                                                                                    }\n                                                                                },\n                                                                                className: \"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"global\",\n                                                                                        className: \"bg-gray-800 text-white\",\n                                                                                        children: \"\\uD83C\\uDF10 Global (todos os chats)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1579,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                                                                        label: \"Chats Espec\\xedficos\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: chat.id,\n                                                                                                className: \"bg-gray-800 text-white\",\n                                                                                                children: [\n                                                                                                    \"\\uD83D\\uDCAC \",\n                                                                                                    chat.name\n                                                                                                ]\n                                                                                            }, chat.id, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1584,\n                                                                                                columnNumber: 39\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1582,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1565,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/50 text-xs\",\n                                                                                children: newMemory.global ? \"Esta mem\\xf3ria ficar\\xe1 dispon\\xedvel em todos os chats\" : \"Esta mem\\xf3ria ficar\\xe1 dispon\\xedvel apenas no chat selecionado\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1590,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1561,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1494,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3 mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowAddMemory(false),\n                                                                        className: \"px-4 py-2 text-white/70 hover:text-white transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1599,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleAddMemory,\n                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: \"Criar Mem\\xf3ria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1605,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1598,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1487,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    memoryCategories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Categorias\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1619,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                                children: memoryCategories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-between mb-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"w-4 h-4 rounded-full\",\n                                                                                                style: {\n                                                                                                    backgroundColor: category.cor\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1625,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                                className: \"text-white font-medium\",\n                                                                                                children: category.nome\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1629,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1624,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteCategory(index),\n                                                                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1636,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                            lineNumber: 1635,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1631,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1623,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            category.descricao && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/60 text-sm\",\n                                                                                children: category.descricao\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1642,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1622,\n                                                                        columnNumber: 33\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1620,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1618,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: [\n                                                                    \"Mem\\xf3rias (\",\n                                                                    memories.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1652,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            memories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white/5 border border-white/10 rounded-xl p-8 text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-12 h-12 text-white/40 mx-auto mb-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1658,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1657,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/60\",\n                                                                        children: \"Nenhuma mem\\xf3ria criada ainda\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1661,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/40 text-sm mt-1\",\n                                                                        children: 'Clique em \"Nova Mem\\xf3ria\" para come\\xe7ar'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1662,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1656,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: memories.map((memory, index)=>{\n                                                                    var _chats_find;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-start justify-between mb-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-3\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"w-4 h-4 rounded-full flex-shrink-0\",\n                                                                                                style: {\n                                                                                                    backgroundColor: memory.cor\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1672,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                                        className: \"text-white font-semibold\",\n                                                                                                        children: memory.titulo\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                        lineNumber: 1677,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"flex items-center space-x-2 mt-1\",\n                                                                                                        children: [\n                                                                                                            memory.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                className: \"bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full\",\n                                                                                                                children: memory.categoria\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                                lineNumber: 1680,\n                                                                                                                columnNumber: 45\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                className: \"text-xs px-2 py-1 rounded-full \".concat(memory.global ? \"bg-green-500/20 text-green-300\" : \"bg-orange-500/20 text-orange-300\"),\n                                                                                                                children: memory.global ? \"\\uD83C\\uDF10 Global\" : \"\\uD83D\\uDCAC \".concat(((_chats_find = chats.find((chat)=>chat.id === memory.chatId)) === null || _chats_find === void 0 ? void 0 : _chats_find.name) || \"Chat Espec\\xedfico\")\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                                lineNumber: 1684,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                        lineNumber: 1678,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1676,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1671,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteMemory(index),\n                                                                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1702,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                            lineNumber: 1701,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1697,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1670,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/80 text-sm leading-relaxed\",\n                                                                                children: memory.conteudo\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1707,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1669,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1667,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1651,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 1382,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"memoria\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 1375,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 827,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                        lineNumber: 757,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-t border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogout,\n                                    className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                    children: \"Sair da Conta\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 1725,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 1724,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"px-6 py-2 text-white/70 hover:text-white transition-colors font-medium\",\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 1737,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: saveConfigurations,\n                                        disabled: loading,\n                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                        children: loading ? \"Salvando...\" : \"Salvar Configura\\xe7\\xf5es\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 1743,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 1736,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                        lineNumber: 1723,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                lineNumber: 727,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n            lineNumber: 721,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n        lineNumber: 719,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsModal, \"CUpttT5PemxXjE9CIpWfTdWdjsQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__.useAppearance\n    ];\n});\n_c = SettingsModal;\nvar _c;\n$RefreshReg$(_c, \"SettingsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9TZXR0aW5nc01vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUVvRDtBQUNJO0FBQ1A7QUFDWTtBQUN1RDtBQUN6QjtBQUNLO0FBQ25EO0FBdUQ5QixTQUFTMEIsY0FBYyxLQUtqQjtRQUxpQixFQUNwQ0MsTUFBTSxFQUNOQyxPQUFPLEVBQ1BDLFFBQVEsRUFDUkMsZ0JBQWdCLEVBQ0csR0FMaUI7O0lBTXBDLE1BQU0sRUFBRUMsTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBRzNCLDhEQUFPQTtJQUNoQyxNQUFNLEVBQUU0QixVQUFVQyxlQUFlLEVBQUVDLGVBQWUsRUFBRSxHQUFHN0IsMEVBQWFBO0lBQ3BFLE1BQU0sQ0FBQzhCLFdBQVdDLGFBQWEsR0FBR3JDLCtDQUFRQSxDQUFVO0lBQ3BELE1BQU0sQ0FBQ3NDLFNBQVNDLFdBQVcsR0FBR3ZDLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU13QyxlQUFldkMsNkNBQU1BLENBQW1CO0lBRTlDLHdCQUF3QjtJQUN4QixNQUFNLENBQUN3QyxhQUFhQyxlQUFlLEdBQUcxQywrQ0FBUUEsQ0FBQztRQUM3QzJDLFVBQVVkLFNBQVNjLFFBQVE7UUFDM0JDLGNBQWNmLFNBQVNlLFlBQVksSUFBSTtRQUN2Q0MsaUJBQWlCO1FBQ2pCQyxhQUFhO1FBQ2JDLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU0sQ0FBQ0Msb0JBQW9CQyxzQkFBc0IsR0FBR2pELCtDQUFRQSxDQUFxQjtRQUMvRWtELE9BQU9oQixnQkFBZ0JnQixLQUFLO1FBQzVCQyxjQUFjakIsZ0JBQWdCaUIsWUFBWTtRQUMxQ0MsbUJBQW1CbEIsZ0JBQWdCa0IsaUJBQWlCO0lBQ3REO0lBRUEsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUd0RCwrQ0FBUUEsQ0FBZTtRQUMzRDtZQUNFdUQsTUFBTTtZQUNOQyxLQUFLO1lBQ0xDLFFBQVE7WUFDUkMsY0FBYztZQUNkQyxPQUFPO1FBQ1Q7UUFDQTtZQUNFSixNQUFNO1lBQ05DLEtBQUs7WUFDTEMsUUFBUTtZQUNSQyxjQUFjO1lBQ2RDLE9BQU87UUFDVDtLQUNEO0lBQ0QsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUc3RCwrQ0FBUUEsQ0FBVyxFQUFFO0lBQ3JELE1BQU0sQ0FBQzhELGtCQUFrQkMsb0JBQW9CLEdBQUcvRCwrQ0FBUUEsQ0FBbUIsRUFBRTtJQUM3RSxNQUFNLENBQUNnRSxPQUFPQyxTQUFTLEdBQUdqRSwrQ0FBUUEsQ0FBUyxFQUFFO0lBQzdDLE1BQU0sQ0FBQ2tFLGVBQWVDLGlCQUFpQixHQUFHbkUsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDb0UsaUJBQWlCQyxtQkFBbUIsR0FBR3JFLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ3NFLFdBQVdDLGFBQWEsR0FBR3ZFLCtDQUFRQSxDQUFTO1FBQ2pEd0UsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZDLEtBQUs7UUFDTEMsV0FBVztRQUNYQyxRQUFRO1FBQ1JDLFFBQVE7SUFDVjtJQUNBLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHL0UsK0NBQVFBLENBQWlCO1FBQzdEdUQsTUFBTTtRQUNOeUIsV0FBVztRQUNYTixLQUFLO0lBQ1A7SUFDQSxNQUFNLENBQUNPLGlCQUFpQkMsbUJBQW1CLEdBQUdsRiwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNtRixhQUFhQyxlQUFlLEdBQUdwRiwrQ0FBUUEsQ0FBYTtRQUN6RHVELE1BQU07UUFDTkMsS0FBSztRQUNMQyxRQUFRO1FBQ1JDLGNBQWM7UUFDZEMsT0FBTztJQUNUO0lBQ0EsTUFBTSxDQUFDMEIsaUJBQWlCQyxtQkFBbUIsR0FBR3RGLCtDQUFRQSxDQUFnQjtJQUN0RSxNQUFNLENBQUN1RixrQkFBa0JDLG9CQUFvQixHQUFHeEYsK0NBQVFBLENBQWE7UUFDbkV1RCxNQUFNO1FBQ05DLEtBQUs7UUFDTEMsUUFBUTtRQUNSQyxjQUFjO1FBQ2RDLE9BQU87SUFDVDtJQUVBLHNDQUFzQztJQUN0Q3pELGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXVGLHFCQUFxQjtZQUN6QixJQUFJLENBQUM1RCxTQUFTYyxRQUFRLEVBQUU7WUFFeEIsSUFBSTtnQkFDRitDLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBa0M5RCxTQUFTYyxRQUFRO2dCQUMvRCxNQUFNaUQsWUFBWSxNQUFNbEYsMERBQU1BLENBQUNILHVEQUFHQSxDQUFDaUIsNkNBQUVBLEVBQUUsWUFBWUssU0FBU2MsUUFBUSxFQUFFLGlCQUFpQjtnQkFFdkYsSUFBSWlELFVBQVVDLE1BQU0sSUFBSTtvQkFDdEIsTUFBTUMsU0FBU0YsVUFBVUcsSUFBSTtvQkFDN0JMLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBNkJHO29CQUV6QyxJQUFJQSxPQUFPRSxTQUFTLEVBQUU7d0JBQ3BCL0Msc0JBQXNCNkMsT0FBT0UsU0FBUztvQkFDeEM7b0JBRUEsSUFBSUYsT0FBT0csU0FBUyxFQUFFO3dCQUNwQixNQUFNQyxpQkFBaUJDLE9BQU9DLE1BQU0sQ0FBQ04sT0FBT0csU0FBUzt3QkFDckQzQyxlQUFlNEM7b0JBQ2pCLE9BQU87d0JBQ0wsMkRBQTJEO3dCQUMzRDVDLGVBQWU7NEJBQ2I7Z0NBQ0VDLE1BQU07Z0NBQ05DLEtBQUs7Z0NBQ0xDLFFBQVE7Z0NBQ1JDLGNBQWM7Z0NBQ2RDLE9BQU87NEJBQ1Q7NEJBQ0E7Z0NBQ0VKLE1BQU07Z0NBQ05DLEtBQUs7Z0NBQ0xDLFFBQVE7Z0NBQ1JDLGNBQWM7Z0NBQ2RDLE9BQU87NEJBQ1Q7eUJBQ0Q7b0JBQ0g7b0JBRUEsSUFBSW1DLE9BQU9PLFFBQVEsRUFBRTt3QkFDbkIsTUFBTUMsZ0JBQWdCSCxPQUFPQyxNQUFNLENBQUNOLE9BQU9PLFFBQVE7d0JBQ25EeEMsWUFBWXlDO29CQUNkO29CQUVBLElBQUlSLE9BQU9TLFVBQVUsRUFBRTt3QkFDckIsTUFBTUMsa0JBQWtCTCxPQUFPQyxNQUFNLENBQUNOLE9BQU9TLFVBQVU7d0JBQ3ZEeEMsb0JBQW9CeUM7b0JBQ3RCO2dCQUNGLE9BQU87b0JBQ0xkLFFBQVFDLEdBQUcsQ0FBQztvQkFDWixnREFBZ0Q7b0JBQ2hEMUMsc0JBQXNCO3dCQUNwQkMsT0FBTzt3QkFDUEMsY0FBYzt3QkFDZEMsbUJBQW1CO29CQUNyQjtnQkFDRjtZQUNGLEVBQUUsT0FBT3FELE9BQU87Z0JBQ2RmLFFBQVFlLEtBQUssQ0FBQyx5Q0FBbUNBO1lBQ25EO1FBQ0Y7UUFFQSxJQUFJOUUsVUFBVUUsU0FBU2MsUUFBUSxFQUFFO1lBQy9CLDJEQUEyRDtZQUMzREQsZUFBZTtnQkFDYkMsVUFBVWQsU0FBU2MsUUFBUTtnQkFDM0JDLGNBQWNmLFNBQVNlLFlBQVksSUFBSTtnQkFDdkNDLGlCQUFpQjtnQkFDakJDLGFBQWE7Z0JBQ2JDLGlCQUFpQjtZQUNuQjtZQUVBMEM7WUFDQWlCO1FBQ0Y7SUFDRixHQUFHO1FBQUMvRTtRQUFRRSxTQUFTYyxRQUFRO1FBQUVkLFNBQVNlLFlBQVk7S0FBQztJQUVyRCx3Q0FBd0M7SUFDeEMsTUFBTThELFlBQVk7UUFDaEIsSUFBSSxFQUFDN0UscUJBQUFBLCtCQUFBQSxTQUFVYyxRQUFRLEdBQUU7UUFFekIsSUFBSTtZQUNGLE1BQU1nRSxXQUFXL0YsOERBQVVBLENBQUNZLDZDQUFFQSxFQUFFLFlBQVlLLFNBQVNjLFFBQVEsRUFBRTtZQUMvRCxNQUFNaUUsYUFBYTlGLHlEQUFLQSxDQUFDNkYsVUFBVTVGLDJEQUFPQSxDQUFDLGlCQUFpQjtZQUM1RCxNQUFNOEYsZ0JBQWdCLE1BQU1oRywyREFBT0EsQ0FBQytGO1lBRXBDLE1BQU1FLGNBQXNCLEVBQUU7WUFDOUJELGNBQWNFLE9BQU8sQ0FBQyxDQUFDeEc7Z0JBQ3JCLE1BQU13RixPQUFPeEYsSUFBSXdGLElBQUk7Z0JBQ3JCZSxZQUFZRSxJQUFJLENBQUM7b0JBQ2ZDLElBQUkxRyxJQUFJMEcsRUFBRTtvQkFDVkMsTUFBTW5CLEtBQUttQixJQUFJLElBQUk7b0JBQ25CQyxhQUFhcEIsS0FBS3FCLGNBQWMsSUFBSTtvQkFDcENDLGlCQUFpQnRCLEtBQUt1QixnQkFBZ0IsSUFBSXZCLEtBQUt3QixTQUFTO2dCQUMxRDtZQUNGO1lBRUF0RCxTQUFTNkM7UUFDWCxFQUFFLE9BQU9MLE9BQU87WUFDZGYsUUFBUWUsS0FBSyxDQUFDLDJCQUEyQkE7UUFDM0M7SUFDRjtJQUVBLHVFQUF1RTtJQUN2RSxNQUFNZSx3QkFBd0IsT0FBTzdFO1FBQ25DLElBQUk7WUFDRitDLFFBQVFDLEdBQUcsQ0FBQyxtREFBZ0RoRDtZQUU1RCw2Q0FBNkM7WUFDN0MsTUFBTThFLGlCQUFpQnpHLHFEQUFHQSxDQUFDUyxrREFBT0EsRUFBRSxZQUFxQixPQUFUa0I7WUFDaEQsTUFBTStFLGtCQUFrQixNQUFNdkcseURBQU9BLENBQUNzRztZQUV0QyxrREFBa0Q7WUFDbEQsTUFBTUUsb0JBQW9CLE9BQU9DO2dCQUMvQixNQUFNQyxhQUFhLE1BQU0xRyx5REFBT0EsQ0FBQ3lHO2dCQUVqQywyQ0FBMkM7Z0JBQzNDLE1BQU1FLHFCQUFxQkQsV0FBV0UsS0FBSyxDQUFDQyxHQUFHLENBQUNDLENBQUFBLE9BQVE3Ryw4REFBWUEsQ0FBQzZHO2dCQUNyRSxNQUFNQyxRQUFRQyxHQUFHLENBQUNMO2dCQUVsQiw0Q0FBNEM7Z0JBQzVDLE1BQU1NLHVCQUF1QlAsV0FBV1EsUUFBUSxDQUFDTCxHQUFHLENBQUNNLENBQUFBLFNBQVVYLGtCQUFrQlc7Z0JBQ2pGLE1BQU1KLFFBQVFDLEdBQUcsQ0FBQ0M7WUFDcEI7WUFFQSxNQUFNVCxrQkFBa0JGO1lBQ3hCL0IsUUFBUUMsR0FBRyxDQUFDLDZDQUE2Q2hEO1FBRTNELEVBQUUsT0FBTzhELE9BQU87WUFDZGYsUUFBUUMsR0FBRyxDQUFDLGdFQUE2RGM7UUFDM0U7SUFDRjtJQUVBLGdGQUFnRjtJQUNoRixNQUFNOEIsc0JBQXNCLE9BQU81RjtRQUNqQyxJQUFJO1lBQ0YrQyxRQUFRQyxHQUFHLENBQUMsNkNBQTBDaEQ7WUFFdEQsc0NBQXNDO1lBQ3RDLElBQUk7Z0JBQ0YsTUFBTWlELFlBQVlyRix1REFBR0EsQ0FBQ2lCLDZDQUFFQSxFQUFFLFlBQVltQixVQUFVLGlCQUFpQjtnQkFDakUsTUFBTTZGLGlCQUFpQixNQUFNOUgsMERBQU1BLENBQUNrRjtnQkFDcEMsSUFBSTRDLGVBQWUzQyxNQUFNLElBQUk7b0JBQzNCLE1BQU1sRiw2REFBU0EsQ0FBQ2lGO29CQUNoQkYsUUFBUUMsR0FBRyxDQUFDO2dCQUNkO1lBQ0YsRUFBRSxPQUFPYyxPQUFPO2dCQUNkZixRQUFRQyxHQUFHLENBQUMsd0NBQWtDYztZQUNoRDtZQUVBLG1FQUFtRTtZQUNuRSxJQUFJO2dCQUNGLE1BQU1nQyxrQkFBa0I3SCw4REFBVUEsQ0FBQ1ksNkNBQUVBLEVBQUUsWUFBWW1CLFVBQVU7Z0JBQzdELE1BQU1rRSxnQkFBZ0IsTUFBTWhHLDJEQUFPQSxDQUFDNEg7Z0JBQ3BDLE1BQU1DLGlCQUFpQjdCLGNBQWM4QixJQUFJLENBQUNYLEdBQUcsQ0FBQ3pILENBQUFBLE1BQU9JLDZEQUFTQSxDQUFDSixJQUFJUyxHQUFHO2dCQUN0RSxNQUFNa0gsUUFBUUMsR0FBRyxDQUFDTztnQkFDbEJoRCxRQUFRQyxHQUFHLENBQUM7WUFDZCxFQUFFLE9BQU9jLE9BQU87Z0JBQ2RmLFFBQVFDLEdBQUcsQ0FBQywwQkFBMEJjO1lBQ3hDO1lBRUEseUNBQXlDO1lBQ3pDLE1BQU1tQyxhQUFhckksdURBQUdBLENBQUNpQiw2Q0FBRUEsRUFBRSxZQUFZbUI7WUFDdkMsTUFBTWhDLDZEQUFTQSxDQUFDaUk7WUFDaEJsRCxRQUFRQyxHQUFHLENBQUM7UUFFZCxFQUFFLE9BQU9jLE9BQU87WUFDZGYsUUFBUWUsS0FBSyxDQUFDLDZDQUEwQ0E7WUFDeEQsTUFBTUE7UUFDUjtJQUNGO0lBRUEsNENBQTRDO0lBQzVDLE1BQU1vQyxpQkFBaUIsZUFBT0M7WUFBcUJDLG9GQUE0QjtRQUM3RSxJQUFJLENBQUNsSCxTQUFTYyxRQUFRLElBQUksQ0FBQ21HLGVBQWVBLGdCQUFnQmpILFNBQVNjLFFBQVEsRUFBRTtZQUMzRSxJQUFJb0csa0JBQWtCQyxNQUFNO1lBQzVCLE9BQU87UUFDVDtRQUVBLElBQUlGLFlBQVlHLE1BQU0sR0FBRyxHQUFHO1lBQzFCLElBQUlGLGtCQUFrQkMsTUFBTTtZQUM1QixPQUFPO1FBQ1Q7UUFFQSxJQUFJRSxpQkFBaUI7UUFFckIsSUFBSTtZQUNGeEQsUUFBUUMsR0FBRyxDQUFDLDJCQUEyQjlELFNBQVNjLFFBQVEsRUFBRSxRQUFRbUc7WUFFbEUseUNBQXlDO1lBQ3pDLE1BQU1LLGFBQWEsTUFBTXpJLDBEQUFNQSxDQUFDSCx1REFBR0EsQ0FBQ2lCLDZDQUFFQSxFQUFFLFlBQVlzSDtZQUNwRCxJQUFJSyxXQUFXdEQsTUFBTSxJQUFJO2dCQUN2QixJQUFJa0Qsa0JBQWtCQyxNQUFNO2dCQUM1QixPQUFPO1lBQ1Q7WUFFQSxnREFBZ0Q7WUFDaEQsTUFBTUksZ0JBQWdCN0ksdURBQUdBLENBQUNpQiw2Q0FBRUEsRUFBRSxZQUFZSyxTQUFTYyxRQUFRO1lBQzNELE1BQU0wRyxhQUFhLE1BQU0zSSwwREFBTUEsQ0FBQzBJO1lBRWhDLElBQUksQ0FBQ0MsV0FBV3hELE1BQU0sSUFBSTtnQkFDeEIsSUFBSWtELGtCQUFrQkMsTUFBTTtnQkFDNUIsT0FBTztZQUNUO1lBRUEsTUFBTU0sY0FBY0QsV0FBV3RELElBQUk7WUFFbkMsMkNBQTJDO1lBQzNDLE1BQU10RiwwREFBTUEsQ0FBQ0YsdURBQUdBLENBQUNpQiw2Q0FBRUEsRUFBRSxZQUFZc0gsY0FBYztnQkFDN0MsR0FBR1EsV0FBVztnQkFDZDNHLFVBQVVtRztnQkFDVlMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ25DO1lBRUFQLGlCQUFpQjtZQUNqQnhELFFBQVFDLEdBQUcsQ0FBQywrQkFBK0JtRDtZQUV6Qyw4Q0FBOEM7WUFDOUMsSUFBSTtnQkFDRixrQ0FBa0M7Z0JBQ2xDLE1BQU1sRCxZQUFZLE1BQU1sRiwwREFBTUEsQ0FBQ0gsdURBQUdBLENBQUNpQiw2Q0FBRUEsRUFBRSxZQUFZSyxTQUFTYyxRQUFRLEVBQUUsaUJBQWlCO2dCQUN2RixJQUFJaUQsVUFBVUMsTUFBTSxJQUFJO29CQUN0QixNQUFNcEYsMERBQU1BLENBQUNGLHVEQUFHQSxDQUFDaUIsNkNBQUVBLEVBQUUsWUFBWXNILGFBQWEsaUJBQWlCLGFBQWFsRCxVQUFVRyxJQUFJO29CQUMxRkwsUUFBUUMsR0FBRyxDQUFDO2dCQUNkO2dCQUVBLDRCQUE0QjtnQkFDNUIsSUFBSTtvQkFDRixNQUFNOEMsa0JBQWtCN0gsOERBQVVBLENBQUNZLDZDQUFFQSxFQUFFLFlBQVlLLFNBQVNjLFFBQVEsRUFBRTtvQkFDdEUsTUFBTWtFLGdCQUFnQixNQUFNaEcsMkRBQU9BLENBQUM0SDtvQkFFcEMsS0FBSyxNQUFNaUIsV0FBVzdDLGNBQWM4QixJQUFJLENBQUU7d0JBQ3hDLE1BQU1nQixXQUFXRCxRQUFRM0QsSUFBSTt3QkFDN0IsTUFBTXRGLDBEQUFNQSxDQUFDRix1REFBR0EsQ0FBQ2lCLDZDQUFFQSxFQUFFLFlBQVlzSCxhQUFhLFNBQVNZLFFBQVF6QyxFQUFFLEdBQUcwQztvQkFDdEU7b0JBRUEsSUFBSTlDLGNBQWM4QixJQUFJLENBQUNNLE1BQU0sR0FBRyxHQUFHO3dCQUNqQ3ZELFFBQVFDLEdBQUcsQ0FBQyxHQUE2QixPQUExQmtCLGNBQWM4QixJQUFJLENBQUNNLE1BQU0sRUFBQztvQkFDM0M7Z0JBQ0YsRUFBRSxPQUFPVyxZQUFZO29CQUNuQmxFLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJpRTtnQkFDdkM7WUFFRixFQUFFLE9BQU9DLGFBQWE7Z0JBQ3BCbkUsUUFBUUMsR0FBRyxDQUFDLHlCQUF5QmtFO1lBQ3ZDO1lBRUEsZ0RBQWdEO1lBQ2hELE1BQU10QixvQkFBb0IxRyxTQUFTYyxRQUFRO1lBQzNDK0MsUUFBUUMsR0FBRyxDQUFDO1lBRVoseUJBQXlCO1lBQ3pCN0QsaUJBQWlCO2dCQUNmLEdBQUdELFFBQVE7Z0JBQ1hjLFVBQVVtRztZQUNaO1lBRUEsSUFBSUMsa0JBQWtCQyxNQUFNO1lBQzVCLE9BQU87UUFFWCxFQUFFLE9BQU92QyxPQUFPO1lBQ2RmLFFBQVFlLEtBQUssQ0FBQywrQkFBK0JBO1lBRTdDLG1FQUFtRTtZQUNuRSxJQUFJeUMsZ0JBQWdCO2dCQUNsQixJQUFJO29CQUNGLE1BQU12SSw2REFBU0EsQ0FBQ0osdURBQUdBLENBQUNpQiw2Q0FBRUEsRUFBRSxZQUFZc0g7b0JBQ3BDcEQsUUFBUUMsR0FBRyxDQUFDO2dCQUNkLEVBQUUsT0FBT21FLGVBQWU7b0JBQ3RCcEUsUUFBUWUsS0FBSyxDQUFDLHFCQUFxQnFEO2dCQUNyQztZQUNGO1lBRUEsSUFBSWYsa0JBQWtCQyxNQUFNLHlDQUFtRyxPQUE3RHZDLGlCQUFpQnNELFFBQVF0RCxNQUFNdUQsT0FBTyxHQUFHO1lBQzNHLE9BQU87UUFDVDtJQUNGO0lBRUEsb0NBQW9DO0lBQ3BDLE1BQU1DLHFCQUFxQjtRQUN6QixJQUFJLENBQUNwSSxTQUFTYyxRQUFRLEVBQUU7WUFDdEJxRyxNQUFNO1lBQ047UUFDRjtRQUVBLElBQUk7WUFDRnpHLFdBQVc7WUFFWCw4REFBOEQ7WUFDOUQsSUFBSUUsWUFBWUUsUUFBUSxLQUFLZCxTQUFTYyxRQUFRLEVBQUU7Z0JBQzlDLE1BQU11SCxrQkFBa0IsTUFBTXJCLGVBQWVwRyxZQUFZRSxRQUFRLEVBQUU7Z0JBQ25FLElBQUksQ0FBQ3VILGlCQUFpQjtvQkFDcEIsNERBQTREO29CQUM1RDtnQkFDRjtZQUNGO1lBRUEseURBQXlEO1lBQ3pELE1BQU1DLGtCQUFrQjFILFlBQVlFLFFBQVEsS0FBS2QsU0FBU2MsUUFBUSxHQUFHRixZQUFZRSxRQUFRLEdBQUdkLFNBQVNjLFFBQVE7WUFFN0csTUFBTXlILGFBQWE7Z0JBQ2pCcEUsV0FBVztvQkFDVDlDLE9BQU9GLG1CQUFtQkUsS0FBSztvQkFDL0JDLGNBQWNILG1CQUFtQkcsWUFBWTtvQkFDN0NDLG1CQUFtQkosbUJBQW1CSSxpQkFBaUI7Z0JBQ3pEO2dCQUNBNkMsV0FBVyxDQUFDO2dCQUNaSSxVQUFVLENBQUM7Z0JBQ1hFLFlBQVksQ0FBQztnQkFDYmdELFdBQVcsSUFBSUMsT0FBT0MsV0FBVztZQUNuQztZQUVBLGdDQUFnQztZQUNoQ3BHLFlBQVkwRCxPQUFPLENBQUMsQ0FBQ3NELFVBQVVDO2dCQUM3QkYsV0FBV25FLFNBQVMsQ0FBQ29FLFNBQVM5RyxJQUFJLElBQUksWUFBa0IsT0FBTitHLE9BQVEsR0FBR0Q7WUFDL0Q7WUFFQXpHLFNBQVNtRCxPQUFPLENBQUMsQ0FBQ3dELFFBQVFEO2dCQUN4QkYsV0FBVy9ELFFBQVEsQ0FBQyxXQUFpQixPQUFOaUUsT0FBUSxHQUFHQztZQUM1QztZQUVBekcsaUJBQWlCaUQsT0FBTyxDQUFDLENBQUN5RCxVQUFVRjtnQkFDbENGLFdBQVc3RCxVQUFVLENBQUNpRSxTQUFTakgsSUFBSSxJQUFJLGFBQW1CLE9BQU4rRyxPQUFRLEdBQUdFO1lBQ2pFO1lBRUE5RSxRQUFRQyxHQUFHLENBQUMsc0NBQWdDd0U7WUFDNUN6RSxRQUFRQyxHQUFHLENBQUMseUJBQXlCeUU7WUFFckMsMkRBQTJEO1lBQzNELE1BQU1LLFNBQVNsSyx1REFBR0EsQ0FBQ2lCLDZDQUFFQSxFQUFFLFlBQVkySSxpQkFBaUIsaUJBQWlCO1lBQ3JFLE1BQU0xSiwwREFBTUEsQ0FBQ2dLLFFBQVFMO1lBRXJCMUUsUUFBUUMsR0FBRyxDQUFDO1lBRVosc0RBQXNEO1lBQ3RELE1BQU14RDtZQUVONkcsTUFBTTtRQUVSLEVBQUUsT0FBT3ZDLE9BQU87WUFDZGYsUUFBUWUsS0FBSyxDQUFDLHVDQUFpQ0E7WUFDL0N1QyxNQUFNLHVDQUE4RixPQUE3RHZDLGlCQUFpQnNELFFBQVF0RCxNQUFNdUQsT0FBTyxHQUFHO1FBQ2xGLFNBQVU7WUFDUnpILFdBQVc7UUFDYjtJQUNGO0lBRUEsSUFBSSxDQUFDWixRQUFRLE9BQU87SUFFcEIsc0JBQXNCO0lBQ3RCLE1BQU0rSSwyQkFBMkIsT0FBT0M7UUFDdEMsSUFBSSxDQUFDM0ksTUFBTTtRQUVYLElBQUk7WUFDRk8sV0FBVztZQUNYLE1BQU1xSSxXQUFXNUoscURBQUdBLENBQUNTLGtEQUFPQSxFQUFFLFlBQThCLE9BQWxCSSxTQUFTYyxRQUFRLEVBQUM7WUFDNUQsTUFBTTFCLDZEQUFXQSxDQUFDMkosVUFBVUQ7WUFDNUIsTUFBTUUsY0FBYyxNQUFNM0osZ0VBQWNBLENBQUMwSjtZQUV6Q2xJLGVBQWVvSSxDQUFBQSxPQUFTO29CQUFFLEdBQUdBLElBQUk7b0JBQUVsSSxjQUFjaUk7Z0JBQVk7WUFFN0QseUJBQXlCO1lBQ3pCLE1BQU1qQyxhQUFhckksdURBQUdBLENBQUNpQiw2Q0FBRUEsRUFBRSxZQUFZSyxTQUFTYyxRQUFRO1lBQ3hELE1BQU1uQyw2REFBU0EsQ0FBQ29JLFlBQVk7Z0JBQUVoRyxjQUFjaUk7WUFBWTtZQUV4RC9JLGlCQUFpQjtnQkFBRSxHQUFHRCxRQUFRO2dCQUFFZSxjQUFjaUk7WUFBWTtZQUMxRDdCLE1BQU07UUFDUixFQUFFLE9BQU92QyxPQUFPO1lBQ2RmLFFBQVFlLEtBQUssQ0FBQyxtQ0FBbUNBO1lBQ2pEdUMsTUFBTTtRQUNSLFNBQVU7WUFDUnpHLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTXdJLHVCQUF1QjtRQUMzQixJQUFJLENBQUMvSSxRQUFRLENBQUNTLFlBQVlJLGVBQWUsSUFBSSxDQUFDSixZQUFZSyxXQUFXLEVBQUU7WUFDckVrRyxNQUFNO1lBQ047UUFDRjtRQUVBLElBQUl2RyxZQUFZSyxXQUFXLEtBQUtMLFlBQVlNLGVBQWUsRUFBRTtZQUMzRGlHLE1BQU07WUFDTjtRQUNGO1FBRUEsSUFBSTtZQUNGekcsV0FBVztZQUNYLE1BQU15SSxhQUFhekosNERBQWlCQSxDQUFDeUosVUFBVSxDQUFDaEosS0FBS2lKLEtBQUssRUFBR3hJLFlBQVlJLGVBQWU7WUFDeEYsTUFBTXZCLDJFQUE0QkEsQ0FBQ1UsTUFBTWdKO1lBQ3pDLE1BQU0zSiw2REFBY0EsQ0FBQ1csTUFBTVMsWUFBWUssV0FBVztZQUVsREosZUFBZW9JLENBQUFBLE9BQVM7b0JBQ3RCLEdBQUdBLElBQUk7b0JBQ1BqSSxpQkFBaUI7b0JBQ2pCQyxhQUFhO29CQUNiQyxpQkFBaUI7Z0JBQ25CO1lBRUFpRyxNQUFNO1FBQ1IsRUFBRSxPQUFPdkMsT0FBTztZQUNkZixRQUFRZSxLQUFLLENBQUMsMEJBQTBCQTtZQUN4Q3VDLE1BQU07UUFDUixTQUFVO1lBQ1J6RyxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU0ySSxlQUFlO1FBQ25CLElBQUlDLFFBQVEsaUNBQWlDO1lBQzNDLE1BQU1wSjtZQUNOSDtRQUNGO0lBQ0Y7SUFFQSx5Q0FBeUM7SUFDekMsTUFBTXdKLG9CQUFvQjtRQUN4QixJQUFJLENBQUNqRyxZQUFZNUIsSUFBSSxJQUFJLENBQUM0QixZQUFZM0IsR0FBRyxJQUFJLENBQUMyQixZQUFZMUIsTUFBTSxFQUFFO1lBQ2hFdUYsTUFBTTtZQUNOO1FBQ0Y7UUFFQTFGLGVBQWV3SCxDQUFBQSxPQUFRO21CQUFJQTtnQkFBTTtvQkFBRSxHQUFHM0YsV0FBVztnQkFBQzthQUFFO1FBQ3BEQyxlQUFlO1lBQ2I3QixNQUFNO1lBQ05DLEtBQUs7WUFDTEMsUUFBUTtZQUNSQyxjQUFjO1lBQ2RDLE9BQU87UUFDVDtRQUNBdUIsbUJBQW1CO1FBQ25COEQsTUFBTTtJQUNSO0lBRUEsTUFBTXFDLHVCQUF1QixDQUFDZjtRQUM1QmhILGVBQWV3SCxDQUFBQSxPQUFRQSxLQUFLOUMsR0FBRyxDQUFDLENBQUNxQyxVQUFVaUIsSUFDekNBLE1BQU1oQixRQUFRO29CQUFFLEdBQUdELFFBQVE7b0JBQUUxRyxPQUFPLENBQUMwRyxTQUFTMUcsS0FBSztnQkFBQyxJQUFJMEc7SUFFNUQ7SUFFQSxNQUFNa0IsdUJBQXVCLENBQUNqQjtRQUM1QixJQUFJYSxRQUFRLGtEQUFrRDtZQUM1RDdILGVBQWV3SCxDQUFBQSxPQUFRQSxLQUFLVSxNQUFNLENBQUMsQ0FBQ0MsR0FBR0gsSUFBTUEsTUFBTWhCO1FBQ3JEO0lBQ0Y7SUFFQSxNQUFNb0IscUJBQXFCLENBQUNwQjtRQUMxQixNQUFNRCxXQUFXaEgsV0FBVyxDQUFDaUgsTUFBTTtRQUNuQzlFLG9CQUFvQjtZQUFFLEdBQUc2RSxRQUFRO1FBQUM7UUFDbEMvRSxtQkFBbUJnRjtJQUNyQjtJQUVBLE1BQU1xQix5QkFBeUI7UUFDN0IsSUFBSXRHLG9CQUFvQixNQUFNO1FBRTlCLElBQUksQ0FBQ0UsaUJBQWlCOUIsTUFBTSxJQUFJLENBQUM4QixpQkFBaUI3QixZQUFZLEVBQUU7WUFDOURzRixNQUFNO1lBQ047UUFDRjtRQUVBMUYsZUFBZXdILENBQUFBLE9BQVFBLEtBQUs5QyxHQUFHLENBQUMsQ0FBQ3FDLFVBQVVpQixJQUN6Q0EsTUFBTWpHLGtCQUFrQjtvQkFBRSxHQUFHRSxnQkFBZ0I7Z0JBQUMsSUFBSThFO1FBR3BEL0UsbUJBQW1CO1FBQ25CRSxvQkFBb0I7WUFDbEJqQyxNQUFNO1lBQ05DLEtBQUs7WUFDTEMsUUFBUTtZQUNSQyxjQUFjO1lBQ2RDLE9BQU87UUFDVDtRQUVBcUYsTUFBTTtJQUNSO0lBRUEsTUFBTTRDLDJCQUEyQjtRQUMvQnRHLG1CQUFtQjtRQUNuQkUsb0JBQW9CO1lBQ2xCakMsTUFBTTtZQUNOQyxLQUFLO1lBQ0xDLFFBQVE7WUFDUkMsY0FBYztZQUNkQyxPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU1rSSxxQkFBcUIsT0FBT3hCO1FBQ2hDLElBQUksQ0FBQ0EsU0FBUzVHLE1BQU0sRUFBRTtZQUNwQnVGLE1BQU07WUFDTjtRQUNGO1FBRUEsSUFBSTtZQUNGekcsV0FBVztZQUNYLE1BQU11SixXQUFXLE1BQU1DLE1BQU0xQixTQUFTN0csR0FBRyxFQUFFO2dCQUN6Q3dJLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixpQkFBaUIsVUFBMEIsT0FBaEI1QixTQUFTNUcsTUFBTTtnQkFDNUM7Z0JBQ0F5SSxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CQyxPQUFPaEMsU0FBUzNHLFlBQVksSUFBSTtvQkFDaEM0SSxVQUFVO3dCQUFDOzRCQUFFQyxNQUFNOzRCQUFRQyxTQUFTO3dCQUFlO3FCQUFFO29CQUNyREMsWUFBWTtnQkFDZDtZQUNGO1lBRUEsSUFBSVgsU0FBU1ksRUFBRSxFQUFFO2dCQUNmMUQsTUFBTTtZQUNSLE9BQU87Z0JBQ0xBLE1BQU07WUFDUjtRQUNGLEVBQUUsT0FBT3ZDLE9BQU87WUFDZGYsUUFBUWUsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUN1QyxNQUFNO1FBQ1IsU0FBVTtZQUNSekcsV0FBVztRQUNiO0lBQ0Y7SUFFQSxrQ0FBa0M7SUFDbEMsTUFBTW9LLG9CQUFvQjtRQUN4QixJQUFJLENBQUM3SCxZQUFZdkIsSUFBSSxFQUFFO1lBQ3JCeUYsTUFBTTtZQUNOO1FBQ0Y7UUFFQWpGLG9CQUFvQitHLENBQUFBLE9BQVE7bUJBQUlBO2dCQUFNO29CQUFFLEdBQUdoRyxXQUFXO2dCQUFDO2FBQUU7UUFDekRDLGVBQWU7WUFDYnhCLE1BQU07WUFDTnlCLFdBQVc7WUFDWE4sS0FBSztRQUNQO1FBQ0FMLG1CQUFtQjtRQUNuQjJFLE1BQU07SUFDUjtJQUVBLE1BQU00RCxrQkFBa0I7UUFDdEIsSUFBSSxDQUFDdEksVUFBVUUsTUFBTSxJQUFJLENBQUNGLFVBQVVHLFFBQVEsRUFBRTtZQUM1Q3VFLE1BQU07WUFDTjtRQUNGO1FBRUFuRixZQUFZaUgsQ0FBQUEsT0FBUTttQkFBSUE7Z0JBQU07b0JBQUUsR0FBR3hHLFNBQVM7Z0JBQUM7YUFBRTtRQUMvQ0MsYUFBYTtZQUNYQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsS0FBSztZQUNMQyxXQUFXO1lBQ1hDLFFBQVE7WUFDUkMsUUFBUTtRQUNWO1FBQ0FWLGlCQUFpQjtRQUNqQjZFLE1BQU07SUFDUjtJQUVBLE1BQU02RCxxQkFBcUIsQ0FBQ3ZDO1FBQzFCLElBQUlhLFFBQVEsb0RBQWlEO1lBQzNEdEgsWUFBWWlILENBQUFBLE9BQVFBLEtBQUtVLE1BQU0sQ0FBQyxDQUFDQyxHQUFHSCxJQUFNQSxNQUFNaEI7UUFDbEQ7SUFDRjtJQUVBLE1BQU13Qyx1QkFBdUIsQ0FBQ3hDO1FBQzVCLElBQUlhLFFBQVEsbURBQW1EO1lBQzdEcEgsb0JBQW9CK0csQ0FBQUEsT0FBUUEsS0FBS1UsTUFBTSxDQUFDLENBQUNDLEdBQUdILElBQU1BLE1BQU1oQjtRQUMxRDtJQUNGO0lBRUEsTUFBTXlDLFNBQVM7UUFDYjtRQUFXO1FBQVc7UUFBVztRQUNqQztRQUFXO1FBQVc7UUFBVztLQUNsQztJQUVELHFCQUNFLDhEQUFDM00sMERBQWVBO2tCQUNidUIsd0JBQ0MsOERBQUN4QixpREFBTUEsQ0FBQzZNLEdBQUc7WUFDVEMsU0FBUztnQkFBRUMsU0FBUztZQUFFO1lBQ3RCQyxTQUFTO2dCQUFFRCxTQUFTO1lBQUU7WUFDdEJFLE1BQU07Z0JBQUVGLFNBQVM7WUFBRTtZQUNuQkcsV0FBVTtzQkFFViw0RUFBQ2xOLGlEQUFNQSxDQUFDNk0sR0FBRztnQkFDVEMsU0FBUztvQkFBRUssT0FBTztvQkFBTUosU0FBUztnQkFBRTtnQkFDbkNDLFNBQVM7b0JBQUVHLE9BQU87b0JBQUdKLFNBQVM7Z0JBQUU7Z0JBQ2hDRSxNQUFNO29CQUFFRSxPQUFPO29CQUFNSixTQUFTO2dCQUFFO2dCQUNoQ0csV0FBVTs7a0NBTVYsOERBQUNMO3dCQUFJSyxXQUFVOzswQ0FDYiw4REFBQ0w7O2tEQUNDLDhEQUFDTzt3Q0FBR0YsV0FBVTtrREFBMkM7Ozs7OztrREFDekQsOERBQUNHO3dDQUFFSCxXQUFVOzs0Q0FDVmpMLGNBQWMsV0FBVzs0Q0FDekJBLGNBQWMsZUFBZTs0Q0FDN0JBLGNBQWMsUUFBUTs0Q0FDdEJBLGNBQWMsYUFBYTs7Ozs7Ozs7Ozs7OzswQ0FHaEMsOERBQUNxTDtnQ0FDQ0MsU0FBUzlMO2dDQUNUeUwsV0FBVTswQ0FFViw0RUFBQ007b0NBQUlOLFdBQVU7b0NBQVVPLE1BQUs7b0NBQU9DLFFBQU87b0NBQWVDLFNBQVE7OENBQ2pFLDRFQUFDQzt3Q0FBS0MsZUFBYzt3Q0FBUUMsZ0JBQWU7d0NBQVFDLGFBQWE7d0NBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSzNFLDhEQUFDbkI7d0JBQUlLLFdBQVU7OzBDQUViLDhEQUFDTDtnQ0FBSUssV0FBVTswQ0FDYiw0RUFBQ2U7b0NBQUlmLFdBQVU7OENBQ2IsNEVBQUNMO3dDQUFJSyxXQUFVOzswREFDZiw4REFBQ0k7Z0RBQ0NDLFNBQVMsSUFBTXJMLGFBQWE7Z0RBQzVCZ0wsV0FBVywwSkFJVixPQUhDakwsY0FBYyxVQUNWLHFDQUNBOztrRUFHTiw4REFBQ3VMO3dEQUFJTixXQUFVO3dEQUF3Qk8sTUFBSzt3REFBT0MsUUFBTzt3REFBZUMsU0FBUTtrRUFDL0UsNEVBQUNDOzREQUFLQyxlQUFjOzREQUFRQyxnQkFBZTs0REFBUUMsYUFBYTs0REFDOURDLEdBQUU7Ozs7Ozs7Ozs7O2tFQUVOLDhEQUFDRTt3REFBS2hCLFdBQVU7a0VBQW1DOzs7Ozs7Ozs7Ozs7MERBR3JELDhEQUFDSTtnREFDQ0MsU0FBUyxJQUFNckwsYUFBYTtnREFDNUJnTCxXQUFXLDBKQUlWLE9BSENqTCxjQUFjLGNBQ1YscUNBQ0E7O2tFQUdOLDhEQUFDdUw7d0RBQUlOLFdBQVU7d0RBQXdCTyxNQUFLO3dEQUFPQyxRQUFPO3dEQUFlQyxTQUFRO2tFQUMvRSw0RUFBQ0M7NERBQUtDLGVBQWM7NERBQVFDLGdCQUFlOzREQUFRQyxhQUFhOzREQUM5REMsR0FBRTs7Ozs7Ozs7Ozs7a0VBRU4sOERBQUNFO3dEQUFLaEIsV0FBVTtrRUFBbUM7Ozs7Ozs7Ozs7OzswREFHckQsOERBQUNJO2dEQUNDQyxTQUFTLElBQU1yTCxhQUFhO2dEQUM1QmdMLFdBQVcsMEpBSVYsT0FIQ2pMLGNBQWMsT0FDVixxQ0FDQTs7a0VBR04sOERBQUN1TDt3REFBSU4sV0FBVTt3REFBd0JPLE1BQUs7d0RBQU9DLFFBQU87d0RBQWVDLFNBQVE7a0VBQy9FLDRFQUFDQzs0REFBS0MsZUFBYzs0REFBUUMsZ0JBQWU7NERBQVFDLGFBQWE7NERBQzlEQyxHQUFFOzs7Ozs7Ozs7OztrRUFFTiw4REFBQ0U7d0RBQUtoQixXQUFVO2tFQUFtQzs7Ozs7Ozs7Ozs7OzBEQUdyRCw4REFBQ0k7Z0RBQ0NDLFNBQVMsSUFBTXJMLGFBQWE7Z0RBQzVCZ0wsV0FBVywwSkFJVixPQUhDakwsY0FBYyxZQUNWLHFDQUNBOztrRUFHTiw4REFBQ3VMO3dEQUFJTixXQUFVO3dEQUF3Qk8sTUFBSzt3REFBT0MsUUFBTzt3REFBZUMsU0FBUTtrRUFDL0UsNEVBQUNDOzREQUFLQyxlQUFjOzREQUFRQyxnQkFBZTs0REFBUUMsYUFBYTs0REFDOURDLEdBQUU7Ozs7Ozs7Ozs7O2tFQUVOLDhEQUFDRTt3REFBS2hCLFdBQVU7a0VBQW1DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU96RCw4REFBQ0w7Z0NBQUlLLFdBQVU7MENBQ2IsNEVBQUNqTiwwREFBZUE7b0NBQUNrTyxNQUFLOzt3Q0FDbkJsTSxjQUFjLHlCQUNiLDhEQUFDakMsaURBQU1BLENBQUM2TSxHQUFHOzRDQUVUQyxTQUFTO2dEQUFFQyxTQUFTO2dEQUFHcUIsR0FBRzs0Q0FBRzs0Q0FDN0JwQixTQUFTO2dEQUFFRCxTQUFTO2dEQUFHcUIsR0FBRzs0Q0FBRTs0Q0FDNUJuQixNQUFNO2dEQUFFRixTQUFTO2dEQUFHcUIsR0FBRyxDQUFDOzRDQUFHOzRDQUMzQmxCLFdBQVU7c0RBRVYsNEVBQUNMOztrRUFDQyw4REFBQ3dCO3dEQUFHbkIsV0FBVTtrRUFBcUM7Ozs7OztrRUFHbkQsOERBQUNMO3dEQUFJSyxXQUFVOzswRUFDYiw4REFBQ29CO2dFQUFHcEIsV0FBVTswRUFBd0M7Ozs7OzswRUFDdEQsOERBQUNMO2dFQUFJSyxXQUFVOztrRkFDYiw4REFBQ0w7d0VBQUlLLFdBQVU7a0ZBQ1o1SyxZQUFZRyxZQUFZLGlCQUN2Qiw4REFBQzhMOzRFQUNDQyxLQUFLbE0sWUFBWUcsWUFBWTs0RUFDN0JnTSxLQUFJOzRFQUNKdkIsV0FBVTs7Ozs7aUdBR1osOERBQUNMOzRFQUFJSyxXQUFVO3NGQUNiLDRFQUFDZ0I7Z0ZBQUtoQixXQUFVOzBGQUNieEwsU0FBU2MsUUFBUSxDQUFDa00sTUFBTSxDQUFDLEdBQUdDLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBS2hELDhEQUFDOUI7OzBGQUNDLDhEQUFDUztnRkFDQ0MsU0FBUzt3RkFBTWxMOzRGQUFBQSx3QkFBQUEsYUFBYXVNLE9BQU8sY0FBcEJ2TSw0Q0FBQUEsc0JBQXNCd00sS0FBSzs7Z0ZBQzFDM0IsV0FBVTswRkFFWDs7Ozs7OzBGQUdELDhEQUFDRztnRkFBRUgsV0FBVTswRkFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFLOUMsOERBQUM0QjtnRUFDQ2pPLEtBQUt3QjtnRUFDTDBNLE1BQUs7Z0VBQ0xDLFFBQU87Z0VBQ1BDLFVBQVUsQ0FBQ0M7d0VBQ0lBO29FQUFiLE1BQU0xRSxRQUFPMEUsa0JBQUFBLEVBQUVDLE1BQU0sQ0FBQ0MsS0FBSyxjQUFkRixzQ0FBQUEsZUFBZ0IsQ0FBQyxFQUFFO29FQUNoQyxJQUFJMUUsTUFBTUQseUJBQXlCQztnRUFDckM7Z0VBQ0EwQyxXQUFVOzs7Ozs7Ozs7Ozs7a0VBS2QsOERBQUNMO3dEQUFJSyxXQUFVOzswRUFDYiw4REFBQ29CO2dFQUFHcEIsV0FBVTswRUFBd0M7Ozs7OzswRUFDdEQsOERBQUNMO2dFQUFJSyxXQUFVOztrRkFDYiw4REFBQzRCO3dFQUNDQyxNQUFLO3dFQUNMTSxPQUFPL00sWUFBWUUsUUFBUTt3RUFDM0J5TSxVQUFVLENBQUNDLElBQU0zTSxlQUFlb0ksQ0FBQUEsT0FBUztvRkFBRSxHQUFHQSxJQUFJO29GQUFFbkksVUFBVTBNLEVBQUVDLE1BQU0sQ0FBQ0UsS0FBSztnRkFBQzt3RUFDN0VuQyxXQUFVO3dFQUdWb0MsYUFBWTs7Ozs7O29FQUViaE4sWUFBWUUsUUFBUSxLQUFLZCxTQUFTYyxRQUFRLGtCQUN6Qyw4REFBQ3FLO3dFQUFJSyxXQUFVO2tGQUNiLDRFQUFDRzs0RUFBRUgsV0FBVTtzRkFBMEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQVMvQyw4REFBQ0w7d0RBQUlLLFdBQVU7OzBFQUNiLDhEQUFDb0I7Z0VBQUdwQixXQUFVOzBFQUF3Qzs7Ozs7OzBFQUN0RCw4REFBQ0w7Z0VBQUlLLFdBQVU7O2tGQUNiLDhEQUFDTDs7MEZBQ0MsOERBQUMwQztnRkFBTXJDLFdBQVU7MEZBQStDOzs7Ozs7MEZBR2hFLDhEQUFDNEI7Z0ZBQ0NDLE1BQUs7Z0ZBQ0xNLE9BQU8vTSxZQUFZSSxlQUFlO2dGQUNsQ3VNLFVBQVUsQ0FBQ0MsSUFBTTNNLGVBQWVvSSxDQUFBQSxPQUFTOzRGQUFFLEdBQUdBLElBQUk7NEZBQUVqSSxpQkFBaUJ3TSxFQUFFQyxNQUFNLENBQUNFLEtBQUs7d0ZBQUM7Z0ZBQ3BGbkMsV0FBVTtnRkFHVm9DLGFBQVk7Ozs7Ozs7Ozs7OztrRkFHaEIsOERBQUN6Qzs7MEZBQ0MsOERBQUMwQztnRkFBTXJDLFdBQVU7MEZBQStDOzs7Ozs7MEZBR2hFLDhEQUFDNEI7Z0ZBQ0NDLE1BQUs7Z0ZBQ0xNLE9BQU8vTSxZQUFZSyxXQUFXO2dGQUM5QnNNLFVBQVUsQ0FBQ0MsSUFBTTNNLGVBQWVvSSxDQUFBQSxPQUFTOzRGQUFFLEdBQUdBLElBQUk7NEZBQUVoSSxhQUFhdU0sRUFBRUMsTUFBTSxDQUFDRSxLQUFLO3dGQUFDO2dGQUNoRm5DLFdBQVU7Z0ZBR1ZvQyxhQUFZOzs7Ozs7Ozs7Ozs7a0ZBR2hCLDhEQUFDekM7OzBGQUNDLDhEQUFDMEM7Z0ZBQU1yQyxXQUFVOzBGQUErQzs7Ozs7OzBGQUdoRSw4REFBQzRCO2dGQUNDQyxNQUFLO2dGQUNMTSxPQUFPL00sWUFBWU0sZUFBZTtnRkFDbENxTSxVQUFVLENBQUNDLElBQU0zTSxlQUFlb0ksQ0FBQUEsT0FBUzs0RkFBRSxHQUFHQSxJQUFJOzRGQUFFL0gsaUJBQWlCc00sRUFBRUMsTUFBTSxDQUFDRSxLQUFLO3dGQUFDO2dGQUNwRm5DLFdBQVU7Z0ZBR1ZvQyxhQUFZOzs7Ozs7Ozs7Ozs7a0ZBR2hCLDhEQUFDaEM7d0VBQ0NDLFNBQVMzQzt3RUFDVDRFLFVBQVVyTjt3RUFDVitLLFdBQVU7a0ZBR1QvSyxVQUFVLGlCQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJDQWhJaEM7Ozs7O3dDQXdJUEYsY0FBYyw2QkFDYiw4REFBQ2pDLGlEQUFNQSxDQUFDNk0sR0FBRzs0Q0FFVEMsU0FBUztnREFBRUMsU0FBUztnREFBR3FCLEdBQUc7NENBQUc7NENBQzdCcEIsU0FBUztnREFBRUQsU0FBUztnREFBR3FCLEdBQUc7NENBQUU7NENBQzVCbkIsTUFBTTtnREFBRUYsU0FBUztnREFBR3FCLEdBQUcsQ0FBQzs0Q0FBRzs0Q0FDM0JsQixXQUFVO3NEQUVWLDRFQUFDTDs7a0VBQ0MsOERBQUN3Qjt3REFBR25CLFdBQVU7a0VBQXFDOzs7Ozs7a0VBR25ELDhEQUFDTDt3REFBSUssV0FBVTs7MEVBQ2IsOERBQUNvQjtnRUFBR3BCLFdBQVU7MEVBQXdDOzs7Ozs7MEVBQ3RELDhEQUFDTDtnRUFBSUssV0FBVTs7a0ZBQ2IsOERBQUNMOzswRkFDQyw4REFBQzBDO2dGQUFNckMsV0FBVTswRkFBK0M7Ozs7OzswRkFHaEUsOERBQUN1QztnRkFDQ0osT0FBT3hNLG1CQUFtQkUsS0FBSztnRkFDL0JrTSxVQUFVLENBQUNDLElBQU1wTSxzQkFBc0I2SCxDQUFBQSxPQUFTOzRGQUFFLEdBQUdBLElBQUk7NEZBQUU1SCxPQUFPbU0sRUFBRUMsTUFBTSxDQUFDRSxLQUFLO3dGQUFDO2dGQUNqRm5DLFdBQVU7O2tHQUlWLDhEQUFDd0M7d0ZBQU9MLE9BQU07d0ZBQVFuQyxXQUFVO2tHQUFjOzs7Ozs7a0dBQzlDLDhEQUFDd0M7d0ZBQU9MLE9BQU07d0ZBQVNuQyxXQUFVO2tHQUFjOzs7Ozs7a0dBQy9DLDhEQUFDd0M7d0ZBQU9MLE9BQU07d0ZBQWlCbkMsV0FBVTtrR0FBYzs7Ozs7O2tHQUN2RCw4REFBQ3dDO3dGQUFPTCxPQUFNO3dGQUFPbkMsV0FBVTtrR0FBYzs7Ozs7O2tHQUM3Qyw4REFBQ3dDO3dGQUFPTCxPQUFNO3dGQUFZbkMsV0FBVTtrR0FBYzs7Ozs7O2tHQUNsRCw4REFBQ3dDO3dGQUFPTCxPQUFNO3dGQUFlbkMsV0FBVTtrR0FBYzs7Ozs7O2tHQUNyRCw4REFBQ3dDO3dGQUFPTCxPQUFNO3dGQUFZbkMsV0FBVTtrR0FBYzs7Ozs7O2tHQUNsRCw4REFBQ3dDO3dGQUFPTCxPQUFNO3dGQUFrQm5DLFdBQVU7a0dBQWM7Ozs7OztrR0FDeEQsOERBQUN3Qzt3RkFBT0wsT0FBTTt3RkFBVW5DLFdBQVU7a0dBQWM7Ozs7OztrR0FDaEQsOERBQUN3Qzt3RkFBT0wsT0FBTTt3RkFBU25DLFdBQVU7a0dBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRkFJbkQsOERBQUNMO3dFQUFJSyxXQUFVOzswRkFDYiw4REFBQ0c7Z0ZBQUVILFdBQVU7MEZBQTZCOzs7Ozs7MEZBQzFDLDhEQUFDTDtnRkFDQ0ssV0FBVTtnRkFDVnlDLE9BQU87b0ZBQUVDLFlBQVkvTSxtQkFBbUJFLEtBQUs7b0ZBQUU4TSxVQUFVLEdBQW1DLE9BQWhDaE4sbUJBQW1CRyxZQUFZLEVBQUM7Z0ZBQUk7MEZBQ2pHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBU1AsOERBQUM2Sjt3REFBSUssV0FBVTs7MEVBQ2IsOERBQUNvQjtnRUFBR3BCLFdBQVU7MEVBQXdDOzs7Ozs7MEVBQ3RELDhEQUFDTDtnRUFBSUssV0FBVTswRUFDYiw0RUFBQ0w7O3NGQUNDLDhEQUFDMEM7NEVBQU1yQyxXQUFVOztnRkFBK0M7Z0ZBQ3BEckssbUJBQW1CRyxZQUFZO2dGQUFDOzs7Ozs7O3NGQUU1Qyw4REFBQzhMOzRFQUNDQyxNQUFLOzRFQUNMZSxLQUFJOzRFQUNKQyxLQUFJOzRFQUNKVixPQUFPeE0sbUJBQW1CRyxZQUFZOzRFQUN0Q2lNLFVBQVUsQ0FBQ0MsSUFBTXBNLHNCQUFzQjZILENBQUFBLE9BQVM7d0ZBQUUsR0FBR0EsSUFBSTt3RkFBRTNILGNBQWNnTixTQUFTZCxFQUFFQyxNQUFNLENBQUNFLEtBQUs7b0ZBQUU7NEVBQ2xHbkMsV0FBVTs7Ozs7O3NGQUVaLDhEQUFDTDs0RUFBSUssV0FBVTs7OEZBQ2IsOERBQUNnQjs4RkFBSzs7Ozs7OzhGQUNOLDhEQUFDQTs4RkFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBT2QsOERBQUNyQjt3REFBSUssV0FBVTs7MEVBQ2IsOERBQUNvQjtnRUFBR3BCLFdBQVU7MEVBQXdDOzs7Ozs7MEVBQ3RELDhEQUFDTDtnRUFBSUssV0FBVTs7a0ZBQ2IsOERBQUNMO3dFQUFJSyxXQUFVOzswRkFDYiw4REFBQ0w7O2tHQUNDLDhEQUFDb0Q7d0ZBQUcvQyxXQUFVO2tHQUF5Qjs7Ozs7O2tHQUN2Qyw4REFBQ0c7d0ZBQUVILFdBQVU7a0dBQXdCOzs7Ozs7Ozs7Ozs7MEZBSXZDLDhEQUFDSTtnRkFDQ0osV0FBVTswRkFFViw0RUFBQ2dCO29GQUFLaEIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBSXBCLDhEQUFDTDs7MEZBQ0MsOERBQUMwQztnRkFBTXJDLFdBQVU7O29GQUErQztvRkFDeENySyxtQkFBbUJJLGlCQUFpQixDQUFDaU4sY0FBYzs7Ozs7OzswRkFFM0UsOERBQUNwQjtnRkFDQ0MsTUFBSztnRkFDTGUsS0FBSTtnRkFDSkMsS0FBSTtnRkFDSkksTUFBSztnRkFDTGQsT0FBT3hNLG1CQUFtQkksaUJBQWlCO2dGQUMzQ2dNLFVBQVUsQ0FBQ0MsSUFBTXBNLHNCQUFzQjZILENBQUFBLE9BQVM7NEZBQUUsR0FBR0EsSUFBSTs0RkFBRTFILG1CQUFtQitNLFNBQVNkLEVBQUVDLE1BQU0sQ0FBQ0UsS0FBSzt3RkFBRTtnRkFDdkduQyxXQUFVOzs7Ozs7MEZBRVosOERBQUNMO2dGQUFJSyxXQUFVOztrR0FDYiw4REFBQ2dCO2tHQUFLOzs7Ozs7a0dBQ04sOERBQUNBO2tHQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBSVYsOERBQUNyQjt3RUFBSUssV0FBVTtrRkFDYiw0RUFBQ0c7NEVBQUVILFdBQVU7O2dGQUF3Qjs4RkFDaEMsOERBQUNrRDs4RkFBTzs7Ozs7O2dGQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MkNBakgvQjs7Ozs7d0NBMkhQbk8sY0FBYyxzQkFDYiw4REFBQ2pDLGlEQUFNQSxDQUFDNk0sR0FBRzs0Q0FFVEMsU0FBUztnREFBRUMsU0FBUztnREFBR3FCLEdBQUc7NENBQUc7NENBQzdCcEIsU0FBUztnREFBRUQsU0FBUztnREFBR3FCLEdBQUc7NENBQUU7NENBQzVCbkIsTUFBTTtnREFBRUYsU0FBUztnREFBR3FCLEdBQUcsQ0FBQzs0Q0FBRzs0Q0FDM0JsQixXQUFVO3NEQUVWLDRFQUFDTDs7a0VBQ0MsOERBQUN3Qjt3REFBR25CLFdBQVU7a0VBQXFDOzs7Ozs7a0VBR25ELDhEQUFDTDt3REFBSUssV0FBVTs7MEVBQ2IsOERBQUNHO2dFQUFFSCxXQUFVOzBFQUFnQjs7Ozs7OzBFQUc3Qiw4REFBQ0k7Z0VBQ0NDLFNBQVMsSUFBTXhJLG1CQUFtQixDQUFDRDtnRUFDbkNvSSxXQUFVOztrRkFHViw4REFBQ007d0VBQUlOLFdBQVU7d0VBQVVPLE1BQUs7d0VBQU9DLFFBQU87d0VBQWVDLFNBQVE7a0ZBQ2pFLDRFQUFDQzs0RUFBS0MsZUFBYzs0RUFBUUMsZ0JBQWU7NEVBQVFDLGFBQWE7NEVBQUdDLEdBQUU7Ozs7Ozs7Ozs7O2tGQUV2RSw4REFBQ0U7a0ZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvREFLVHBKLGlDQUNDLDhEQUFDOUUsaURBQU1BLENBQUM2TSxHQUFHO3dEQUNUQyxTQUFTOzREQUFFQyxTQUFTOzREQUFHc0QsUUFBUTt3REFBRTt3REFDakNyRCxTQUFTOzREQUFFRCxTQUFTOzREQUFHc0QsUUFBUTt3REFBTzt3REFDdENwRCxNQUFNOzREQUFFRixTQUFTOzREQUFHc0QsUUFBUTt3REFBRTt3REFDOUJuRCxXQUFVOzswRUFFViw4REFBQ29CO2dFQUFHcEIsV0FBVTswRUFBd0M7Ozs7OzswRUFDdEQsOERBQUNMO2dFQUFJSyxXQUFVOztrRkFDYiw4REFBQ0w7OzBGQUNDLDhEQUFDMEM7Z0ZBQU1yQyxXQUFVOzBGQUErQzs7Ozs7OzBGQUdoRSw4REFBQzRCO2dGQUNDQyxNQUFLO2dGQUNMTSxPQUFPckssWUFBWTVCLElBQUk7Z0ZBQ3ZCNkwsVUFBVSxDQUFDQyxJQUFNakssZUFBZTBGLENBQUFBLE9BQVM7NEZBQUUsR0FBR0EsSUFBSTs0RkFBRXZILE1BQU04TCxFQUFFQyxNQUFNLENBQUNFLEtBQUs7d0ZBQUM7Z0ZBQ3pFbkMsV0FBVTtnRkFHVm9DLGFBQVk7Ozs7Ozs7Ozs7OztrRkFHaEIsOERBQUN6Qzs7MEZBQ0MsOERBQUMwQztnRkFBTXJDLFdBQVU7MEZBQStDOzs7Ozs7MEZBR2hFLDhEQUFDNEI7Z0ZBQ0NDLE1BQUs7Z0ZBQ0xNLE9BQU9ySyxZQUFZM0IsR0FBRztnRkFDdEI0TCxVQUFVLENBQUNDLElBQU1qSyxlQUFlMEYsQ0FBQUEsT0FBUzs0RkFBRSxHQUFHQSxJQUFJOzRGQUFFdEgsS0FBSzZMLEVBQUVDLE1BQU0sQ0FBQ0UsS0FBSzt3RkFBQztnRkFDeEVuQyxXQUFVO2dGQUdWb0MsYUFBWTs7Ozs7Ozs7Ozs7O2tGQUdoQiw4REFBQ3pDOzswRkFDQyw4REFBQzBDO2dGQUFNckMsV0FBVTswRkFBK0M7Ozs7OzswRkFHaEUsOERBQUM0QjtnRkFDQ0MsTUFBSztnRkFDTE0sT0FBT3JLLFlBQVkxQixNQUFNO2dGQUN6QjJMLFVBQVUsQ0FBQ0MsSUFBTWpLLGVBQWUwRixDQUFBQSxPQUFTOzRGQUFFLEdBQUdBLElBQUk7NEZBQUVySCxRQUFRNEwsRUFBRUMsTUFBTSxDQUFDRSxLQUFLO3dGQUFDO2dGQUMzRW5DLFdBQVU7Z0ZBR1ZvQyxhQUFZOzs7Ozs7Ozs7Ozs7a0ZBR2hCLDhEQUFDekM7OzBGQUNDLDhEQUFDMEM7Z0ZBQU1yQyxXQUFVOzBGQUErQzs7Ozs7OzBGQUdoRSw4REFBQzRCO2dGQUNDQyxNQUFLO2dGQUNMTSxPQUFPckssWUFBWXpCLFlBQVk7Z0ZBQy9CMEwsVUFBVSxDQUFDQyxJQUFNakssZUFBZTBGLENBQUFBLE9BQVM7NEZBQUUsR0FBR0EsSUFBSTs0RkFBRXBILGNBQWMyTCxFQUFFQyxNQUFNLENBQUNFLEtBQUs7d0ZBQUM7Z0ZBQ2pGbkMsV0FBVTtnRkFHVm9DLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFJbEIsOERBQUN6QztnRUFBSUssV0FBVTs7a0ZBQ2IsOERBQUNJO3dFQUNDQyxTQUFTLElBQU14SSxtQkFBbUI7d0VBQ2xDbUksV0FBVTtrRkFDWDs7Ozs7O2tGQUdELDhEQUFDSTt3RUFDQ0MsU0FBU3RDO3dFQUNUaUMsV0FBVTtrRkFFWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQVFQLDhEQUFDTDt3REFBSUssV0FBVTtrRUFDWmhLLFlBQVkyRSxHQUFHLENBQUMsQ0FBQ3FDLFVBQVVDLHNCQUMxQiw4REFBQzBDO2dFQUFnQkssV0FBVTs7a0ZBQ3pCLDhEQUFDTDt3RUFBSUssV0FBVTs7MEZBQ2IsOERBQUNMO2dGQUFJSyxXQUFVOztrR0FDYiw4REFBQ0w7d0ZBQUlLLFdBQVcsd0JBQXdFLE9BQWhEaEQsU0FBUzFHLEtBQUssR0FBRyxpQkFBaUI7Ozs7OztrR0FDMUUsOERBQUM4Szt3RkFBR3BCLFdBQVU7a0dBQW9DaEQsU0FBUzlHLElBQUk7Ozs7OztvRkFDN0Q4RyxDQUFBQSxTQUFTOUcsSUFBSSxLQUFLLGdCQUFnQjhHLFNBQVM5RyxJQUFJLEtBQUssVUFBUyxtQkFDN0QsOERBQUM4Szt3RkFBS2hCLFdBQVU7a0dBQThEOzs7Ozs7Ozs7Ozs7MEZBS2xGLDhEQUFDTDtnRkFBSUssV0FBVTs7a0dBQ2IsOERBQUNJO3dGQUNDQyxTQUFTLElBQU1yQyxxQkFBcUJmO3dGQUNwQytDLFdBQVcsd0VBSVYsT0FIQ2hELFNBQVMxRyxLQUFLLEdBQ1YsK0NBQ0E7a0dBR0wwRyxTQUFTMUcsS0FBSyxHQUFHLFVBQVU7Ozs7OztrR0FFOUIsOERBQUM4Sjt3RkFDQ0MsU0FBUyxJQUFNN0IsbUJBQW1CeEI7d0ZBQ2xDc0YsVUFBVXJOLFdBQVcsQ0FBQytILFNBQVM1RyxNQUFNO3dGQUNyQzRKLFdBQVU7a0dBRVg7Ozs7OztrR0FHRCw4REFBQ0k7d0ZBQ0NDLFNBQVMsSUFBTWhDLG1CQUFtQnBCO3dGQUNsQytDLFdBQVU7a0dBRVg7Ozs7OztvRkFHQWhELFNBQVM5RyxJQUFJLEtBQUssZ0JBQWdCOEcsU0FBUzlHLElBQUksS0FBSyw0QkFDbkQsOERBQUNrSzt3RkFDQ0MsU0FBUyxJQUFNbkMscUJBQXFCakI7d0ZBQ3BDK0MsV0FBVTtrR0FFWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tGQU9QLDhEQUFDTDt3RUFBSUssV0FBVTs7MEZBQ2IsOERBQUNMOztrR0FDQyw4REFBQ3FCO3dGQUFLaEIsV0FBVTtrR0FBZ0I7Ozs7OztrR0FDaEMsOERBQUNHO3dGQUFFSCxXQUFVO2tHQUEwQ2hELFNBQVM3RyxHQUFHOzs7Ozs7Ozs7Ozs7MEZBRXJFLDhEQUFDd0o7O2tHQUNDLDhEQUFDcUI7d0ZBQUtoQixXQUFVO2tHQUFnQjs7Ozs7O2tHQUNoQyw4REFBQ0c7d0ZBQUVILFdBQVU7a0dBQWNoRCxTQUFTM0csWUFBWSxJQUFJOzs7Ozs7Ozs7Ozs7MEZBRXRELDhEQUFDc0o7O2tHQUNDLDhEQUFDcUI7d0ZBQUtoQixXQUFVO2tHQUFnQjs7Ozs7O2tHQUNoQyw4REFBQ0c7d0ZBQUVILFdBQVU7a0dBQ1ZoRCxTQUFTNUcsTUFBTSxHQUFHLGlCQUFpQjRHLFNBQVM1RyxNQUFNLENBQUNnTixLQUFLLENBQUMsQ0FBQyxLQUFLOzs7Ozs7Ozs7Ozs7MEZBR3BFLDhEQUFDekQ7O2tHQUNDLDhEQUFDcUI7d0ZBQUtoQixXQUFVO2tHQUFnQjs7Ozs7O2tHQUNoQyw4REFBQ0c7d0ZBQUVILFdBQVcsZUFBbUUsT0FBcERoRCxTQUFTMUcsS0FBSyxHQUFHLG1CQUFtQjtrR0FDOUQwRyxTQUFTMUcsS0FBSyxHQUFHLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztvRUFNakMwQixvQkFBb0JpRix1QkFDbkIsOERBQUNuSyxpREFBTUEsQ0FBQzZNLEdBQUc7d0VBQ1RDLFNBQVM7NEVBQUVDLFNBQVM7NEVBQUdzRCxRQUFRO3dFQUFFO3dFQUNqQ3JELFNBQVM7NEVBQUVELFNBQVM7NEVBQUdzRCxRQUFRO3dFQUFPO3dFQUN0Q3BELE1BQU07NEVBQUVGLFNBQVM7NEVBQUdzRCxRQUFRO3dFQUFFO3dFQUM5Qm5ELFdBQVU7OzBGQUVWLDhEQUFDK0M7Z0ZBQUcvQyxXQUFVOzBGQUFnQzs7Ozs7OzBGQUM5Qyw4REFBQ0w7Z0ZBQUlLLFdBQVU7O2tHQUNiLDhEQUFDTDs7MEdBQ0MsOERBQUMwQztnR0FBTXJDLFdBQVU7MEdBQStDOzs7Ozs7MEdBR2hFLDhEQUFDNEI7Z0dBQ0NDLE1BQUs7Z0dBQ0xNLE9BQU9qSyxpQkFBaUI5QixNQUFNO2dHQUM5QjJMLFVBQVUsQ0FBQ0MsSUFBTTdKLG9CQUFvQnNGLENBQUFBLE9BQVM7NEdBQUUsR0FBR0EsSUFBSTs0R0FBRXJILFFBQVE0TCxFQUFFQyxNQUFNLENBQUNFLEtBQUs7d0dBQUM7Z0dBQ2hGbkMsV0FBVTtnR0FHVm9DLGFBQVk7Ozs7Ozs7Ozs7OztrR0FHaEIsOERBQUN6Qzs7MEdBQ0MsOERBQUMwQztnR0FBTXJDLFdBQVU7MEdBQStDOzs7Ozs7MEdBR2hFLDhEQUFDNEI7Z0dBQ0NDLE1BQUs7Z0dBQ0xNLE9BQU9qSyxpQkFBaUI3QixZQUFZO2dHQUNwQzBMLFVBQVUsQ0FBQ0MsSUFBTTdKLG9CQUFvQnNGLENBQUFBLE9BQVM7NEdBQUUsR0FBR0EsSUFBSTs0R0FBRXBILGNBQWMyTCxFQUFFQyxNQUFNLENBQUNFLEtBQUs7d0dBQUM7Z0dBQ3RGbkMsV0FBVTtnR0FHVm9DLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRkFJbEIsOERBQUN6QztnRkFBSUssV0FBVTs7a0dBQ2IsOERBQUNJO3dGQUNDQyxTQUFTOUI7d0ZBQ1R5QixXQUFVO2tHQUVYOzs7Ozs7a0dBR0QsOERBQUNJO3dGQUNDQyxTQUFTL0I7d0ZBQ1QwQixXQUFVO2tHQUVYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0VBUUxoRCxDQUFBQSxTQUFTOUcsSUFBSSxLQUFLLGdCQUFnQjhHLFNBQVM5RyxJQUFJLEtBQUssVUFBUyxLQUFNLENBQUM4RyxTQUFTNUcsTUFBTSxJQUFJNEIsb0JBQW9CaUYsdUJBQzNHLDhEQUFDMEM7d0VBQUlLLFdBQVU7OzBGQUNiLDhEQUFDcUM7Z0ZBQU1yQyxXQUFVOzBGQUErQzs7Ozs7OzBGQUdoRSw4REFBQ0w7Z0ZBQUlLLFdBQVU7O2tHQUNiLDhEQUFDNEI7d0ZBQ0NDLE1BQUs7d0ZBQ0xPLGFBQVk7d0ZBQ1pwQyxXQUFVO3dGQUdWK0IsVUFBVSxDQUFDQzs0RkFDVCxNQUFNcUIsU0FBU3JCLEVBQUVDLE1BQU0sQ0FBQ0UsS0FBSzs0RkFDN0JsTSxlQUFld0gsQ0FBQUEsT0FBUUEsS0FBSzlDLEdBQUcsQ0FBQyxDQUFDMkksSUFBSXJGLElBQ25DQSxNQUFNaEIsUUFBUTt3R0FBRSxHQUFHcUcsRUFBRTt3R0FBRWxOLFFBQVFpTjtvR0FBTyxJQUFJQzt3RkFFOUM7Ozs7OztrR0FFRiw4REFBQ2xEO3dGQUNDQyxTQUFTLElBQU1yQyxxQkFBcUJmO3dGQUNwQytDLFdBQVU7a0dBRVg7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7K0RBMUpDL0M7Ozs7Ozs7Ozs7Ozs7Ozs7MkNBbEhaOzs7Ozt3Q0F5UlBsSSxjQUFjLDJCQUNiLDhEQUFDakMsaURBQU1BLENBQUM2TSxHQUFHOzRDQUVUQyxTQUFTO2dEQUFFQyxTQUFTO2dEQUFHcUIsR0FBRzs0Q0FBRzs0Q0FDN0JwQixTQUFTO2dEQUFFRCxTQUFTO2dEQUFHcUIsR0FBRzs0Q0FBRTs0Q0FDNUJuQixNQUFNO2dEQUFFRixTQUFTO2dEQUFHcUIsR0FBRyxDQUFDOzRDQUFHOzRDQUMzQmxCLFdBQVU7c0RBRVYsNEVBQUNMOztrRUFDQyw4REFBQ3dCO3dEQUFHbkIsV0FBVTtrRUFBcUM7Ozs7OztrRUFHbkQsOERBQUNMO3dEQUFJSyxXQUFVOzswRUFDYiw4REFBQ0k7Z0VBQ0NDLFNBQVMsSUFBTXJKLG1CQUFtQixDQUFDRDtnRUFDbkNpSixXQUFVOztrRkFHViw4REFBQ007d0VBQUlOLFdBQVU7d0VBQVVPLE1BQUs7d0VBQU9DLFFBQU87d0VBQWVDLFNBQVE7a0ZBQ2pFLDRFQUFDQzs0RUFBS0MsZUFBYzs0RUFBUUMsZ0JBQWU7NEVBQVFDLGFBQWE7NEVBQzlEQyxHQUFFOzs7Ozs7Ozs7OztrRkFFTiw4REFBQ0U7a0ZBQUs7Ozs7Ozs7Ozs7OzswRUFFUiw4REFBQ1o7Z0VBQ0NDLFNBQVMsSUFBTXZKLGlCQUFpQixDQUFDRDtnRUFDakNtSixXQUFVOztrRkFHViw4REFBQ007d0VBQUlOLFdBQVU7d0VBQVVPLE1BQUs7d0VBQU9DLFFBQU87d0VBQWVDLFNBQVE7a0ZBQ2pFLDRFQUFDQzs0RUFBS0MsZUFBYzs0RUFBUUMsZ0JBQWU7NEVBQVFDLGFBQWE7NEVBQzlEQyxHQUFFOzs7Ozs7Ozs7OztrRkFFTiw4REFBQ0U7a0ZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvREFLVGpLLGlDQUNDLDhEQUFDakUsaURBQU1BLENBQUM2TSxHQUFHO3dEQUNUQyxTQUFTOzREQUFFQyxTQUFTOzREQUFHc0QsUUFBUTt3REFBRTt3REFDakNyRCxTQUFTOzREQUFFRCxTQUFTOzREQUFHc0QsUUFBUTt3REFBTzt3REFDdENwRCxNQUFNOzREQUFFRixTQUFTOzREQUFHc0QsUUFBUTt3REFBRTt3REFDOUJuRCxXQUFVOzswRUFFViw4REFBQ29CO2dFQUFHcEIsV0FBVTswRUFBd0M7Ozs7OzswRUFDdEQsOERBQUNMO2dFQUFJSyxXQUFVOztrRkFDYiw4REFBQ0w7OzBGQUNDLDhEQUFDMEM7Z0ZBQU1yQyxXQUFVOzBGQUErQzs7Ozs7OzBGQUdoRSw4REFBQzRCO2dGQUNDQyxNQUFLO2dGQUNMTSxPQUFPMUssWUFBWXZCLElBQUk7Z0ZBQ3ZCNkwsVUFBVSxDQUFDQyxJQUFNdEssZUFBZStGLENBQUFBLE9BQVM7NEZBQUUsR0FBR0EsSUFBSTs0RkFBRXZILE1BQU04TCxFQUFFQyxNQUFNLENBQUNFLEtBQUs7d0ZBQUM7Z0ZBQ3pFbkMsV0FBVTtnRkFHVm9DLGFBQVk7Ozs7Ozs7Ozs7OztrRkFHaEIsOERBQUN6Qzs7MEZBQ0MsOERBQUMwQztnRkFBTXJDLFdBQVU7MEZBQStDOzs7Ozs7MEZBR2hFLDhEQUFDdUQ7Z0ZBQ0NwQixPQUFPMUssWUFBWUUsU0FBUztnRkFDNUJvSyxVQUFVLENBQUNDLElBQU10SyxlQUFlK0YsQ0FBQUEsT0FBUzs0RkFBRSxHQUFHQSxJQUFJOzRGQUFFOUYsV0FBV3FLLEVBQUVDLE1BQU0sQ0FBQ0UsS0FBSzt3RkFBQztnRkFDOUVuQyxXQUFVO2dGQUdWd0QsTUFBTTtnRkFDTnBCLGFBQVk7Ozs7Ozs7Ozs7OztrRkFHaEIsOERBQUN6Qzs7MEZBQ0MsOERBQUMwQztnRkFBTXJDLFdBQVU7MEZBQStDOzs7Ozs7MEZBR2hFLDhEQUFDTDtnRkFBSUssV0FBVTswRkFDWk4sT0FBTy9FLEdBQUcsQ0FBQyxDQUFDOEksc0JBQ1gsOERBQUNyRDt3RkFFQ0MsU0FBUyxJQUFNM0ksZUFBZStGLENBQUFBLE9BQVM7b0dBQUUsR0FBR0EsSUFBSTtvR0FBRXBHLEtBQUtvTTtnR0FBTTt3RkFDN0R6RCxXQUFXLDZEQUVWLE9BREN2SSxZQUFZSixHQUFHLEtBQUtvTSxRQUFRLDJCQUEyQjt3RkFFekRoQixPQUFPOzRGQUFFaUIsaUJBQWlCRDt3RkFBTTt1RkFMM0JBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQVdmLDhEQUFDOUQ7Z0VBQUlLLFdBQVU7O2tGQUNiLDhEQUFDSTt3RUFDQ0MsU0FBUyxJQUFNckosbUJBQW1CO3dFQUNsQ2dKLFdBQVU7a0ZBQ1g7Ozs7OztrRkFHRCw4REFBQ0k7d0VBQ0NDLFNBQVNmO3dFQUNUVSxXQUFVO2tGQUVYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0RBUU5uSiwrQkFDQyw4REFBQy9ELGlEQUFNQSxDQUFDNk0sR0FBRzt3REFDVEMsU0FBUzs0REFBRUMsU0FBUzs0REFBR3NELFFBQVE7d0RBQUU7d0RBQ2pDckQsU0FBUzs0REFBRUQsU0FBUzs0REFBR3NELFFBQVE7d0RBQU87d0RBQ3RDcEQsTUFBTTs0REFBRUYsU0FBUzs0REFBR3NELFFBQVE7d0RBQUU7d0RBQzlCbkQsV0FBVTs7MEVBRVYsOERBQUNvQjtnRUFBR3BCLFdBQVU7MEVBQXdDOzs7Ozs7MEVBQ3RELDhEQUFDTDtnRUFBSUssV0FBVTs7a0ZBQ2IsOERBQUNMOzswRkFDQyw4REFBQzBDO2dGQUFNckMsV0FBVTswRkFBK0M7Ozs7OzswRkFHaEUsOERBQUM0QjtnRkFDQ0MsTUFBSztnRkFDTE0sT0FBT2xMLFVBQVVFLE1BQU07Z0ZBQ3ZCNEssVUFBVSxDQUFDQyxJQUFNOUssYUFBYXVHLENBQUFBLE9BQVM7NEZBQUUsR0FBR0EsSUFBSTs0RkFBRXRHLFFBQVE2SyxFQUFFQyxNQUFNLENBQUNFLEtBQUs7d0ZBQUM7Z0ZBQ3pFbkMsV0FBVTtnRkFHVm9DLGFBQVk7Ozs7Ozs7Ozs7OztrRkFHaEIsOERBQUN6Qzs7MEZBQ0MsOERBQUMwQztnRkFBTXJDLFdBQVU7MEZBQStDOzs7Ozs7MEZBR2hFLDhEQUFDdUQ7Z0ZBQ0NwQixPQUFPbEwsVUFBVUcsUUFBUTtnRkFDekIySyxVQUFVLENBQUNDLElBQU05SyxhQUFhdUcsQ0FBQUEsT0FBUzs0RkFBRSxHQUFHQSxJQUFJOzRGQUFFckcsVUFBVTRLLEVBQUVDLE1BQU0sQ0FBQ0UsS0FBSzt3RkFBQztnRkFDM0VuQyxXQUFVO2dGQUdWd0QsTUFBTTtnRkFDTnBCLGFBQVk7Ozs7Ozs7Ozs7OztrRkFHaEIsOERBQUN6Qzt3RUFBSUssV0FBVTs7MEZBQ2IsOERBQUNMOztrR0FDQyw4REFBQzBDO3dGQUFNckMsV0FBVTtrR0FBK0M7Ozs7OztrR0FHaEUsOERBQUN1Qzt3RkFDQ0osT0FBT2xMLFVBQVVLLFNBQVMsSUFBSTt3RkFDOUJ5SyxVQUFVLENBQUNDLElBQU05SyxhQUFhdUcsQ0FBQUEsT0FBUztvR0FBRSxHQUFHQSxJQUFJO29HQUFFbkcsV0FBVzBLLEVBQUVDLE1BQU0sQ0FBQ0UsS0FBSyxJQUFJO2dHQUFLO3dGQUNwRm5DLFdBQVU7OzBHQUlWLDhEQUFDd0M7Z0dBQU9MLE9BQU07Z0dBQUduQyxXQUFVOzBHQUFjOzs7Ozs7NEZBQ3hDdkosaUJBQWlCa0UsR0FBRyxDQUFDLENBQUN3QyxVQUFVRixzQkFDL0IsOERBQUN1RjtvR0FBbUJMLE9BQU9oRixTQUFTakgsSUFBSTtvR0FBRThKLFdBQVU7OEdBQ2pEN0MsU0FBU2pILElBQUk7bUdBREgrRzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEZBTW5CLDhEQUFDMEM7O2tHQUNDLDhEQUFDMEM7d0ZBQU1yQyxXQUFVO2tHQUErQzs7Ozs7O2tHQUdoRSw4REFBQ0w7d0ZBQUlLLFdBQVU7a0dBQ1pOLE9BQU8wRCxLQUFLLENBQUMsR0FBRyxHQUFHekksR0FBRyxDQUFDLENBQUM4SSxzQkFDdkIsOERBQUNyRDtnR0FFQ0MsU0FBUyxJQUFNbkosYUFBYXVHLENBQUFBLE9BQVM7NEdBQUUsR0FBR0EsSUFBSTs0R0FBRXBHLEtBQUtvTTt3R0FBTTtnR0FDM0R6RCxXQUFXLDZEQUVWLE9BREMvSSxVQUFVSSxHQUFHLEtBQUtvTSxRQUFRLDJCQUEyQjtnR0FFdkRoQixPQUFPO29HQUFFaUIsaUJBQWlCRDtnR0FBTTsrRkFMM0JBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tGQVdmLDhEQUFDOUQ7d0VBQUlLLFdBQVU7OzBGQUNiLDhEQUFDcUM7Z0ZBQU1yQyxXQUFVOzBGQUEwQzs7Ozs7OzBGQUczRCw4REFBQ3VDO2dGQUNDSixPQUFPbEwsVUFBVU8sTUFBTSxHQUFHLFdBQVlQLFVBQVVNLE1BQU0sSUFBSTtnRkFDMUR3SyxVQUFVLENBQUNDO29GQUNULE1BQU1HLFFBQVFILEVBQUVDLE1BQU0sQ0FBQ0UsS0FBSztvRkFDNUIsSUFBSUEsVUFBVSxVQUFVO3dGQUN0QmpMLGFBQWF1RyxDQUFBQSxPQUFTO2dHQUFFLEdBQUdBLElBQUk7Z0dBQUVqRyxRQUFRO2dHQUFNRCxRQUFROzRGQUFLO29GQUM5RCxPQUFPO3dGQUNMTCxhQUFhdUcsQ0FBQUEsT0FBUztnR0FBRSxHQUFHQSxJQUFJO2dHQUFFakcsUUFBUTtnR0FBT0QsUUFBUTRLOzRGQUFNO29GQUNoRTtnRkFDRjtnRkFDQW5DLFdBQVU7O2tHQUlWLDhEQUFDd0M7d0ZBQU9MLE9BQU07d0ZBQVNuQyxXQUFVO2tHQUF5Qjs7Ozs7O2tHQUcxRCw4REFBQzJEO3dGQUFTdEIsT0FBTTt3RkFBb0JyQyxXQUFVO2tHQUMzQ3JKLE1BQU1nRSxHQUFHLENBQUMsQ0FBQ2lKLHFCQUNWLDhEQUFDcEI7Z0dBQXFCTCxPQUFPeUIsS0FBS2hLLEVBQUU7Z0dBQUVvRyxXQUFVOztvR0FBeUI7b0dBQ25FNEQsS0FBSy9KLElBQUk7OytGQURGK0osS0FBS2hLLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7MEZBTTFCLDhEQUFDdUc7Z0ZBQUVILFdBQVU7MEZBQ1YvSSxVQUFVTyxNQUFNLEdBQ2IsOERBQ0E7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFLViw4REFBQ21JO2dFQUFJSyxXQUFVOztrRkFDYiw4REFBQ0k7d0VBQ0NDLFNBQVMsSUFBTXZKLGlCQUFpQjt3RUFDaENrSixXQUFVO2tGQUNYOzs7Ozs7a0ZBR0QsOERBQUNJO3dFQUNDQyxTQUFTZDt3RUFDVFMsV0FBVTtrRkFFWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O29EQVFOdkosaUJBQWlCbUYsTUFBTSxHQUFHLG1CQUN6Qiw4REFBQytEO3dEQUFJSyxXQUFVOzswRUFDYiw4REFBQ29CO2dFQUFHcEIsV0FBVTswRUFBd0M7Ozs7OzswRUFDdEQsOERBQUNMO2dFQUFJSyxXQUFVOzBFQUNadkosaUJBQWlCa0UsR0FBRyxDQUFDLENBQUN3QyxVQUFVRixzQkFDL0IsOERBQUMwQzt3RUFBZ0JLLFdBQVU7OzBGQUN6Qiw4REFBQ0w7Z0ZBQUlLLFdBQVU7O2tHQUNiLDhEQUFDTDt3RkFBSUssV0FBVTs7MEdBQ2IsOERBQUNMO2dHQUNDSyxXQUFVO2dHQUNWeUMsT0FBTztvR0FBRWlCLGlCQUFpQnZHLFNBQVM5RixHQUFHO2dHQUFDOzs7Ozs7MEdBRXpDLDhEQUFDMEw7Z0dBQUcvQyxXQUFVOzBHQUEwQjdDLFNBQVNqSCxJQUFJOzs7Ozs7Ozs7Ozs7a0dBRXZELDhEQUFDa0s7d0ZBQ0NDLFNBQVMsSUFBTVoscUJBQXFCeEM7d0ZBQ3BDK0MsV0FBVTtrR0FFViw0RUFBQ007NEZBQUlOLFdBQVU7NEZBQVVPLE1BQUs7NEZBQU9DLFFBQU87NEZBQWVDLFNBQVE7c0dBQ2pFLDRFQUFDQztnR0FBS0MsZUFBYztnR0FBUUMsZ0JBQWU7Z0dBQVFDLGFBQWE7Z0dBQzlEQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRFQUlUM0QsU0FBU3hGLFNBQVMsa0JBQ2pCLDhEQUFDd0k7Z0ZBQUVILFdBQVU7MEZBQXlCN0MsU0FBU3hGLFNBQVM7Ozs7Ozs7dUVBcEJsRHNGOzs7Ozs7Ozs7Ozs7Ozs7O2tFQTZCbEIsOERBQUMwQzs7MEVBQ0MsOERBQUN5QjtnRUFBR3BCLFdBQVU7O29FQUF3QztvRUFDekN6SixTQUFTcUYsTUFBTTtvRUFBQzs7Ozs7Ozs0REFFNUJyRixTQUFTcUYsTUFBTSxLQUFLLGtCQUNuQiw4REFBQytEO2dFQUFJSyxXQUFVOztrRkFDYiw4REFBQ007d0VBQUlOLFdBQVU7d0VBQXVDTyxNQUFLO3dFQUFPQyxRQUFPO3dFQUFlQyxTQUFRO2tGQUM5Riw0RUFBQ0M7NEVBQUtDLGVBQWM7NEVBQVFDLGdCQUFlOzRFQUFRQyxhQUFhOzRFQUM5REMsR0FBRTs7Ozs7Ozs7Ozs7a0ZBRU4sOERBQUNYO3dFQUFFSCxXQUFVO2tGQUFnQjs7Ozs7O2tGQUM3Qiw4REFBQ0c7d0VBQUVILFdBQVU7a0ZBQTZCOzs7Ozs7Ozs7OztxRkFLNUMsOERBQUNMO2dFQUFJSyxXQUFVOzBFQUNaekosU0FBU29FLEdBQUcsQ0FBQyxDQUFDdUMsUUFBUUQ7d0VBdUJDdEc7eUZBdEJ0Qiw4REFBQ2dKO3dFQUFnQkssV0FBVTs7MEZBQ3pCLDhEQUFDTDtnRkFBSUssV0FBVTs7a0dBQ2IsOERBQUNMO3dGQUFJSyxXQUFVOzswR0FDYiw4REFBQ0w7Z0dBQ0NLLFdBQVU7Z0dBQ1Z5QyxPQUFPO29HQUFFaUIsaUJBQWlCeEcsT0FBTzdGLEdBQUc7Z0dBQUM7Ozs7OzswR0FFdkMsOERBQUNzSTs7a0hBQ0MsOERBQUNvRDt3R0FBRy9DLFdBQVU7a0hBQTRCOUMsT0FBTy9GLE1BQU07Ozs7OztrSEFDdkQsOERBQUN3STt3R0FBSUssV0FBVTs7NEdBQ1o5QyxPQUFPNUYsU0FBUyxrQkFDZiw4REFBQzBKO2dIQUFLaEIsV0FBVTswSEFDYjlDLE9BQU81RixTQUFTOzs7Ozs7MEhBR3JCLDhEQUFDMEo7Z0hBQUtoQixXQUFXLGtDQUloQixPQUhDOUMsT0FBTzFGLE1BQU0sR0FDVCxtQ0FDQTswSEFFSDBGLE9BQU8xRixNQUFNLEdBQ1Ysd0JBQ0EsZ0JBQStFLE9BQXpFYixFQUFBQSxjQUFBQSxNQUFNa04sSUFBSSxDQUFDRCxDQUFBQSxPQUFRQSxLQUFLaEssRUFBRSxLQUFLc0QsT0FBTzNGLE1BQU0sZUFBNUNaLGtDQUFBQSxZQUErQ2tELElBQUksS0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tHQU16RSw4REFBQ3VHO3dGQUNDQyxTQUFTLElBQU1iLG1CQUFtQnZDO3dGQUNsQytDLFdBQVU7a0dBRVYsNEVBQUNNOzRGQUFJTixXQUFVOzRGQUFVTyxNQUFLOzRGQUFPQyxRQUFPOzRGQUFlQyxTQUFRO3NHQUNqRSw0RUFBQ0M7Z0dBQUtDLGVBQWM7Z0dBQVFDLGdCQUFlO2dHQUFRQyxhQUFhO2dHQUM5REMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRkFJViw4REFBQ1g7Z0ZBQUVILFdBQVU7MEZBQ1Y5QyxPQUFPOUYsUUFBUTs7Ozs7Ozt1RUF2Q1Y2Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MkNBclNoQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0EyVmQsOERBQUMwQzt3QkFBSUssV0FBVTs7MENBQ2IsOERBQUNMO2dDQUFJSyxXQUFVOzBDQUNiLDRFQUFDSTtvQ0FDQ0MsU0FBU3hDO29DQUNUbUMsV0FBVTs4Q0FFWDs7Ozs7Ozs7Ozs7MENBT0gsOERBQUNMO2dDQUFJSyxXQUFVOztrREFDYiw4REFBQ0k7d0NBQ0NDLFNBQVM5TDt3Q0FDVHlMLFdBQVU7a0RBQ1g7Ozs7OztrREFHRCw4REFBQ0k7d0NBQ0NDLFNBQVN6RDt3Q0FDVDBGLFVBQVVyTjt3Q0FDVitLLFdBQVU7a0RBR1QvSyxVQUFVLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVM3QztHQTdwRHdCWjs7UUFNR3JCLDBEQUFPQTtRQUN1QkMsc0VBQWFBOzs7S0FQOUNvQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvU2V0dGluZ3NNb2RhbC50c3g/YTFlMyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VSZWYsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IG1vdGlvbiwgQW5pbWF0ZVByZXNlbmNlIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyB1c2VBcHBlYXJhbmNlIH0gZnJvbSAnQC9jb250ZXh0cy9BcHBlYXJhbmNlQ29udGV4dCc7XG5pbXBvcnQgeyBkb2MsIHVwZGF0ZURvYywgc2V0RG9jLCBnZXREb2MsIGRlbGV0ZURvYywgY29sbGVjdGlvbiwgZ2V0RG9jcywgcXVlcnksIG9yZGVyQnkgfSBmcm9tICdmaXJlYmFzZS9maXJlc3RvcmUnO1xuaW1wb3J0IHsgcmVmLCB1cGxvYWRCeXRlcywgZ2V0RG93bmxvYWRVUkwsIGxpc3RBbGwsIGRlbGV0ZU9iamVjdCB9IGZyb20gJ2ZpcmViYXNlL3N0b3JhZ2UnO1xuaW1wb3J0IHsgdXBkYXRlUGFzc3dvcmQsIHJlYXV0aGVudGljYXRlV2l0aENyZWRlbnRpYWwsIEVtYWlsQXV0aFByb3ZpZGVyIH0gZnJvbSAnZmlyZWJhc2UvYXV0aCc7XG5pbXBvcnQgeyBkYiwgc3RvcmFnZSB9IGZyb20gJ0AvbGliL2ZpcmViYXNlJztcblxuaW50ZXJmYWNlIFVzZXJEYXRhIHtcbiAgdXNlcm5hbWU6IHN0cmluZztcbiAgZW1haWw6IHN0cmluZztcbiAgYmFsYW5jZTogbnVtYmVyO1xuICBjcmVhdGVkQXQ6IHN0cmluZztcbiAgcHJvZmlsZUltYWdlPzogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgU2V0dGluZ3NNb2RhbFByb3BzIHtcbiAgaXNPcGVuOiBib29sZWFuO1xuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xuICB1c2VyRGF0YTogVXNlckRhdGE7XG4gIG9uVXNlckRhdGFVcGRhdGU6ICh1c2VyRGF0YTogVXNlckRhdGEpID0+IHZvaWQ7XG59XG5cbmludGVyZmFjZSBBcHBlYXJhbmNlU2V0dGluZ3Mge1xuICBmb250ZTogc3RyaW5nO1xuICB0YW1hbmhvRm9udGU6IG51bWJlcjtcbiAgcGFsYXZyYXNQb3JTZXNzYW86IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIEFJRW5kcG9pbnQge1xuICBub21lOiBzdHJpbmc7XG4gIHVybDogc3RyaW5nO1xuICBhcGlLZXk6IHN0cmluZztcbiAgbW9kZWxvUGFkcmFvOiBzdHJpbmc7XG4gIGF0aXZvOiBib29sZWFuO1xufVxuXG5pbnRlcmZhY2UgTWVtb3J5IHtcbiAgdGl0dWxvOiBzdHJpbmc7XG4gIGNvbnRldWRvOiBzdHJpbmc7XG4gIGNvcjogc3RyaW5nO1xuICBjYXRlZ29yaWE6IHN0cmluZyB8IG51bGw7XG4gIGNoYXRJZDogc3RyaW5nIHwgbnVsbDtcbiAgZ2xvYmFsOiBib29sZWFuO1xufVxuXG5pbnRlcmZhY2UgQ2hhdCB7XG4gIGlkOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgbGFzdE1lc3NhZ2U6IHN0cmluZztcbiAgbGFzdE1lc3NhZ2VUaW1lOiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBNZW1vcnlDYXRlZ29yeSB7XG4gIG5vbWU6IHN0cmluZztcbiAgZGVzY3JpY2FvOiBzdHJpbmc7XG4gIGNvcjogc3RyaW5nO1xufVxuXG50eXBlIFRhYlR5cGUgPSAnZ2VyYWwnIHwgJ2FwYXJlbmNpYScgfCAnaWEnIHwgJ21lbW9yaWEnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTZXR0aW5nc01vZGFsKHtcbiAgaXNPcGVuLFxuICBvbkNsb3NlLFxuICB1c2VyRGF0YSxcbiAgb25Vc2VyRGF0YVVwZGF0ZVxufTogU2V0dGluZ3NNb2RhbFByb3BzKSB7XG4gIGNvbnN0IHsgbG9nb3V0LCB1c2VyIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IHsgc2V0dGluZ3M6IGNvbnRleHRTZXR0aW5ncywgcmVmcmVzaFNldHRpbmdzIH0gPSB1c2VBcHBlYXJhbmNlKCk7XG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZTxUYWJUeXBlPignZ2VyYWwnKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBmaWxlSW5wdXRSZWYgPSB1c2VSZWY8SFRNTElucHV0RWxlbWVudD4obnVsbCk7XG5cbiAgLy8gRXN0YWRvcyBwYXJhIGNhZGEgYWJhXG4gIGNvbnN0IFtnZW5lcmFsRGF0YSwgc2V0R2VuZXJhbERhdGFdID0gdXNlU3RhdGUoe1xuICAgIHVzZXJuYW1lOiB1c2VyRGF0YS51c2VybmFtZSxcbiAgICBwcm9maWxlSW1hZ2U6IHVzZXJEYXRhLnByb2ZpbGVJbWFnZSB8fCAnJyxcbiAgICBjdXJyZW50UGFzc3dvcmQ6ICcnLFxuICAgIG5ld1Bhc3N3b3JkOiAnJyxcbiAgICBjb25maXJtUGFzc3dvcmQ6ICcnXG4gIH0pO1xuXG4gIGNvbnN0IFthcHBlYXJhbmNlU2V0dGluZ3MsIHNldEFwcGVhcmFuY2VTZXR0aW5nc10gPSB1c2VTdGF0ZTxBcHBlYXJhbmNlU2V0dGluZ3M+KHtcbiAgICBmb250ZTogY29udGV4dFNldHRpbmdzLmZvbnRlLFxuICAgIHRhbWFuaG9Gb250ZTogY29udGV4dFNldHRpbmdzLnRhbWFuaG9Gb250ZSxcbiAgICBwYWxhdnJhc1BvclNlc3NhbzogY29udGV4dFNldHRpbmdzLnBhbGF2cmFzUG9yU2Vzc2FvXG4gIH0pO1xuXG4gIGNvbnN0IFthaUVuZHBvaW50cywgc2V0QWlFbmRwb2ludHNdID0gdXNlU3RhdGU8QUlFbmRwb2ludFtdPihbXG4gICAge1xuICAgICAgbm9tZTogJ09wZW5Sb3V0ZXInLFxuICAgICAgdXJsOiAnaHR0cHM6Ly9vcGVucm91dGVyLmFpL2FwaS92MS9jaGF0L2NvbXBsZXRpb25zJyxcbiAgICAgIGFwaUtleTogJycsXG4gICAgICBtb2RlbG9QYWRyYW86ICdtZXRhLWxsYW1hL2xsYW1hLTMuMS04Yi1pbnN0cnVjdDpmcmVlJyxcbiAgICAgIGF0aXZvOiBmYWxzZVxuICAgIH0sXG4gICAge1xuICAgICAgbm9tZTogJ0RlZXBTZWVrJyxcbiAgICAgIHVybDogJ2h0dHBzOi8vYXBpLmRlZXBzZWVrLmNvbS92MS9jaGF0L2NvbXBsZXRpb25zJyxcbiAgICAgIGFwaUtleTogJycsXG4gICAgICBtb2RlbG9QYWRyYW86ICdkZWVwc2Vlay1jaGF0JyxcbiAgICAgIGF0aXZvOiBmYWxzZVxuICAgIH1cbiAgXSk7XG4gIGNvbnN0IFttZW1vcmllcywgc2V0TWVtb3JpZXNdID0gdXNlU3RhdGU8TWVtb3J5W10+KFtdKTtcbiAgY29uc3QgW21lbW9yeUNhdGVnb3JpZXMsIHNldE1lbW9yeUNhdGVnb3JpZXNdID0gdXNlU3RhdGU8TWVtb3J5Q2F0ZWdvcnlbXT4oW10pO1xuICBjb25zdCBbY2hhdHMsIHNldENoYXRzXSA9IHVzZVN0YXRlPENoYXRbXT4oW10pO1xuICBjb25zdCBbc2hvd0FkZE1lbW9yeSwgc2V0U2hvd0FkZE1lbW9yeV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93QWRkQ2F0ZWdvcnksIHNldFNob3dBZGRDYXRlZ29yeV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtuZXdNZW1vcnksIHNldE5ld01lbW9yeV0gPSB1c2VTdGF0ZTxNZW1vcnk+KHtcbiAgICB0aXR1bG86ICcnLFxuICAgIGNvbnRldWRvOiAnJyxcbiAgICBjb3I6ICcjM0I4MkY2JyxcbiAgICBjYXRlZ29yaWE6IG51bGwsXG4gICAgY2hhdElkOiBudWxsLFxuICAgIGdsb2JhbDogdHJ1ZVxuICB9KTtcbiAgY29uc3QgW25ld0NhdGVnb3J5LCBzZXROZXdDYXRlZ29yeV0gPSB1c2VTdGF0ZTxNZW1vcnlDYXRlZ29yeT4oe1xuICAgIG5vbWU6ICcnLFxuICAgIGRlc2NyaWNhbzogJycsXG4gICAgY29yOiAnIzNCODJGNidcbiAgfSk7XG4gIGNvbnN0IFtzaG93QWRkRW5kcG9pbnQsIHNldFNob3dBZGRFbmRwb2ludF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtuZXdFbmRwb2ludCwgc2V0TmV3RW5kcG9pbnRdID0gdXNlU3RhdGU8QUlFbmRwb2ludD4oe1xuICAgIG5vbWU6ICcnLFxuICAgIHVybDogJycsXG4gICAgYXBpS2V5OiAnJyxcbiAgICBtb2RlbG9QYWRyYW86ICcnLFxuICAgIGF0aXZvOiBmYWxzZVxuICB9KTtcbiAgY29uc3QgW2VkaXRpbmdFbmRwb2ludCwgc2V0RWRpdGluZ0VuZHBvaW50XSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbZWRpdEVuZHBvaW50RGF0YSwgc2V0RWRpdEVuZHBvaW50RGF0YV0gPSB1c2VTdGF0ZTxBSUVuZHBvaW50Pih7XG4gICAgbm9tZTogJycsXG4gICAgdXJsOiAnJyxcbiAgICBhcGlLZXk6ICcnLFxuICAgIG1vZGVsb1BhZHJhbzogJycsXG4gICAgYXRpdm86IGZhbHNlXG4gIH0pO1xuXG4gIC8vIENhcnJlZ2FyIGNvbmZpZ3VyYcOnw7VlcyBkbyBGaXJlc3RvcmVcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBsb2FkQ29uZmlndXJhdGlvbnMgPSBhc3luYyAoKSA9PiB7XG4gICAgICBpZiAoIXVzZXJEYXRhLnVzZXJuYW1lKSByZXR1cm47XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdDYXJyZWdhbmRvIGNvbmZpZ3VyYcOnw7VlcyBwYXJhOicsIHVzZXJEYXRhLnVzZXJuYW1lKTtcbiAgICAgICAgY29uc3QgY29uZmlnRG9jID0gYXdhaXQgZ2V0RG9jKGRvYyhkYiwgJ3VzdWFyaW9zJywgdXNlckRhdGEudXNlcm5hbWUsICdjb25maWd1cmFjb2VzJywgJ3NldHRpbmdzJykpO1xuXG4gICAgICAgIGlmIChjb25maWdEb2MuZXhpc3RzKCkpIHtcbiAgICAgICAgICBjb25zdCBjb25maWcgPSBjb25maWdEb2MuZGF0YSgpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdDb25maWd1cmHDp8O1ZXMgY2FycmVnYWRhczonLCBjb25maWcpO1xuXG4gICAgICAgICAgaWYgKGNvbmZpZy5hcGFyZW5jaWEpIHtcbiAgICAgICAgICAgIHNldEFwcGVhcmFuY2VTZXR0aW5ncyhjb25maWcuYXBhcmVuY2lhKTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBpZiAoY29uZmlnLmVuZHBvaW50cykge1xuICAgICAgICAgICAgY29uc3QgZW5kcG9pbnRzQXJyYXkgPSBPYmplY3QudmFsdWVzKGNvbmZpZy5lbmRwb2ludHMpIGFzIEFJRW5kcG9pbnRbXTtcbiAgICAgICAgICAgIHNldEFpRW5kcG9pbnRzKGVuZHBvaW50c0FycmF5KTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8gTWFudGVyIGVuZHBvaW50cyBwYWRyw6NvIHNlIG7Do28gaG91dmVyIGNvbmZpZ3VyYcOnw6NvIHNhbHZhXG4gICAgICAgICAgICBzZXRBaUVuZHBvaW50cyhbXG4gICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICBub21lOiAnT3BlblJvdXRlcicsXG4gICAgICAgICAgICAgICAgdXJsOiAnaHR0cHM6Ly9vcGVucm91dGVyLmFpL2FwaS92MS9jaGF0L2NvbXBsZXRpb25zJyxcbiAgICAgICAgICAgICAgICBhcGlLZXk6ICcnLFxuICAgICAgICAgICAgICAgIG1vZGVsb1BhZHJhbzogJ21ldGEtbGxhbWEvbGxhbWEtMy4xLThiLWluc3RydWN0OmZyZWUnLFxuICAgICAgICAgICAgICAgIGF0aXZvOiBmYWxzZVxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgbm9tZTogJ0RlZXBTZWVrJyxcbiAgICAgICAgICAgICAgICB1cmw6ICdodHRwczovL2FwaS5kZWVwc2Vlay5jb20vdjEvY2hhdC9jb21wbGV0aW9ucycsXG4gICAgICAgICAgICAgICAgYXBpS2V5OiAnJyxcbiAgICAgICAgICAgICAgICBtb2RlbG9QYWRyYW86ICdkZWVwc2Vlay1jaGF0JyxcbiAgICAgICAgICAgICAgICBhdGl2bzogZmFsc2VcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgXSk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgaWYgKGNvbmZpZy5tZW1vcmlhcykge1xuICAgICAgICAgICAgY29uc3QgbWVtb3JpYXNBcnJheSA9IE9iamVjdC52YWx1ZXMoY29uZmlnLm1lbW9yaWFzKSBhcyBNZW1vcnlbXTtcbiAgICAgICAgICAgIHNldE1lbW9yaWVzKG1lbW9yaWFzQXJyYXkpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGlmIChjb25maWcuY2F0ZWdvcmlhcykge1xuICAgICAgICAgICAgY29uc3QgY2F0ZWdvcmlhc0FycmF5ID0gT2JqZWN0LnZhbHVlcyhjb25maWcuY2F0ZWdvcmlhcykgYXMgTWVtb3J5Q2F0ZWdvcnlbXTtcbiAgICAgICAgICAgIHNldE1lbW9yeUNhdGVnb3JpZXMoY2F0ZWdvcmlhc0FycmF5KTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ05lbmh1bWEgY29uZmlndXJhw6fDo28gZW5jb250cmFkYSwgdXNhbmRvIHBhZHLDtWVzJyk7XG4gICAgICAgICAgLy8gQ29uZmlndXJhw6fDtWVzIHBhZHLDo28gc2UgbsOjbyBleGlzdGlyIGRvY3VtZW50b1xuICAgICAgICAgIHNldEFwcGVhcmFuY2VTZXR0aW5ncyh7XG4gICAgICAgICAgICBmb250ZTogJ0ludGVyJyxcbiAgICAgICAgICAgIHRhbWFuaG9Gb250ZTogMTQsXG4gICAgICAgICAgICBwYWxhdnJhc1BvclNlc3NhbzogNTAwMFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGNhcnJlZ2FyIGNvbmZpZ3VyYcOnw7VlczonLCBlcnJvcik7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGlmIChpc09wZW4gJiYgdXNlckRhdGEudXNlcm5hbWUpIHtcbiAgICAgIC8vIFJlc2V0IGRvIGVzdGFkbyBkbyBmb3JtdWzDoXJpbyBnZXJhbCBxdWFuZG8gYWJyaXIgbyBtb2RhbFxuICAgICAgc2V0R2VuZXJhbERhdGEoe1xuICAgICAgICB1c2VybmFtZTogdXNlckRhdGEudXNlcm5hbWUsXG4gICAgICAgIHByb2ZpbGVJbWFnZTogdXNlckRhdGEucHJvZmlsZUltYWdlIHx8ICcnLFxuICAgICAgICBjdXJyZW50UGFzc3dvcmQ6ICcnLFxuICAgICAgICBuZXdQYXNzd29yZDogJycsXG4gICAgICAgIGNvbmZpcm1QYXNzd29yZDogJydcbiAgICAgIH0pO1xuXG4gICAgICBsb2FkQ29uZmlndXJhdGlvbnMoKTtcbiAgICAgIGxvYWRDaGF0cygpO1xuICAgIH1cbiAgfSwgW2lzT3BlbiwgdXNlckRhdGEudXNlcm5hbWUsIHVzZXJEYXRhLnByb2ZpbGVJbWFnZV0pO1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgY2FycmVnYXIgY2hhdHMgZG8gdXN1w6FyaW9cbiAgY29uc3QgbG9hZENoYXRzID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghdXNlckRhdGE/LnVzZXJuYW1lKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgY2hhdHNSZWYgPSBjb2xsZWN0aW9uKGRiLCAndXN1YXJpb3MnLCB1c2VyRGF0YS51c2VybmFtZSwgJ2NvbnZlcnNhcycpO1xuICAgICAgY29uc3QgY2hhdHNRdWVyeSA9IHF1ZXJ5KGNoYXRzUmVmLCBvcmRlckJ5KCdsYXN0VXBkYXRlZEF0JywgJ2Rlc2MnKSk7XG4gICAgICBjb25zdCBjaGF0c1NuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhjaGF0c1F1ZXJ5KTtcblxuICAgICAgY29uc3QgbG9hZGVkQ2hhdHM6IENoYXRbXSA9IFtdO1xuICAgICAgY2hhdHNTbmFwc2hvdC5mb3JFYWNoKChkb2MpID0+IHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGRvYy5kYXRhKCk7XG4gICAgICAgIGxvYWRlZENoYXRzLnB1c2goe1xuICAgICAgICAgIGlkOiBkb2MuaWQsXG4gICAgICAgICAgbmFtZTogZGF0YS5uYW1lIHx8ICdDb252ZXJzYSBzZW0gbm9tZScsXG4gICAgICAgICAgbGFzdE1lc3NhZ2U6IGRhdGEudWx0aW1hTWVuc2FnZW0gfHwgJ05lbmh1bWEgbWVuc2FnZW0gYWluZGEnLFxuICAgICAgICAgIGxhc3RNZXNzYWdlVGltZTogZGF0YS51bHRpbWFNZW5zYWdlbUVtIHx8IGRhdGEuY3JlYXRlZEF0LFxuICAgICAgICB9KTtcbiAgICAgIH0pO1xuXG4gICAgICBzZXRDaGF0cyhsb2FkZWRDaGF0cyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gY2FycmVnYXIgY2hhdHM6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBhdXhpbGlhciBwYXJhIGRlbGV0YXIgdG9kb3Mgb3MgZGFkb3MgZG8gU3RvcmFnZSBkZSB1bSB1c3XDoXJpb1xuICBjb25zdCBkZWxldGVVc2VyU3RvcmFnZURhdGEgPSBhc3luYyAodXNlcm5hbWU6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnSW5pY2lhbmRvIGV4Y2x1c8OjbyBkZSBkYWRvcyBkbyBTdG9yYWdlIHBhcmE6JywgdXNlcm5hbWUpO1xuXG4gICAgICAvLyBEZWxldGFyIHRvZGEgYSBwYXN0YSBkbyB1c3XDoXJpbyBubyBTdG9yYWdlXG4gICAgICBjb25zdCB1c2VyU3RvcmFnZVJlZiA9IHJlZihzdG9yYWdlLCBgdXN1YXJpb3MvJHt1c2VybmFtZX1gKTtcbiAgICAgIGNvbnN0IHVzZXJTdG9yYWdlTGlzdCA9IGF3YWl0IGxpc3RBbGwodXNlclN0b3JhZ2VSZWYpO1xuXG4gICAgICAvLyBGdW7Dp8OjbyByZWN1cnNpdmEgcGFyYSBkZWxldGFyIHBhc3RhcyBlIGFycXVpdm9zXG4gICAgICBjb25zdCBkZWxldGVSZWN1cnNpdmVseSA9IGFzeW5jIChmb2xkZXJSZWY6IGFueSkgPT4ge1xuICAgICAgICBjb25zdCBmb2xkZXJMaXN0ID0gYXdhaXQgbGlzdEFsbChmb2xkZXJSZWYpO1xuXG4gICAgICAgIC8vIERlbGV0YXIgdG9kb3Mgb3MgYXJxdWl2b3MgbmEgcGFzdGEgYXR1YWxcbiAgICAgICAgY29uc3QgZmlsZURlbGV0ZVByb21pc2VzID0gZm9sZGVyTGlzdC5pdGVtcy5tYXAoaXRlbSA9PiBkZWxldGVPYmplY3QoaXRlbSkpO1xuICAgICAgICBhd2FpdCBQcm9taXNlLmFsbChmaWxlRGVsZXRlUHJvbWlzZXMpO1xuXG4gICAgICAgIC8vIERlbGV0YXIgcmVjdXJzaXZhbWVudGUgdG9kYXMgYXMgc3VicGFzdGFzXG4gICAgICAgIGNvbnN0IGZvbGRlckRlbGV0ZVByb21pc2VzID0gZm9sZGVyTGlzdC5wcmVmaXhlcy5tYXAocHJlZml4ID0+IGRlbGV0ZVJlY3Vyc2l2ZWx5KHByZWZpeCkpO1xuICAgICAgICBhd2FpdCBQcm9taXNlLmFsbChmb2xkZXJEZWxldGVQcm9taXNlcyk7XG4gICAgICB9O1xuXG4gICAgICBhd2FpdCBkZWxldGVSZWN1cnNpdmVseSh1c2VyU3RvcmFnZVJlZik7XG4gICAgICBjb25zb2xlLmxvZygnVG9kb3Mgb3MgZGFkb3MgZG8gU3RvcmFnZSBkZWxldGFkb3MgcGFyYTonLCB1c2VybmFtZSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5sb2coJ0Vycm8gYW8gZGVsZXRhciBkYWRvcyBkbyBTdG9yYWdlIG91IHBhc3RhIG7Do28gZW5jb250cmFkYTonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIGF1eGlsaWFyIHBhcmEgZGVsZXRhciByZWN1cnNpdmFtZW50ZSB0b2RvcyBvcyBkb2N1bWVudG9zIGRlIHVtIHVzdcOhcmlvXG4gIGNvbnN0IGRlbGV0ZVVzZXJEb2N1bWVudHMgPSBhc3luYyAodXNlcm5hbWU6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnSW5pY2lhbmRvIGV4Y2x1c8OjbyBkZSBkb2N1bWVudG9zIHBhcmE6JywgdXNlcm5hbWUpO1xuXG4gICAgICAvLyBEZWxldGFyIHN1YmNvbGXDp8OjbyBkZSBjb25maWd1cmHDp8O1ZXNcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGNvbmZpZ0RvYyA9IGRvYyhkYiwgJ3VzdWFyaW9zJywgdXNlcm5hbWUsICdjb25maWd1cmFjb2VzJywgJ3NldHRpbmdzJyk7XG4gICAgICAgIGNvbnN0IGNvbmZpZ1NuYXBzaG90ID0gYXdhaXQgZ2V0RG9jKGNvbmZpZ0RvYyk7XG4gICAgICAgIGlmIChjb25maWdTbmFwc2hvdC5leGlzdHMoKSkge1xuICAgICAgICAgIGF3YWl0IGRlbGV0ZURvYyhjb25maWdEb2MpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdDb25maWd1cmHDp8O1ZXMgZGVsZXRhZGFzJyk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdFcnJvIGFvIGRlbGV0YXIgY29uZmlndXJhw6fDtWVzOicsIGVycm9yKTtcbiAgICAgIH1cblxuICAgICAgLy8gRGVsZXRhciBvdXRyYXMgc3ViY29sZcOnw7VlcyBzZSBleGlzdGlyZW0gKGNoYXRzLCBoaXN0w7NyaWNvLCBldGMuKVxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgY2hhdHNDb2xsZWN0aW9uID0gY29sbGVjdGlvbihkYiwgJ3VzdWFyaW9zJywgdXNlcm5hbWUsICdjaGF0cycpO1xuICAgICAgICBjb25zdCBjaGF0c1NuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhjaGF0c0NvbGxlY3Rpb24pO1xuICAgICAgICBjb25zdCBkZWxldGVQcm9taXNlcyA9IGNoYXRzU25hcHNob3QuZG9jcy5tYXAoZG9jID0+IGRlbGV0ZURvYyhkb2MucmVmKSk7XG4gICAgICAgIGF3YWl0IFByb21pc2UuYWxsKGRlbGV0ZVByb21pc2VzKTtcbiAgICAgICAgY29uc29sZS5sb2coJ0NoYXRzIGRlbGV0YWRvcycpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0Vycm8gYW8gZGVsZXRhciBjaGF0czonLCBlcnJvcik7XG4gICAgICB9XG5cbiAgICAgIC8vIERlbGV0YXIgZG9jdW1lbnRvIHByaW5jaXBhbCBkbyB1c3XDoXJpb1xuICAgICAgY29uc3QgdXNlckRvY1JlZiA9IGRvYyhkYiwgJ3VzdWFyaW9zJywgdXNlcm5hbWUpO1xuICAgICAgYXdhaXQgZGVsZXRlRG9jKHVzZXJEb2NSZWYpO1xuICAgICAgY29uc29sZS5sb2coJ0RvY3VtZW50byBwcmluY2lwYWwgZG8gdXN1w6FyaW8gZGVsZXRhZG8nKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGRlbGV0YXIgZG9jdW1lbnRvcyBkbyB1c3XDoXJpbzonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH07XG5cbiAgLy8gQXR1YWxpemFyIHVzZXJuYW1lIG5vIGRvY3VtZW50byBwcmluY2lwYWxcbiAgY29uc3QgdXBkYXRlVXNlcm5hbWUgPSBhc3luYyAobmV3VXNlcm5hbWU6IHN0cmluZywgc2hvd1N1Y2Nlc3NBbGVydDogYm9vbGVhbiA9IHRydWUpID0+IHtcbiAgICBpZiAoIXVzZXJEYXRhLnVzZXJuYW1lIHx8ICFuZXdVc2VybmFtZSB8fCBuZXdVc2VybmFtZSA9PT0gdXNlckRhdGEudXNlcm5hbWUpIHtcbiAgICAgIGlmIChzaG93U3VjY2Vzc0FsZXJ0KSBhbGVydCgnTm9tZSBkZSB1c3XDoXJpbyBpbnbDoWxpZG8gb3UgaWd1YWwgYW8gYXR1YWwuJyk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgaWYgKG5ld1VzZXJuYW1lLmxlbmd0aCA8IDMpIHtcbiAgICAgIGlmIChzaG93U3VjY2Vzc0FsZXJ0KSBhbGVydCgnTm9tZSBkZSB1c3XDoXJpbyBkZXZlIHRlciBwZWxvIG1lbm9zIDMgY2FyYWN0ZXJlcy4nKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICBsZXQgbmV3VXNlckNyZWF0ZWQgPSBmYWxzZTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnQXR1YWxpemFuZG8gdXNlcm5hbWUgZGUnLCB1c2VyRGF0YS51c2VybmFtZSwgJ3BhcmEnLCBuZXdVc2VybmFtZSk7XG5cbiAgICAgIC8vIFZlcmlmaWNhciBzZSBvIG5vdm8gdXNlcm5hbWUgasOhIGV4aXN0ZVxuICAgICAgY29uc3QgbmV3VXNlckRvYyA9IGF3YWl0IGdldERvYyhkb2MoZGIsICd1c3VhcmlvcycsIG5ld1VzZXJuYW1lKSk7XG4gICAgICBpZiAobmV3VXNlckRvYy5leGlzdHMoKSkge1xuICAgICAgICBpZiAoc2hvd1N1Y2Nlc3NBbGVydCkgYWxlcnQoJ0VzdGUgbm9tZSBkZSB1c3XDoXJpbyBqw6EgZXN0w6EgZW0gdXNvLiBFc2NvbGhhIG91dHJvLicpO1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIC8vIEJ1c2NhciBvIGRvY3VtZW50byBhdHVhbCBwZWxvIHVzZXJuYW1lIGFudGlnb1xuICAgICAgY29uc3Qgb2xkVXNlckRvY1JlZiA9IGRvYyhkYiwgJ3VzdWFyaW9zJywgdXNlckRhdGEudXNlcm5hbWUpO1xuICAgICAgY29uc3Qgb2xkVXNlckRvYyA9IGF3YWl0IGdldERvYyhvbGRVc2VyRG9jUmVmKTtcblxuICAgICAgaWYgKCFvbGRVc2VyRG9jLmV4aXN0cygpKSB7XG4gICAgICAgIGlmIChzaG93U3VjY2Vzc0FsZXJ0KSBhbGVydCgnVXN1w6FyaW8gbsOjbyBlbmNvbnRyYWRvLicpO1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGN1cnJlbnREYXRhID0gb2xkVXNlckRvYy5kYXRhKCk7XG5cbiAgICAgIC8vIENyaWFyIG5vdm8gZG9jdW1lbnRvIGNvbSBvIG5vdm8gdXNlcm5hbWVcbiAgICAgIGF3YWl0IHNldERvYyhkb2MoZGIsICd1c3VhcmlvcycsIG5ld1VzZXJuYW1lKSwge1xuICAgICAgICAuLi5jdXJyZW50RGF0YSxcbiAgICAgICAgdXNlcm5hbWU6IG5ld1VzZXJuYW1lLFxuICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSk7XG5cbiAgICAgIG5ld1VzZXJDcmVhdGVkID0gdHJ1ZTtcbiAgICAgIGNvbnNvbGUubG9nKCdOb3ZvIGRvY3VtZW50byBjcmlhZG8gcGFyYTonLCBuZXdVc2VybmFtZSk7XG5cbiAgICAgICAgLy8gQ29waWFyIHRvZGFzIGFzIGNvbmZpZ3VyYcOnw7VlcyBlIHN1YmNvbGXDp8O1ZXNcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAvLyBDb3BpYXIgY29uZmlndXJhw6fDtWVzIHByaW5jaXBhaXNcbiAgICAgICAgICBjb25zdCBjb25maWdEb2MgPSBhd2FpdCBnZXREb2MoZG9jKGRiLCAndXN1YXJpb3MnLCB1c2VyRGF0YS51c2VybmFtZSwgJ2NvbmZpZ3VyYWNvZXMnLCAnc2V0dGluZ3MnKSk7XG4gICAgICAgICAgaWYgKGNvbmZpZ0RvYy5leGlzdHMoKSkge1xuICAgICAgICAgICAgYXdhaXQgc2V0RG9jKGRvYyhkYiwgJ3VzdWFyaW9zJywgbmV3VXNlcm5hbWUsICdjb25maWd1cmFjb2VzJywgJ3NldHRpbmdzJyksIGNvbmZpZ0RvYy5kYXRhKCkpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ0NvbmZpZ3VyYcOnw7VlcyBjb3BpYWRhcyBwYXJhIG5vdm8gdXNlcm5hbWUnKTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBDb3BpYXIgY2hhdHMgc2UgZXhpc3RpcmVtXG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IGNoYXRzQ29sbGVjdGlvbiA9IGNvbGxlY3Rpb24oZGIsICd1c3VhcmlvcycsIHVzZXJEYXRhLnVzZXJuYW1lLCAnY2hhdHMnKTtcbiAgICAgICAgICAgIGNvbnN0IGNoYXRzU25hcHNob3QgPSBhd2FpdCBnZXREb2NzKGNoYXRzQ29sbGVjdGlvbik7XG5cbiAgICAgICAgICAgIGZvciAoY29uc3QgY2hhdERvYyBvZiBjaGF0c1NuYXBzaG90LmRvY3MpIHtcbiAgICAgICAgICAgICAgY29uc3QgY2hhdERhdGEgPSBjaGF0RG9jLmRhdGEoKTtcbiAgICAgICAgICAgICAgYXdhaXQgc2V0RG9jKGRvYyhkYiwgJ3VzdWFyaW9zJywgbmV3VXNlcm5hbWUsICdjaGF0cycsIGNoYXREb2MuaWQpLCBjaGF0RGF0YSk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmIChjaGF0c1NuYXBzaG90LmRvY3MubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgJHtjaGF0c1NuYXBzaG90LmRvY3MubGVuZ3RofSBjaGF0cyBjb3BpYWRvcyBwYXJhIG5vdm8gdXNlcm5hbWVgKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGNhdGNoIChjaGF0c0Vycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygnRXJybyBhbyBjb3BpYXIgY2hhdHM6JywgY2hhdHNFcnJvcik7XG4gICAgICAgICAgfVxuXG4gICAgICAgIH0gY2F0Y2ggKGNvbmZpZ0Vycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0Vycm8gYW8gY29waWFyIGRhZG9zOicsIGNvbmZpZ0Vycm9yKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIERlbGV0YXIgdG9kb3Mgb3MgZG9jdW1lbnRvcyBkbyB1c3XDoXJpbyBhbnRpZ29cbiAgICAgICAgYXdhaXQgZGVsZXRlVXNlckRvY3VtZW50cyh1c2VyRGF0YS51c2VybmFtZSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdUb2RvcyBvcyBkb2N1bWVudG9zIGRvIHVzdcOhcmlvIGFudGlnbyBmb3JhbSBkZWxldGFkb3MnKTtcblxuICAgICAgICAvLyBBdHVhbGl6YXIgZXN0YWRvIGxvY2FsXG4gICAgICAgIG9uVXNlckRhdGFVcGRhdGUoe1xuICAgICAgICAgIC4uLnVzZXJEYXRhLFxuICAgICAgICAgIHVzZXJuYW1lOiBuZXdVc2VybmFtZVxuICAgICAgICB9KTtcblxuICAgICAgICBpZiAoc2hvd1N1Y2Nlc3NBbGVydCkgYWxlcnQoJ05vbWUgZGUgdXN1w6FyaW8gYXR1YWxpemFkbyBjb20gc3VjZXNzbyEnKTtcbiAgICAgICAgcmV0dXJuIHRydWU7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBhdHVhbGl6YXIgdXNlcm5hbWU6JywgZXJyb3IpO1xuXG4gICAgICAvLyBTZSBob3V2ZSBlcnJvIGUgbyBub3ZvIHVzdcOhcmlvIGZvaSBjcmlhZG8sIHRlbnRhciBmYXplciByb2xsYmFja1xuICAgICAgaWYgKG5ld1VzZXJDcmVhdGVkKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgYXdhaXQgZGVsZXRlRG9jKGRvYyhkYiwgJ3VzdWFyaW9zJywgbmV3VXNlcm5hbWUpKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygnUm9sbGJhY2sgcmVhbGl6YWRvIC0gbm92byB1c3XDoXJpbyBkZWxldGFkbycpO1xuICAgICAgICB9IGNhdGNoIChyb2xsYmFja0Vycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJybyBubyByb2xsYmFjazonLCByb2xsYmFja0Vycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAoc2hvd1N1Y2Nlc3NBbGVydCkgYWxlcnQoYEVycm8gYW8gYXR1YWxpemFyIG5vbWUgZGUgdXN1w6FyaW86ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnRXJybyBkZXNjb25oZWNpZG8nfWApO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfTtcblxuICAvLyBTYWx2YXIgY29uZmlndXJhw6fDtWVzIG5vIEZpcmVzdG9yZVxuICBjb25zdCBzYXZlQ29uZmlndXJhdGlvbnMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyRGF0YS51c2VybmFtZSkge1xuICAgICAgYWxlcnQoJ0Vycm86IHVzdcOhcmlvIG7Do28gaWRlbnRpZmljYWRvJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG5cbiAgICAgIC8vIFZlcmlmaWNhciBzZSBvIHVzZXJuYW1lIGZvaSBhbHRlcmFkbyBlIGF0dWFsaXrDoS1sbyBwcmltZWlyb1xuICAgICAgaWYgKGdlbmVyYWxEYXRhLnVzZXJuYW1lICE9PSB1c2VyRGF0YS51c2VybmFtZSkge1xuICAgICAgICBjb25zdCB1c2VybmFtZVVwZGF0ZWQgPSBhd2FpdCB1cGRhdGVVc2VybmFtZShnZW5lcmFsRGF0YS51c2VybmFtZSwgZmFsc2UpO1xuICAgICAgICBpZiAoIXVzZXJuYW1lVXBkYXRlZCkge1xuICAgICAgICAgIC8vIFNlIGZhbGhvdSBhbyBhdHVhbGl6YXIgbyB1c2VybmFtZSwgaW50ZXJyb21wZXIgbyBwcm9jZXNzb1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBEZXRlcm1pbmFyIHF1YWwgdXNlcm5hbWUgdXNhciAobyBub3ZvIHNlIGZvaSBhbHRlcmFkbylcbiAgICAgIGNvbnN0IGN1cnJlbnRVc2VybmFtZSA9IGdlbmVyYWxEYXRhLnVzZXJuYW1lICE9PSB1c2VyRGF0YS51c2VybmFtZSA/IGdlbmVyYWxEYXRhLnVzZXJuYW1lIDogdXNlckRhdGEudXNlcm5hbWU7XG5cbiAgICAgIGNvbnN0IGNvbmZpZ0RhdGEgPSB7XG4gICAgICAgIGFwYXJlbmNpYToge1xuICAgICAgICAgIGZvbnRlOiBhcHBlYXJhbmNlU2V0dGluZ3MuZm9udGUsXG4gICAgICAgICAgdGFtYW5ob0ZvbnRlOiBhcHBlYXJhbmNlU2V0dGluZ3MudGFtYW5ob0ZvbnRlLFxuICAgICAgICAgIHBhbGF2cmFzUG9yU2Vzc2FvOiBhcHBlYXJhbmNlU2V0dGluZ3MucGFsYXZyYXNQb3JTZXNzYW9cbiAgICAgICAgfSxcbiAgICAgICAgZW5kcG9pbnRzOiB7fSBhcyBSZWNvcmQ8c3RyaW5nLCBBSUVuZHBvaW50PixcbiAgICAgICAgbWVtb3JpYXM6IHt9IGFzIFJlY29yZDxzdHJpbmcsIE1lbW9yeT4sXG4gICAgICAgIGNhdGVnb3JpYXM6IHt9IGFzIFJlY29yZDxzdHJpbmcsIE1lbW9yeUNhdGVnb3J5PixcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH07XG5cbiAgICAgIC8vIENvbnZlcnRlciBhcnJheXMgcGFyYSBvYmpldG9zXG4gICAgICBhaUVuZHBvaW50cy5mb3JFYWNoKChlbmRwb2ludCwgaW5kZXgpID0+IHtcbiAgICAgICAgY29uZmlnRGF0YS5lbmRwb2ludHNbZW5kcG9pbnQubm9tZSB8fCBgZW5kcG9pbnRfJHtpbmRleH1gXSA9IGVuZHBvaW50O1xuICAgICAgfSk7XG5cbiAgICAgIG1lbW9yaWVzLmZvckVhY2goKG1lbW9yeSwgaW5kZXgpID0+IHtcbiAgICAgICAgY29uZmlnRGF0YS5tZW1vcmlhc1tgbWVtb3JpYV8ke2luZGV4fWBdID0gbWVtb3J5O1xuICAgICAgfSk7XG5cbiAgICAgIG1lbW9yeUNhdGVnb3JpZXMuZm9yRWFjaCgoY2F0ZWdvcnksIGluZGV4KSA9PiB7XG4gICAgICAgIGNvbmZpZ0RhdGEuY2F0ZWdvcmlhc1tjYXRlZ29yeS5ub21lIHx8IGBjYXRlZ29yaWFfJHtpbmRleH1gXSA9IGNhdGVnb3J5O1xuICAgICAgfSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCdTYWx2YW5kbyBjb25maWd1cmHDp8O1ZXMgcGFyYTonLCBjdXJyZW50VXNlcm5hbWUpO1xuICAgICAgY29uc29sZS5sb2coJ0RhZG9zIGEgc2VyZW0gc2Fsdm9zOicsIGNvbmZpZ0RhdGEpO1xuXG4gICAgICAvLyBVc2FyIHNldERvYyBjb20gbWVyZ2UgcGFyYSBuw6NvIHNvYnJlc2NyZXZlciBvdXRyb3MgZGFkb3NcbiAgICAgIGNvbnN0IGRvY1JlZiA9IGRvYyhkYiwgJ3VzdWFyaW9zJywgY3VycmVudFVzZXJuYW1lLCAnY29uZmlndXJhY29lcycsICdzZXR0aW5ncycpO1xuICAgICAgYXdhaXQgc2V0RG9jKGRvY1JlZiwgY29uZmlnRGF0YSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCdDb25maWd1cmHDp8O1ZXMgc2FsdmFzIGNvbSBzdWNlc3NvIG5vIEZpcmVzdG9yZScpO1xuXG4gICAgICAvLyBBdHVhbGl6YXIgYXMgY29uZmlndXJhw6fDtWVzIGRlIGFwYXLDqm5jaWEgbm8gY29udGV4dG9cbiAgICAgIGF3YWl0IHJlZnJlc2hTZXR0aW5ncygpO1xuXG4gICAgICBhbGVydCgnQ29uZmlndXJhw6fDtWVzIHNhbHZhcyBjb20gc3VjZXNzbyEnKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIHNhbHZhciBjb25maWd1cmHDp8O1ZXM6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoYEVycm8gYW8gc2FsdmFyIGNvbmZpZ3VyYcOnw7VlczogJHtlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdFcnJvIGRlc2NvbmhlY2lkbyd9YCk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBpZiAoIWlzT3BlbikgcmV0dXJuIG51bGw7XG5cbiAgLy8gRnVuw6fDtWVzIHV0aWxpdMOhcmlhc1xuICBjb25zdCBoYW5kbGVQcm9maWxlSW1hZ2VVcGxvYWQgPSBhc3luYyAoZmlsZTogRmlsZSkgPT4ge1xuICAgIGlmICghdXNlcikgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCBpbWFnZVJlZiA9IHJlZihzdG9yYWdlLCBgdXN1YXJpb3MvJHt1c2VyRGF0YS51c2VybmFtZX0vcHJvZmlsZS5qcGdgKTtcbiAgICAgIGF3YWl0IHVwbG9hZEJ5dGVzKGltYWdlUmVmLCBmaWxlKTtcbiAgICAgIGNvbnN0IGRvd25sb2FkVVJMID0gYXdhaXQgZ2V0RG93bmxvYWRVUkwoaW1hZ2VSZWYpO1xuXG4gICAgICBzZXRHZW5lcmFsRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHByb2ZpbGVJbWFnZTogZG93bmxvYWRVUkwgfSkpO1xuXG4gICAgICAvLyBBdHVhbGl6YXIgbm8gRmlyZXN0b3JlXG4gICAgICBjb25zdCB1c2VyRG9jUmVmID0gZG9jKGRiLCAndXN1YXJpb3MnLCB1c2VyRGF0YS51c2VybmFtZSk7XG4gICAgICBhd2FpdCB1cGRhdGVEb2ModXNlckRvY1JlZiwgeyBwcm9maWxlSW1hZ2U6IGRvd25sb2FkVVJMIH0pO1xuXG4gICAgICBvblVzZXJEYXRhVXBkYXRlKHsgLi4udXNlckRhdGEsIHByb2ZpbGVJbWFnZTogZG93bmxvYWRVUkwgfSk7XG4gICAgICBhbGVydCgnRm90byBkZSBwZXJmaWwgYXR1YWxpemFkYSBjb20gc3VjZXNzbyEnKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBmYXplciB1cGxvYWQgZGEgaW1hZ2VtOicsIGVycm9yKTtcbiAgICAgIGFsZXJ0KCdFcnJvIGFvIGF0dWFsaXphciBmb3RvIGRlIHBlcmZpbC4nKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVBhc3N3b3JkQ2hhbmdlID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghdXNlciB8fCAhZ2VuZXJhbERhdGEuY3VycmVudFBhc3N3b3JkIHx8ICFnZW5lcmFsRGF0YS5uZXdQYXNzd29yZCkge1xuICAgICAgYWxlcnQoJ1ByZWVuY2hhIHRvZG9zIG9zIGNhbXBvcyBkZSBzZW5oYS4nKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAoZ2VuZXJhbERhdGEubmV3UGFzc3dvcmQgIT09IGdlbmVyYWxEYXRhLmNvbmZpcm1QYXNzd29yZCkge1xuICAgICAgYWxlcnQoJ0FzIHNlbmhhcyBuw6NvIGNvaW5jaWRlbS4nKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnN0IGNyZWRlbnRpYWwgPSBFbWFpbEF1dGhQcm92aWRlci5jcmVkZW50aWFsKHVzZXIuZW1haWwhLCBnZW5lcmFsRGF0YS5jdXJyZW50UGFzc3dvcmQpO1xuICAgICAgYXdhaXQgcmVhdXRoZW50aWNhdGVXaXRoQ3JlZGVudGlhbCh1c2VyLCBjcmVkZW50aWFsKTtcbiAgICAgIGF3YWl0IHVwZGF0ZVBhc3N3b3JkKHVzZXIsIGdlbmVyYWxEYXRhLm5ld1Bhc3N3b3JkKTtcblxuICAgICAgc2V0R2VuZXJhbERhdGEocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBjdXJyZW50UGFzc3dvcmQ6ICcnLFxuICAgICAgICBuZXdQYXNzd29yZDogJycsXG4gICAgICAgIGNvbmZpcm1QYXNzd29yZDogJydcbiAgICAgIH0pKTtcblxuICAgICAgYWxlcnQoJ1NlbmhhIGFsdGVyYWRhIGNvbSBzdWNlc3NvIScpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGFsdGVyYXIgc2VuaGE6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ0Vycm8gYW8gYWx0ZXJhciBzZW5oYS4gVmVyaWZpcXVlIGEgc2VuaGEgYXR1YWwuJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVMb2dvdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKGNvbmZpcm0oJ1RlbSBjZXJ0ZXphIHF1ZSBkZXNlamEgc2Fpcj8nKSkge1xuICAgICAgYXdhaXQgbG9nb3V0KCk7XG4gICAgICBvbkNsb3NlKCk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bsOnw7VlcyBwYXJhIGdlcmVuY2lhciBlbmRwb2ludHMgZGUgSUFcbiAgY29uc3QgaGFuZGxlQWRkRW5kcG9pbnQgPSAoKSA9PiB7XG4gICAgaWYgKCFuZXdFbmRwb2ludC5ub21lIHx8ICFuZXdFbmRwb2ludC51cmwgfHwgIW5ld0VuZHBvaW50LmFwaUtleSkge1xuICAgICAgYWxlcnQoJ1ByZWVuY2hhIHRvZG9zIG9zIGNhbXBvcyBvYnJpZ2F0w7NyaW9zLicpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHNldEFpRW5kcG9pbnRzKHByZXYgPT4gWy4uLnByZXYsIHsgLi4ubmV3RW5kcG9pbnQgfV0pO1xuICAgIHNldE5ld0VuZHBvaW50KHtcbiAgICAgIG5vbWU6ICcnLFxuICAgICAgdXJsOiAnJyxcbiAgICAgIGFwaUtleTogJycsXG4gICAgICBtb2RlbG9QYWRyYW86ICcnLFxuICAgICAgYXRpdm86IGZhbHNlXG4gICAgfSk7XG4gICAgc2V0U2hvd0FkZEVuZHBvaW50KGZhbHNlKTtcbiAgICBhbGVydCgnRW5kcG9pbnQgYWRpY2lvbmFkbyBjb20gc3VjZXNzbyEnKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVUb2dnbGVFbmRwb2ludCA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgc2V0QWlFbmRwb2ludHMocHJldiA9PiBwcmV2Lm1hcCgoZW5kcG9pbnQsIGkpID0+XG4gICAgICBpID09PSBpbmRleCA/IHsgLi4uZW5kcG9pbnQsIGF0aXZvOiAhZW5kcG9pbnQuYXRpdm8gfSA6IGVuZHBvaW50XG4gICAgKSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRGVsZXRlRW5kcG9pbnQgPSAoaW5kZXg6IG51bWJlcikgPT4ge1xuICAgIGlmIChjb25maXJtKCdUZW0gY2VydGV6YSBxdWUgZGVzZWphIGRlbGV0YXIgZXN0ZSBlbmRwb2ludD8nKSkge1xuICAgICAgc2V0QWlFbmRwb2ludHMocHJldiA9PiBwcmV2LmZpbHRlcigoXywgaSkgPT4gaSAhPT0gaW5kZXgpKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRWRpdEVuZHBvaW50ID0gKGluZGV4OiBudW1iZXIpID0+IHtcbiAgICBjb25zdCBlbmRwb2ludCA9IGFpRW5kcG9pbnRzW2luZGV4XTtcbiAgICBzZXRFZGl0RW5kcG9pbnREYXRhKHsgLi4uZW5kcG9pbnQgfSk7XG4gICAgc2V0RWRpdGluZ0VuZHBvaW50KGluZGV4KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTYXZlRWRpdEVuZHBvaW50ID0gKCkgPT4ge1xuICAgIGlmIChlZGl0aW5nRW5kcG9pbnQgPT09IG51bGwpIHJldHVybjtcblxuICAgIGlmICghZWRpdEVuZHBvaW50RGF0YS5hcGlLZXkgfHwgIWVkaXRFbmRwb2ludERhdGEubW9kZWxvUGFkcmFvKSB7XG4gICAgICBhbGVydCgnQVBJIEtleSBlIE1vZGVsbyBQYWRyw6NvIHPDo28gb2JyaWdhdMOzcmlvcy4nKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRBaUVuZHBvaW50cyhwcmV2ID0+IHByZXYubWFwKChlbmRwb2ludCwgaSkgPT5cbiAgICAgIGkgPT09IGVkaXRpbmdFbmRwb2ludCA/IHsgLi4uZWRpdEVuZHBvaW50RGF0YSB9IDogZW5kcG9pbnRcbiAgICApKTtcblxuICAgIHNldEVkaXRpbmdFbmRwb2ludChudWxsKTtcbiAgICBzZXRFZGl0RW5kcG9pbnREYXRhKHtcbiAgICAgIG5vbWU6ICcnLFxuICAgICAgdXJsOiAnJyxcbiAgICAgIGFwaUtleTogJycsXG4gICAgICBtb2RlbG9QYWRyYW86ICcnLFxuICAgICAgYXRpdm86IGZhbHNlXG4gICAgfSk7XG5cbiAgICBhbGVydCgnRW5kcG9pbnQgYXR1YWxpemFkbyBjb20gc3VjZXNzbyEnKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVDYW5jZWxFZGl0RW5kcG9pbnQgPSAoKSA9PiB7XG4gICAgc2V0RWRpdGluZ0VuZHBvaW50KG51bGwpO1xuICAgIHNldEVkaXRFbmRwb2ludERhdGEoe1xuICAgICAgbm9tZTogJycsXG4gICAgICB1cmw6ICcnLFxuICAgICAgYXBpS2V5OiAnJyxcbiAgICAgIG1vZGVsb1BhZHJhbzogJycsXG4gICAgICBhdGl2bzogZmFsc2VcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVUZXN0RW5kcG9pbnQgPSBhc3luYyAoZW5kcG9pbnQ6IEFJRW5kcG9pbnQpID0+IHtcbiAgICBpZiAoIWVuZHBvaW50LmFwaUtleSkge1xuICAgICAgYWxlcnQoJ0FQSSBLZXkgw6kgbmVjZXNzw6FyaWEgcGFyYSB0ZXN0YXIgbyBlbmRwb2ludC4nKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goZW5kcG9pbnQudXJsLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHtlbmRwb2ludC5hcGlLZXl9YFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgbW9kZWw6IGVuZHBvaW50Lm1vZGVsb1BhZHJhbyB8fCAnZ3B0LTMuNS10dXJibycsXG4gICAgICAgICAgbWVzc2FnZXM6IFt7IHJvbGU6ICd1c2VyJywgY29udGVudDogJ1Rlc3QgbWVzc2FnZScgfV0sXG4gICAgICAgICAgbWF4X3Rva2VuczogMTBcbiAgICAgICAgfSlcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgYWxlcnQoJ+KchSBFbmRwb2ludCB0ZXN0YWRvIGNvbSBzdWNlc3NvIScpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgYWxlcnQoJ+KdjCBFcnJvIGFvIHRlc3RhciBlbmRwb2ludC4gVmVyaWZpcXVlIGFzIGNvbmZpZ3VyYcOnw7Vlcy4nKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyB0ZXN0YXIgZW5kcG9pbnQ6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ+KdjCBFcnJvIGFvIGNvbmVjdGFyIGNvbSBvIGVuZHBvaW50LicpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRnVuw6fDtWVzIHBhcmEgZ2VyZW5jaWFyIG1lbcOzcmlhc1xuICBjb25zdCBoYW5kbGVBZGRDYXRlZ29yeSA9ICgpID0+IHtcbiAgICBpZiAoIW5ld0NhdGVnb3J5Lm5vbWUpIHtcbiAgICAgIGFsZXJ0KCdOb21lIGRhIGNhdGVnb3JpYSDDqSBvYnJpZ2F0w7NyaW8uJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0TWVtb3J5Q2F0ZWdvcmllcyhwcmV2ID0+IFsuLi5wcmV2LCB7IC4uLm5ld0NhdGVnb3J5IH1dKTtcbiAgICBzZXROZXdDYXRlZ29yeSh7XG4gICAgICBub21lOiAnJyxcbiAgICAgIGRlc2NyaWNhbzogJycsXG4gICAgICBjb3I6ICcjM0I4MkY2J1xuICAgIH0pO1xuICAgIHNldFNob3dBZGRDYXRlZ29yeShmYWxzZSk7XG4gICAgYWxlcnQoJ0NhdGVnb3JpYSBjcmlhZGEgY29tIHN1Y2Vzc28hJyk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQWRkTWVtb3J5ID0gKCkgPT4ge1xuICAgIGlmICghbmV3TWVtb3J5LnRpdHVsbyB8fCAhbmV3TWVtb3J5LmNvbnRldWRvKSB7XG4gICAgICBhbGVydCgnVMOtdHVsbyBlIGNvbnRlw7pkbyBzw6NvIG9icmlnYXTDs3Jpb3MuJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0TWVtb3JpZXMocHJldiA9PiBbLi4ucHJldiwgeyAuLi5uZXdNZW1vcnkgfV0pO1xuICAgIHNldE5ld01lbW9yeSh7XG4gICAgICB0aXR1bG86ICcnLFxuICAgICAgY29udGV1ZG86ICcnLFxuICAgICAgY29yOiAnIzNCODJGNicsXG4gICAgICBjYXRlZ29yaWE6IG51bGwsXG4gICAgICBjaGF0SWQ6IG51bGwsXG4gICAgICBnbG9iYWw6IHRydWVcbiAgICB9KTtcbiAgICBzZXRTaG93QWRkTWVtb3J5KGZhbHNlKTtcbiAgICBhbGVydCgnTWVtw7NyaWEgY3JpYWRhIGNvbSBzdWNlc3NvIScpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZU1lbW9yeSA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgaWYgKGNvbmZpcm0oJ1RlbSBjZXJ0ZXphIHF1ZSBkZXNlamEgZGVsZXRhciBlc3RhIG1lbcOzcmlhPycpKSB7XG4gICAgICBzZXRNZW1vcmllcyhwcmV2ID0+IHByZXYuZmlsdGVyKChfLCBpKSA9PiBpICE9PSBpbmRleCkpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVEZWxldGVDYXRlZ29yeSA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgaWYgKGNvbmZpcm0oJ1RlbSBjZXJ0ZXphIHF1ZSBkZXNlamEgZGVsZXRhciBlc3RhIGNhdGVnb3JpYT8nKSkge1xuICAgICAgc2V0TWVtb3J5Q2F0ZWdvcmllcyhwcmV2ID0+IHByZXYuZmlsdGVyKChfLCBpKSA9PiBpICE9PSBpbmRleCkpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBjb2xvcnMgPSBbXG4gICAgJyMzQjgyRjYnLCAnI0VGNDQ0NCcsICcjMTBCOTgxJywgJyNGNTlFMEInLFxuICAgICcjOEI1Q0Y2JywgJyNFQzQ4OTknLCAnIzA2QjZENCcsICcjODRDQzE2J1xuICBdO1xuXG4gIHJldHVybiAoXG4gICAgPEFuaW1hdGVQcmVzZW5jZT5cbiAgICAgIHtpc09wZW4gJiYgKFxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSB9fVxuICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2svNTAgYmFja2Ryb3AtYmx1ci1zbSB6LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtNFwiXG4gICAgICAgID5cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgaW5pdGlhbD17eyBzY2FsZTogMC45NSwgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBzY2FsZTogMSwgb3BhY2l0eTogMSB9fVxuICAgICAgICAgICAgZXhpdD17eyBzY2FsZTogMC45NSwgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTkwMC85NSB0by1ibHVlLTgwMC85NSBiYWNrZHJvcC1ibHVyLXNtXG4gICAgICAgICAgICAgICAgICAgICAgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCByb3VuZGVkLTJ4bCB3LWZ1bGwgbWF4LXctNnhsIG1heC1oLVs5NXZoXSBvdmVyZmxvdy1oaWRkZW5cbiAgICAgICAgICAgICAgICAgICAgICBteC00IGxnOm14LTBcIlxuICAgICAgICAgID5cblxuICAgICAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBsZzpwLTYgYm9yZGVyLWIgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgbGc6dGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj5Db25maWd1cmHDp8O1ZXM8L2gyPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNjAgdGV4dC1zbSBsZzpoaWRkZW4gbXQtMVwiPlxuICAgICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2dlcmFsJyAmJiAnSW5mb3JtYcOnw7VlcyBwZXNzb2FpcyBlIHNlbmhhJ31cbiAgICAgICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdhcGFyZW5jaWEnICYmICdQZXJzb25hbGl6YcOnw6NvIGRhIGludGVyZmFjZSd9XG4gICAgICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnaWEnICYmICdFbmRwb2ludHMgZGUgaW50ZWxpZ8OqbmNpYSBhcnRpZmljaWFsJ31cbiAgICAgICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdtZW1vcmlhJyAmJiAnU2lzdGVtYSBkZSBtZW3Ds3JpYXMnfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNjAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyBwLTIgaG92ZXI6Ymctd2hpdGUvMTAgcm91bmRlZC1sZ1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTZcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk02IDE4TDE4IDZNNiA2bDEyIDEyXCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGxnOmZsZXgtcm93XCI+XG4gICAgICAgICAgICAgIHsvKiBTaWRlYmFyIGRlIG5hdmVnYcOnw6NvICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBsZzp3LTY0IGJnLXdoaXRlLzUgYm9yZGVyLWIgbGc6Ym9yZGVyLWItMCBsZzpib3JkZXItciBib3JkZXItd2hpdGUvMTBcIj5cbiAgICAgICAgICAgICAgICA8bmF2IGNsYXNzTmFtZT1cInAtMiBsZzpwLTQgc3BhY2UteS0xIGxnOnNwYWNlLXktMiBvdmVyZmxvdy14LWF1dG8gbGc6b3ZlcmZsb3cteC12aXNpYmxlXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggbGc6ZmxleC1jb2wgc3BhY2UteC0yIGxnOnNwYWNlLXgtMCBsZzpzcGFjZS15LTIgbWluLXctbWF4IGxnOm1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdnZXJhbCcpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgbGc6dy1hdXRvIHRleHQtbGVmdCBweC0zIGxnOnB4LTQgcHktMiBsZzpweS0zIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBsZzpzcGFjZS14LTMgd2hpdGVzcGFjZS1ub3dyYXAgJHtcbiAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdnZXJhbCdcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtNjAwIHRleHQtd2hpdGUgc2hhZG93LWxnJ1xuICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC13aGl0ZS83MCBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOmJnLXdoaXRlLzEwJ1xuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgbGc6dy01IGgtNCBsZzpoLTVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTE2IDdhNCA0IDAgMTEtOCAwIDQgNCAwIDAxOCAwek0xMiAxNGE3IDcgMCAwMC03IDdoMTRhNyA3IDAgMDAtNy03elwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtIGxnOnRleHQtYmFzZVwiPkdlcmFsPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdhcGFyZW5jaWEnKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIGxnOnctYXV0byB0ZXh0LWxlZnQgcHgtMyBsZzpweC00IHB5LTIgbGc6cHktMyByb3VuZGVkLWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbGc6c3BhY2UteC0zIHdoaXRlc3BhY2Utbm93cmFwICR7XG4gICAgICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnYXBhcmVuY2lhJ1xuICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBzaGFkb3ctbGcnXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LXdoaXRlLzcwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Ymctd2hpdGUvMTAnXG4gICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBsZzp3LTUgaC00IGxnOmgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNyAyMWE0IDQgMCAwMS00LTRWNWEyIDIgMCAwMTItMmg0YTIgMiAwIDAxMiAydjEyYTQgNCAwIDAxLTQgNHpNMjEgNWEyIDIgMCAwMC0yLTJoLTRhMiAyIDAgMDAtMiAydjEyYTQgNCAwIDAwNCA0aDRhMiAyIDAgMDAyLTJWNXpcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbSBsZzp0ZXh0LWJhc2VcIj5BcGFyw6puY2lhPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdpYScpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgbGc6dy1hdXRvIHRleHQtbGVmdCBweC0zIGxnOnB4LTQgcHktMiBsZzpweS0zIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBsZzpzcGFjZS14LTMgd2hpdGVzcGFjZS1ub3dyYXAgJHtcbiAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdpYSdcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtNjAwIHRleHQtd2hpdGUgc2hhZG93LWxnJ1xuICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC13aGl0ZS83MCBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOmJnLXdoaXRlLzEwJ1xuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgbGc6dy01IGgtNCBsZzpoLTVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTkuNzUgMTdMOSAyMGwtMSAxaDhsLTEtMS0uNzUtM00zIDEzaDE4TTUgMTdoMTRhMiAyIDAgMDAyLTJWNWEyIDIgMCAwMC0yLTJINWEyIDIgMCAwMC0yIDJ2MTBhMiAyIDAgMDAyIDJ6XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc20gbGc6dGV4dC1iYXNlXCI+SUE8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ21lbW9yaWEnKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIGxnOnctYXV0byB0ZXh0LWxlZnQgcHgtMyBsZzpweC00IHB5LTIgbGc6cHktMyByb3VuZGVkLWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbGc6c3BhY2UteC0zIHdoaXRlc3BhY2Utbm93cmFwICR7XG4gICAgICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnbWVtb3JpYSdcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtNjAwIHRleHQtd2hpdGUgc2hhZG93LWxnJ1xuICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC13aGl0ZS83MCBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOmJnLXdoaXRlLzEwJ1xuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgbGc6dy01IGgtNCBsZzpoLTVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTE5IDExSDVtMTQgMGEyIDIgMCAwMTIgMnY2YTIgMiAwIDAxLTIgMkg1YTIgMiAwIDAxLTItMnYtNmEyIDIgMCAwMTItMm0xNCAwVjlhMiAyIDAgMDAtMi0yTTUgMTFWOWEyIDIgMCAwMTItMm0wIDBWNWEyIDIgMCAwMTItMmg2YTIgMiAwIDAxMiAydjJNNyA3aDEwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc20gbGc6dGV4dC1iYXNlXCI+TWVtw7NyaWE8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvbmF2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogQ29udGXDumRvIHByaW5jaXBhbCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcC00IGxnOnAtNiBvdmVyZmxvdy15LWF1dG8gbWF4LWgtW2NhbGMoOTV2aC0yMDBweCldXCI+XG4gICAgICAgICAgICAgICAgPEFuaW1hdGVQcmVzZW5jZSBtb2RlPVwid2FpdFwiPlxuICAgICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2dlcmFsJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgICAga2V5PVwiZ2VyYWxcIlxuICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogMjAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHg6IC0yMCB9fVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNwYWNlLXktOFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTZcIj5Db25maWd1cmHDp8O1ZXMgR2VyYWlzPC9oMz5cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIEZvdG8gZGUgUGVyZmlsICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS81IGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC14bCBwLTYgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItNFwiPkZvdG8gZGUgUGVyZmlsPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2VuZXJhbERhdGEucHJvZmlsZUltYWdlID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtnZW5lcmFsRGF0YS5wcm9maWxlSW1hZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwiUHJvZmlsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0yMCBoLTIwIHJvdW5kZWQtZnVsbCBvYmplY3QtY292ZXIgYm9yZGVyLTIgYm9yZGVyLXdoaXRlLzIwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yMCBoLTIwIGJnLWJsdWUtNjAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQtMnhsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlckRhdGEudXNlcm5hbWUuY2hhckF0KDApLnRvVXBwZXJDYXNlKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZmlsZUlucHV0UmVmLmN1cnJlbnQ/LmNsaWNrKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIHRleHQtd2hpdGUgcHgtNCBweS0yIHJvdW5kZWQtbGdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQWx0ZXJhciBGb3RvXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNjAgdGV4dC1zbSBtdC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEpQRywgUE5HIG91IEdJRi4gTcOheGltbyA1TUIuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZWY9e2ZpbGVJbnB1dFJlZn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWNjZXB0PVwiaW1hZ2UvKlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBmaWxlID0gZS50YXJnZXQuZmlsZXM/LlswXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChmaWxlKSBoYW5kbGVQcm9maWxlSW1hZ2VVcGxvYWQoZmlsZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoaWRkZW5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBOb21lIGRlIHVzdcOhcmlvICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS81IGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC14bCBwLTYgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItNFwiPk5vbWUgZGUgVXN1w6FyaW88L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2dlbmVyYWxEYXRhLnVzZXJuYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRHZW5lcmFsRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHVzZXJuYW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctd2hpdGUvMTAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHJvdW5kZWQtbGdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB4LTQgcHktMyB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLXdoaXRlLzUwIGZvY3VzOm91dGxpbmUtbm9uZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRGlnaXRlIHNldSBub21lIGRlIHVzdcOhcmlvXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZW5lcmFsRGF0YS51c2VybmFtZSAhPT0gdXNlckRhdGEudXNlcm5hbWUgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy15ZWxsb3ctNTAwLzEwIGJvcmRlciBib3JkZXIteWVsbG93LTUwMC8yMCByb3VuZGVkLWxnIHAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy0zMDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIOKaoO+4jyBOb21lIGRlIHVzdcOhcmlvIGFsdGVyYWRvLiBDbGlxdWUgZW0gXCJTYWx2YXIgQ29uZmlndXJhw6fDtWVzXCIgcGFyYSBhcGxpY2FyIGFzIG11ZGFuw6dhcy5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIEFsdGVyYXIgU2VuaGEgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLXhsIHAtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItNFwiPkFsdGVyYXIgU2VuaGE8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC13aGl0ZS84MCB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgU2VuaGEgQXR1YWxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2dlbmVyYWxEYXRhLmN1cnJlbnRQYXNzd29yZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRHZW5lcmFsRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIGN1cnJlbnRQYXNzd29yZDogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctd2hpdGUvMTAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHJvdW5kZWQtbGdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHgtNCBweS0zIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItd2hpdGUvNTAgZm9jdXM6b3V0bGluZS1ub25lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRGlnaXRlIHN1YSBzZW5oYSBhdHVhbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC13aGl0ZS84MCB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTm92YSBTZW5oYVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Z2VuZXJhbERhdGEubmV3UGFzc3dvcmR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0R2VuZXJhbERhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBuZXdQYXNzd29yZDogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctd2hpdGUvMTAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHJvdW5kZWQtbGdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHgtNCBweS0zIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItd2hpdGUvNTAgZm9jdXM6b3V0bGluZS1ub25lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRGlnaXRlIHN1YSBub3ZhIHNlbmhhXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXdoaXRlLzgwIHRleHQtc20gZm9udC1tZWRpdW0gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDb25maXJtYXIgTm92YSBTZW5oYVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Z2VuZXJhbERhdGEuY29uZmlybVBhc3N3b3JkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEdlbmVyYWxEYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgY29uZmlybVBhc3N3b3JkOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBweC00IHB5LTMgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci13aGl0ZS81MCBmb2N1czpvdXRsaW5lLW5vbmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJDb25maXJtZSBzdWEgbm92YSBzZW5oYVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVBhc3N3b3JkQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGV4dC13aGl0ZSBweC02IHB5LTIgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtsb2FkaW5nID8gJ0FsdGVyYW5kby4uLicgOiAnQWx0ZXJhciBTZW5oYSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdhcGFyZW5jaWEnICYmIChcbiAgICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9XCJhcGFyZW5jaWFcIlxuICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogMjAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHg6IC0yMCB9fVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNwYWNlLXktOFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTZcIj5Db25maWd1cmHDp8O1ZXMgZGUgQXBhcsOqbmNpYTwvaDM+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBGb250ZSBkbyBDaGF0ICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS81IGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC14bCBwLTYgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItNFwiPkZvbnRlIGRvIENoYXQ8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC13aGl0ZS84MCB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRmFtw61saWEgZGEgRm9udGVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXthcHBlYXJhbmNlU2V0dGluZ3MuZm9udGV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0QXBwZWFyYW5jZVNldHRpbmdzKHByZXYgPT4gKHsgLi4ucHJldiwgZm9udGU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci13aGl0ZS8yMCByb3VuZGVkLWxnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB4LTQgcHktMyB0ZXh0LXdoaXRlIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiSW50ZXJcIiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMFwiPkludGVyPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJSb2JvdG9cIiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMFwiPlJvYm90bzwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiSmV0QnJhaW5zIE1vbm9cIiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMFwiPkpldEJyYWlucyBNb25vPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJMYXRvXCIgY2xhc3NOYW1lPVwiYmctZ3JheS04MDBcIj5MYXRvPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJGaXJhIENvZGVcIiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMFwiPkZpcmEgQ29kZTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiTWVycml3ZWF0aGVyXCIgY2xhc3NOYW1lPVwiYmctZ3JheS04MDBcIj5NZXJyaXdlYXRoZXI8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIk9wZW4gU2Fuc1wiIGNsYXNzTmFtZT1cImJnLWdyYXktODAwXCI+T3BlbiBTYW5zPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJTb3VyY2UgU2FucyBQcm9cIiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMFwiPlNvdXJjZSBTYW5zIFBybzwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiUG9wcGluc1wiIGNsYXNzTmFtZT1cImJnLWdyYXktODAwXCI+UG9wcGluczwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiTnVuaXRvXCIgY2xhc3NOYW1lPVwiYmctZ3JheS04MDBcIj5OdW5pdG88L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS81IGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvODAgdGV4dC1zbSBtYi0yXCI+UHLDqS12aXN1YWxpemHDp8Ojbzo8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgcC0zIGJnLXdoaXRlLzUgcm91bmRlZCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgZm9udEZhbWlseTogYXBwZWFyYW5jZVNldHRpbmdzLmZvbnRlLCBmb250U2l6ZTogYCR7YXBwZWFyYW5jZVNldHRpbmdzLnRhbWFuaG9Gb250ZX1weGAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRXN0YSDDqSB1bWEgbWVuc2FnZW0gZGUgZXhlbXBsbyBwYXJhIHZpc3VhbGl6YXIgYSBmb250ZSBzZWxlY2lvbmFkYS5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTG9yZW0gaXBzdW0gZG9sb3Igc2l0IGFtZXQsIGNvbnNlY3RldHVyIGFkaXBpc2NpbmcgZWxpdC5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogVGFtYW5obyBkYSBGb250ZSAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvNSBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQteGwgcC02IG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5UYW1hbmhvIGRhIEZvbnRlPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtd2hpdGUvODAgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFRhbWFuaG86IHthcHBlYXJhbmNlU2V0dGluZ3MudGFtYW5ob0ZvbnRlfXB4XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYW5nZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbj1cIjEwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWF4PVwiMjRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17YXBwZWFyYW5jZVNldHRpbmdzLnRhbWFuaG9Gb250ZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRBcHBlYXJhbmNlU2V0dGluZ3MocHJldiA9PiAoeyAuLi5wcmV2LCB0YW1hbmhvRm9udGU6IHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLTIgYmctd2hpdGUvMjAgcm91bmRlZC1sZyBhcHBlYXJhbmNlLW5vbmUgY3Vyc29yLXBvaW50ZXIgc2xpZGVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIHRleHQteHMgdGV4dC13aGl0ZS82MCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPjEwcHg8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPjI0cHg8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIFNlc3PDtWVzIGRlIENoYXQgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLXhsIHAtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItNFwiPlNlc3PDtWVzIGRlIENoYXQ8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPkRpdmlzw6NvIEF1dG9tw6F0aWNhPC9oNT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS82MCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRGl2aWRpciBjaGF0cyBsb25nb3MgZW0gc2Vzc8O1ZXMgYmFzZWFkYXMgbmEgY29udGFnZW0gZGUgcGFsYXZyYXNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIHJlbGF0aXZlIGlubGluZS1mbGV4IGgtNiB3LTExIGl0ZW1zLWNlbnRlciByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0cmFuc2xhdGUteC02IGlubGluZS1ibG9jayBoLTQgdy00IHRyYW5zZm9ybSByb3VuZGVkLWZ1bGwgYmctd2hpdGUgdHJhbnNpdGlvblwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC13aGl0ZS84MCB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgUGFsYXZyYXMgcG9yIFNlc3PDo286IHthcHBlYXJhbmNlU2V0dGluZ3MucGFsYXZyYXNQb3JTZXNzYW8udG9Mb2NhbGVTdHJpbmcoKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInJhbmdlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWluPVwiMTAwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1heD1cIjIwMDAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RlcD1cIjUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXthcHBlYXJhbmNlU2V0dGluZ3MucGFsYXZyYXNQb3JTZXNzYW99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0QXBwZWFyYW5jZVNldHRpbmdzKHByZXYgPT4gKHsgLi4ucHJldiwgcGFsYXZyYXNQb3JTZXNzYW86IHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLTIgYmctd2hpdGUvMjAgcm91bmRlZC1sZyBhcHBlYXJhbmNlLW5vbmUgY3Vyc29yLXBvaW50ZXIgc2xpZGVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIHRleHQteHMgdGV4dC13aGl0ZS82MCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPjEuMDAwPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj4yMC4wMDA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAvMTAgYm9yZGVyIGJvcmRlci1ibHVlLTUwMC8yMCByb3VuZGVkLWxnIHAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTMwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIPCfkqEgPHN0cm9uZz5EaWNhOjwvc3Ryb25nPiBTZXNzw7VlcyBtZW5vcmVzIGNhcnJlZ2FtIG1haXMgcsOhcGlkbywgbWFzIHBvZGVtIGZyYWdtZW50YXIgY29udmVyc2FzIGxvbmdhcy5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgUmVjb21lbmRhbW9zIGVudHJlIDMuMDAwLTguMDAwIHBhbGF2cmFzIHBhcmEgbWVsaG9yIGV4cGVyacOqbmNpYS5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2lhJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgICAga2V5PVwiaWFcIlxuICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogMjAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHg6IC0yMCB9fVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNwYWNlLXktOFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTZcIj5JbnRlbGlnw6puY2lhIEFydGlmaWNpYWw8L2gzPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogQm90w6NvIEFkaWNpb25hciBFbmRwb2ludCAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS84MFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIEdlcmVuY2llIHNldXMgZW5kcG9pbnRzIGRlIElBIHBlcnNvbmFsaXphZG9zXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dBZGRFbmRwb2ludCghc2hvd0FkZEVuZHBvaW50KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi02MDAgaG92ZXI6YmctZ3JlZW4tNzAwIHRleHQtd2hpdGUgcHgtNCBweS0yIHJvdW5kZWQtbGdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZm9udC1tZWRpdW0gZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDR2MTZtOC04SDRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkFkaWNpb25hciBFbmRwb2ludDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIEZvcm11bMOhcmlvIEFkaWNpb25hciBFbmRwb2ludCAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIHtzaG93QWRkRW5kcG9pbnQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgaGVpZ2h0OiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBoZWlnaHQ6ICdhdXRvJyB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgaGVpZ2h0OiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUvNSBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQteGwgcC02IG1iLTZcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5Ob3ZvIEVuZHBvaW50PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXdoaXRlLzgwIHRleHQtc20gZm9udC1tZWRpdW0gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE5vbWUgZG8gRW5kcG9pbnQgKlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e25ld0VuZHBvaW50Lm5vbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdFbmRwb2ludChwcmV2ID0+ICh7IC4uLnByZXYsIG5vbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctd2hpdGUvMTAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHJvdW5kZWQtbGdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBweC00IHB5LTMgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci13aGl0ZS81MCBmb2N1czpvdXRsaW5lLW5vbmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRXg6IE1ldSBFbmRwb2ludFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXdoaXRlLzgwIHRleHQtc20gZm9udC1tZWRpdW0gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFVSTCBkbyBFbmRwb2ludCAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ1cmxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtuZXdFbmRwb2ludC51cmx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdFbmRwb2ludChwcmV2ID0+ICh7IC4uLnByZXYsIHVybDogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB4LTQgcHktMyB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLXdoaXRlLzUwIGZvY3VzOm91dGxpbmUtbm9uZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJodHRwczovL2FwaS5leGVtcGxvLmNvbS92MS9jaGF0L2NvbXBsZXRpb25zXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtd2hpdGUvODAgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQVBJIEtleSAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJwYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e25ld0VuZHBvaW50LmFwaUtleX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE5ld0VuZHBvaW50KHByZXYgPT4gKHsgLi4ucHJldiwgYXBpS2V5OiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci13aGl0ZS8yMCByb3VuZGVkLWxnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHgtNCBweS0zIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItd2hpdGUvNTAgZm9jdXM6b3V0bGluZS1ub25lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cInNrLS4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXdoaXRlLzgwIHRleHQtc20gZm9udC1tZWRpdW0gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1vZGVsbyBQYWRyw6NvXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3RW5kcG9pbnQubW9kZWxvUGFkcmFvfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3RW5kcG9pbnQocHJldiA9PiAoeyAuLi5wcmV2LCBtb2RlbG9QYWRyYW86IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctd2hpdGUvMTAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHJvdW5kZWQtbGdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBweC00IHB5LTMgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci13aGl0ZS81MCBmb2N1czpvdXRsaW5lLW5vbmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZ3B0LTMuNS10dXJib1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgc3BhY2UteC0zIG10LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0FkZEVuZHBvaW50KGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtd2hpdGUvNzAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIENhbmNlbGFyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWRkRW5kcG9pbnR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDAgdGV4dC13aGl0ZSBweC02IHB5LTIgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBZGljaW9uYXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogTGlzdGEgZGUgRW5kcG9pbnRzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2FpRW5kcG9pbnRzLm1hcCgoZW5kcG9pbnQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJiZy13aGl0ZS81IGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC14bCBwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMyBoLTMgcm91bmRlZC1mdWxsICR7ZW5kcG9pbnQuYXRpdm8gPyAnYmctZ3JlZW4tNTAwJyA6ICdiZy1ncmF5LTUwMCd9YH0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj57ZW5kcG9pbnQubm9tZX08L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsoZW5kcG9pbnQubm9tZSA9PT0gJ09wZW5Sb3V0ZXInIHx8IGVuZHBvaW50Lm5vbWUgPT09ICdEZWVwU2VlaycpICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLWJsdWUtNTAwLzIwIHRleHQtYmx1ZS0zMDAgdGV4dC14cyBweC0yIHB5LTEgcm91bmRlZC1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFByw6ktY29uZmlndXJhZG9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVUb2dnbGVFbmRwb2ludChpbmRleCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0zIHB5LTEgcm91bmRlZC1sZyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbmRwb2ludC5hdGl2b1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDAgdGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTYwMCBob3ZlcjpiZy1ncmF5LTcwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2VuZHBvaW50LmF0aXZvID8gJ0F0aXZvJyA6ICdJbmF0aXZvJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVUZXN0RW5kcG9pbnQoZW5kcG9pbnQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmcgfHwgIWVuZHBvaW50LmFwaUtleX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0LXdoaXRlIHB4LTMgcHktMSByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBUZXN0YXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVFZGl0RW5kcG9pbnQoaW5kZXgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmcteWVsbG93LTYwMCBob3ZlcjpiZy15ZWxsb3ctNzAwIHRleHQtd2hpdGUgcHgtMyBweS0xIHJvdW5kZWQtbGdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBFZGl0YXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZW5kcG9pbnQubm9tZSAhPT0gJ09wZW5Sb3V0ZXInICYmIGVuZHBvaW50Lm5vbWUgIT09ICdEZWVwU2VlaycgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGVFbmRwb2ludChpbmRleCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXJlZC02MDAgaG92ZXI6YmctcmVkLTcwMCB0ZXh0LXdoaXRlIHB4LTMgcHktMSByb3VuZGVkLWxnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRGVsZXRhclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00IHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwXCI+VVJMOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbW9ubyB0ZXh0LXhzIGJyZWFrLWFsbFwiPntlbmRwb2ludC51cmx9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwXCI+TW9kZWxvOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCI+e2VuZHBvaW50Lm1vZGVsb1BhZHJhbyB8fCAnTsOjbyBlc3BlY2lmaWNhZG8nfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZS82MFwiPkFQSSBLZXk6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tb25vIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtlbmRwb2ludC5hcGlLZXkgPyAn4oCi4oCi4oCi4oCi4oCi4oCi4oCi4oCi4oCi4oCi4oCi4oCiJyArIGVuZHBvaW50LmFwaUtleS5zbGljZSgtNCkgOiAnTsOjbyBjb25maWd1cmFkYSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwXCI+U3RhdHVzOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2Bmb250LW1lZGl1bSAke2VuZHBvaW50LmF0aXZvID8gJ3RleHQtZ3JlZW4tNDAwJyA6ICd0ZXh0LWdyYXktNDAwJ31gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtlbmRwb2ludC5hdGl2byA/ICdBdGl2bycgOiAnSW5hdGl2byd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogTW9kYWwgZGUgRWRpw6fDo28gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZWRpdGluZ0VuZHBvaW50ID09PSBpbmRleCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBoZWlnaHQ6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIGhlaWdodDogJ2F1dG8nIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCBoZWlnaHQ6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC00IHB0LTQgYm9yZGVyLXQgYm9yZGVyLXdoaXRlLzEwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgbWItNFwiPkVkaXRhciBFbmRwb2ludDwvaDU+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC13aGl0ZS84MCB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBUEkgS2V5ICpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZWRpdEVuZHBvaW50RGF0YS5hcGlLZXl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFZGl0RW5kcG9pbnREYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgYXBpS2V5OiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci13aGl0ZS8yMCByb3VuZGVkLWxnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHgtNCBweS0yIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItd2hpdGUvNTAgZm9jdXM6b3V0bGluZS1ub25lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQ29sZSBzdWEgQVBJIEtleSBhcXVpLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtd2hpdGUvODAgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTW9kZWxvIFBhZHLDo28gKlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2VkaXRFbmRwb2ludERhdGEubW9kZWxvUGFkcmFvfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RWRpdEVuZHBvaW50RGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIG1vZGVsb1BhZHJhbzogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB4LTQgcHktMiB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLXdoaXRlLzUwIGZvY3VzOm91dGxpbmUtbm9uZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkV4OiBncHQtNCwgY2xhdWRlLTMtc29ubmV0LCBldGMuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBzcGFjZS14LTIgbXQtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDYW5jZWxFZGl0RW5kcG9pbnR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNzAwIHRleHQtd2hpdGUgcHgtNCBweS0yIHJvdW5kZWQtbGdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDYW5jZWxhclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNhdmVFZGl0RW5kcG9pbnR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFNhbHZhclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBDYW1wbyBwYXJhIGVkaXRhciBBUEkgS2V5IGRvcyBwcsOpLWNvbmZpZ3VyYWRvcyAobWFudGlkbyBwYXJhIGNvbXBhdGliaWxpZGFkZSkgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7KGVuZHBvaW50Lm5vbWUgPT09ICdPcGVuUm91dGVyJyB8fCBlbmRwb2ludC5ub21lID09PSAnRGVlcFNlZWsnKSAmJiAhZW5kcG9pbnQuYXBpS2V5ICYmIGVkaXRpbmdFbmRwb2ludCAhPT0gaW5kZXggJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgcHQtNCBib3JkZXItdCBib3JkZXItd2hpdGUvMTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC13aGl0ZS84MCB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIENvbmZpZ3VyZSBzdWEgQVBJIEtleTpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkNvbGUgc3VhIEFQSSBLZXkgYXF1aS4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBweC00IHB5LTIgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci13aGl0ZS81MCBmb2N1czpvdXRsaW5lLW5vbmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdLZXkgPSBlLnRhcmdldC52YWx1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRBaUVuZHBvaW50cyhwcmV2ID0+IHByZXYubWFwKChlcCwgaSkgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGkgPT09IGluZGV4ID8geyAuLi5lcCwgYXBpS2V5OiBuZXdLZXkgfSA6IGVwXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVUb2dnbGVFbmRwb2ludChpbmRleCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFNhbHZhclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ21lbW9yaWEnICYmIChcbiAgICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9XCJtZW1vcmlhXCJcbiAgICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IDIwIH19XG4gICAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCB4OiAtMjAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzcGFjZS15LThcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi02XCI+U2lzdGVtYSBkZSBNZW3Ds3JpYTwvaDM+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBCb3TDtWVzIGRlIEHDp8OjbyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTMgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0FkZENhdGVnb3J5KCFzaG93QWRkQ2F0ZWdvcnkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMiByb3VuZGVkLWxnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZvbnQtbWVkaXVtIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk03IDdoLjAxTTcgM2g1Yy41MTIgMCAxLjAyNC4xOTUgMS40MTQuNTg2bDcgN2EyIDIgMCAwMTAgMi44MjhsLTcgN2EyIDIgMCAwMS0yLjgyOCAwbC03LTdBMS45OTQgMS45OTQgMCAwMTMgMTJWN2E0IDQgMCAwMTQtNHpcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPk5vdmEgQ2F0ZWdvcmlhPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dBZGRNZW1vcnkoIXNob3dBZGRNZW1vcnkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmb250LW1lZGl1bSBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNMTkgMTFINW0xNCAwYTIgMiAwIDAxMiAydjZhMiAyIDAgMDEtMiAySDVhMiAyIDAgMDEtMi0ydi02YTIgMiAwIDAxMi0ybTE0IDBWOWEyIDIgMCAwMC0yLTJNNSAxMVY5YTIgMiAwIDAxMi0ybTAgMFY1YTIgMiAwIDAxMi0yaDZhMiAyIDAgMDEyIDJ2Mk03IDdoMTBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPk5vdmEgTWVtw7NyaWE8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBGb3JtdWzDoXJpbyBOb3ZhIENhdGVnb3JpYSAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIHtzaG93QWRkQ2F0ZWdvcnkgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgaGVpZ2h0OiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBoZWlnaHQ6ICdhdXRvJyB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgaGVpZ2h0OiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUvNSBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQteGwgcC02IG1iLTZcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5Ob3ZhIENhdGVnb3JpYTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXdoaXRlLzgwIHRleHQtc20gZm9udC1tZWRpdW0gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE5vbWUgZGEgQ2F0ZWdvcmlhICpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtuZXdDYXRlZ29yeS5ub21lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3Q2F0ZWdvcnkocHJldiA9PiAoeyAuLi5wcmV2LCBub21lOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci13aGl0ZS8yMCByb3VuZGVkLWxnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHgtNCBweS0zIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItd2hpdGUvNTAgZm9jdXM6b3V0bGluZS1ub25lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkV4OiBUcmFiYWxobywgUGVzc29hbCwgUHJvamV0b3MuLi5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC13aGl0ZS84MCB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBEZXNjcmnDp8Ojb1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3Q2F0ZWdvcnkuZGVzY3JpY2FvfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3Q2F0ZWdvcnkocHJldiA9PiAoeyAuLi5wcmV2LCBkZXNjcmljYW86IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctd2hpdGUvMTAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHJvdW5kZWQtbGdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBweC00IHB5LTMgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci13aGl0ZS81MCBmb2N1czpvdXRsaW5lLW5vbmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgcmVzaXplLW5vbmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJEZXNjcmV2YSBvIHByb3DDs3NpdG8gZGVzdGEgY2F0ZWdvcmlhLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtd2hpdGUvODAgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ29yIGRhIENhdGVnb3JpYVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbG9ycy5tYXAoKGNvbG9yKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17Y29sb3J9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldE5ld0NhdGVnb3J5KHByZXYgPT4gKHsgLi4ucHJldiwgY29yOiBjb2xvciB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctOCBoLTggcm91bmRlZC1mdWxsIGJvcmRlci0yIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld0NhdGVnb3J5LmNvciA9PT0gY29sb3IgPyAnYm9yZGVyLXdoaXRlIHNjYWxlLTExMCcgOiAnYm9yZGVyLXdoaXRlLzMwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiBjb2xvciB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgc3BhY2UteC0zIG10LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0FkZENhdGVnb3J5KGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtd2hpdGUvNzAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIENhbmNlbGFyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWRkQ2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMiByb3VuZGVkLWxnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmb250LW1lZGl1bVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIENyaWFyIENhdGVnb3JpYVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBGb3JtdWzDoXJpbyBOb3ZhIE1lbcOzcmlhICovfVxuICAgICAgICAgICAgICAgICAgICAgICAge3Nob3dBZGRNZW1vcnkgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgaGVpZ2h0OiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBoZWlnaHQ6ICdhdXRvJyB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgaGVpZ2h0OiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUvNSBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQteGwgcC02IG1iLTZcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5Ob3ZhIE1lbcOzcmlhPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtd2hpdGUvODAgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgVMOtdHVsbyBkYSBNZW3Ds3JpYSAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3TWVtb3J5LnRpdHVsb31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE5ld01lbW9yeShwcmV2ID0+ICh7IC4uLnByZXYsIHRpdHVsbzogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB4LTQgcHktMyB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLXdoaXRlLzUwIGZvY3VzOm91dGxpbmUtbm9uZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFeDogSW5mb3JtYcOnw7VlcyBpbXBvcnRhbnRlcyBzb2JyZS4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXdoaXRlLzgwIHRleHQtc20gZm9udC1tZWRpdW0gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIENvbnRlw7pkbyAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtuZXdNZW1vcnkuY29udGV1ZG99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdNZW1vcnkocHJldiA9PiAoeyAuLi5wcmV2LCBjb250ZXVkbzogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB4LTQgcHktMyB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLXdoaXRlLzUwIGZvY3VzOm91dGxpbmUtbm9uZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCByZXNpemUtbm9uZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17NH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkRpZ2l0ZSBvIGNvbnRlw7pkbyBkYSBtZW3Ds3JpYS4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXdoaXRlLzgwIHRleHQtc20gZm9udC1tZWRpdW0gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ2F0ZWdvcmlhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3TWVtb3J5LmNhdGVnb3JpYSB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3TWVtb3J5KHByZXYgPT4gKHsgLi4ucHJldiwgY2F0ZWdvcmlhOiBlLnRhcmdldC52YWx1ZSB8fCBudWxsIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHgtNCBweS0zIHRleHQtd2hpdGUgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMFwiPlNlbSBjYXRlZ29yaWE8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttZW1vcnlDYXRlZ29yaWVzLm1hcCgoY2F0ZWdvcnksIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtpbmRleH0gdmFsdWU9e2NhdGVnb3J5Lm5vbWV9IGNsYXNzTmFtZT1cImJnLWdyYXktODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NhdGVnb3J5Lm5vbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXdoaXRlLzgwIHRleHQtc20gZm9udC1tZWRpdW0gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ29yIGRhIE1lbcOzcmlhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29sb3JzLnNsaWNlKDAsIDQpLm1hcCgoY29sb3IpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17Y29sb3J9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0TmV3TWVtb3J5KHByZXYgPT4gKHsgLi4ucHJldiwgY29yOiBjb2xvciB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy04IGgtOCByb3VuZGVkLWZ1bGwgYm9yZGVyLTIgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdNZW1vcnkuY29yID09PSBjb2xvciA/ICdib3JkZXItd2hpdGUgc2NhbGUtMTEwJyA6ICdib3JkZXItd2hpdGUvMzAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiBjb2xvciB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC13aGl0ZS84MCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRXNjb3BvIGRhIE1lbcOzcmlhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3TWVtb3J5Lmdsb2JhbCA/ICdnbG9iYWwnIDogKG5ld01lbW9yeS5jaGF0SWQgfHwgJycpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBlLnRhcmdldC52YWx1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh2YWx1ZSA9PT0gJ2dsb2JhbCcpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0TmV3TWVtb3J5KHByZXYgPT4gKHsgLi4ucHJldiwgZ2xvYmFsOiB0cnVlLCBjaGF0SWQ6IG51bGwgfSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0TmV3TWVtb3J5KHByZXYgPT4gKHsgLi4ucHJldiwgZ2xvYmFsOiBmYWxzZSwgY2hhdElkOiB2YWx1ZSB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJnLXdoaXRlLzEwIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItd2hpdGUvNTAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJnbG9iYWxcIiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDwn4yQIEdsb2JhbCAodG9kb3Mgb3MgY2hhdHMpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGdyb3VwIGxhYmVsPVwiQ2hhdHMgRXNwZWPDrWZpY29zXCIgY2xhc3NOYW1lPVwiYmctZ3JheS04MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjaGF0cy5tYXAoKGNoYXQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2NoYXQuaWR9IHZhbHVlPXtjaGF0LmlkfSBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+SrCB7Y2hhdC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvb3B0Z3JvdXA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzUwIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3TWVtb3J5Lmdsb2JhbFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnRXN0YSBtZW3Ds3JpYSBmaWNhcsOhIGRpc3BvbsOtdmVsIGVtIHRvZG9zIG9zIGNoYXRzJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnRXN0YSBtZW3Ds3JpYSBmaWNhcsOhIGRpc3BvbsOtdmVsIGFwZW5hcyBubyBjaGF0IHNlbGVjaW9uYWRvJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtMyBtdC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dBZGRNZW1vcnkoZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC13aGl0ZS83MCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ2FuY2VsYXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBZGRNZW1vcnl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDAgdGV4dC13aGl0ZSBweC02IHB5LTIgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDcmlhciBNZW3Ds3JpYVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBMaXN0YSBkZSBDYXRlZ29yaWFzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAge21lbW9yeUNhdGVnb3JpZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+Q2F0ZWdvcmlhczwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bWVtb3J5Q2F0ZWdvcmllcy5tYXAoKGNhdGVnb3J5LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLXhsIHAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWQtZnVsbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiBjYXRlZ29yeS5jb3IgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg1IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj57Y2F0ZWdvcnkubm9tZX08L2g1PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZURlbGV0ZUNhdGVnb3J5KGluZGV4KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNDAwIGhvdmVyOnRleHQtcmVkLTMwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNMTkgN2wtLjg2NyAxMi4xNDJBMiAyIDAgMDExNi4xMzggMjFINy44NjJhMiAyIDAgMDEtMS45OTUtMS44NThMNSA3bTUgNHY2bTQtNnY2bTEtMTBWNGExIDEgMCAwMC0xLTFoLTRhMSAxIDAgMDAtMSAxdjNNNCA3aDE2XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2F0ZWdvcnkuZGVzY3JpY2FvICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNjAgdGV4dC1zbVwiPntjYXRlZ29yeS5kZXNjcmljYW99PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIExpc3RhIGRlIE1lbcOzcmlhcyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgTWVtw7NyaWFzICh7bWVtb3JpZXMubGVuZ3RofSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge21lbW9yaWVzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLXhsIHAtOCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgdGV4dC13aGl0ZS80MCBteC1hdXRvIG1iLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNMTkgMTFINW0xNCAwYTIgMiAwIDAxMiAydjZhMiAyIDAgMDEtMiAySDVhMiAyIDAgMDEtMi0ydi02YTIgMiAwIDAxMi0ybTE0IDBWOWEyIDIgMCAwMC0yLTJNNSAxMVY5YTIgMiAwIDAxMi0ybTAgMFY1YTIgMiAwIDAxMi0yaDZhMiAyIDAgMDEyIDJ2Mk03IDdoMTBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwXCI+TmVuaHVtYSBtZW3Ds3JpYSBjcmlhZGEgYWluZGE8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzQwIHRleHQtc20gbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDbGlxdWUgZW0gXCJOb3ZhIE1lbcOzcmlhXCIgcGFyYSBjb21lw6dhclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bWVtb3JpZXMubWFwKChtZW1vcnksIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiYmctd2hpdGUvNSBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQteGwgcC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNCByb3VuZGVkLWZ1bGwgZmxleC1zaHJpbmstMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiBtZW1vcnkuY29yIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg1IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1zZW1pYm9sZFwiPnttZW1vcnkudGl0dWxvfTwvaDU+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21lbW9yeS5jYXRlZ29yaWEgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwMC8yMCB0ZXh0LWJsdWUtMzAwIHRleHQteHMgcHgtMiBweS0xIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttZW1vcnkuY2F0ZWdvcmlhfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC14cyBweC0yIHB5LTEgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbW9yeS5nbG9iYWxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi01MDAvMjAgdGV4dC1ncmVlbi0zMDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctb3JhbmdlLTUwMC8yMCB0ZXh0LW9yYW5nZS0zMDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttZW1vcnkuZ2xvYmFsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAn8J+MkCBHbG9iYWwnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBg8J+SrCAke2NoYXRzLmZpbmQoY2hhdCA9PiBjaGF0LmlkID09PSBtZW1vcnkuY2hhdElkKT8ubmFtZSB8fCAnQ2hhdCBFc3BlY8OtZmljbyd9YFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZURlbGV0ZU1lbW9yeShpbmRleCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTQwMCBob3Zlcjp0ZXh0LXJlZC0zMDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTE5IDdsLS44NjcgMTIuMTQyQTIgMiAwIDAxMTYuMTM4IDIxSDcuODYyYTIgMiAwIDAxLTEuOTk1LTEuODU4TDUgN201IDR2Nm00LTZ2Nm0xLTEwVjRhMSAxIDAgMDAtMS0xaC00YTEgMSAwIDAwLTEgMXYzTTQgN2gxNlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS84MCB0ZXh0LXNtIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21lbW9yeS5jb250ZXVkb31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEZvb3RlciAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNiBib3JkZXItdCBib3JkZXItd2hpdGUvMjBcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVMb2dvdXR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1yZWQtNjAwIGhvdmVyOmJnLXJlZC03MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZC1sZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBTYWlyIGRhIENvbnRhXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG5cblxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC02IHB5LTIgdGV4dC13aGl0ZS83MCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBDYW5jZWxhclxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3NhdmVDb25maWd1cmF0aW9uc31cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgIHRleHQtd2hpdGUgcHgtNiBweS0yIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7bG9hZGluZyA/ICdTYWx2YW5kby4uLicgOiAnU2FsdmFyIENvbmZpZ3VyYcOnw7Vlcyd9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICApfVxuICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0IiwibW90aW9uIiwiQW5pbWF0ZVByZXNlbmNlIiwidXNlQXV0aCIsInVzZUFwcGVhcmFuY2UiLCJkb2MiLCJ1cGRhdGVEb2MiLCJzZXREb2MiLCJnZXREb2MiLCJkZWxldGVEb2MiLCJjb2xsZWN0aW9uIiwiZ2V0RG9jcyIsInF1ZXJ5Iiwib3JkZXJCeSIsInJlZiIsInVwbG9hZEJ5dGVzIiwiZ2V0RG93bmxvYWRVUkwiLCJsaXN0QWxsIiwiZGVsZXRlT2JqZWN0IiwidXBkYXRlUGFzc3dvcmQiLCJyZWF1dGhlbnRpY2F0ZVdpdGhDcmVkZW50aWFsIiwiRW1haWxBdXRoUHJvdmlkZXIiLCJkYiIsInN0b3JhZ2UiLCJTZXR0aW5nc01vZGFsIiwiaXNPcGVuIiwib25DbG9zZSIsInVzZXJEYXRhIiwib25Vc2VyRGF0YVVwZGF0ZSIsImxvZ291dCIsInVzZXIiLCJzZXR0aW5ncyIsImNvbnRleHRTZXR0aW5ncyIsInJlZnJlc2hTZXR0aW5ncyIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZmlsZUlucHV0UmVmIiwiZ2VuZXJhbERhdGEiLCJzZXRHZW5lcmFsRGF0YSIsInVzZXJuYW1lIiwicHJvZmlsZUltYWdlIiwiY3VycmVudFBhc3N3b3JkIiwibmV3UGFzc3dvcmQiLCJjb25maXJtUGFzc3dvcmQiLCJhcHBlYXJhbmNlU2V0dGluZ3MiLCJzZXRBcHBlYXJhbmNlU2V0dGluZ3MiLCJmb250ZSIsInRhbWFuaG9Gb250ZSIsInBhbGF2cmFzUG9yU2Vzc2FvIiwiYWlFbmRwb2ludHMiLCJzZXRBaUVuZHBvaW50cyIsIm5vbWUiLCJ1cmwiLCJhcGlLZXkiLCJtb2RlbG9QYWRyYW8iLCJhdGl2byIsIm1lbW9yaWVzIiwic2V0TWVtb3JpZXMiLCJtZW1vcnlDYXRlZ29yaWVzIiwic2V0TWVtb3J5Q2F0ZWdvcmllcyIsImNoYXRzIiwic2V0Q2hhdHMiLCJzaG93QWRkTWVtb3J5Iiwic2V0U2hvd0FkZE1lbW9yeSIsInNob3dBZGRDYXRlZ29yeSIsInNldFNob3dBZGRDYXRlZ29yeSIsIm5ld01lbW9yeSIsInNldE5ld01lbW9yeSIsInRpdHVsbyIsImNvbnRldWRvIiwiY29yIiwiY2F0ZWdvcmlhIiwiY2hhdElkIiwiZ2xvYmFsIiwibmV3Q2F0ZWdvcnkiLCJzZXROZXdDYXRlZ29yeSIsImRlc2NyaWNhbyIsInNob3dBZGRFbmRwb2ludCIsInNldFNob3dBZGRFbmRwb2ludCIsIm5ld0VuZHBvaW50Iiwic2V0TmV3RW5kcG9pbnQiLCJlZGl0aW5nRW5kcG9pbnQiLCJzZXRFZGl0aW5nRW5kcG9pbnQiLCJlZGl0RW5kcG9pbnREYXRhIiwic2V0RWRpdEVuZHBvaW50RGF0YSIsImxvYWRDb25maWd1cmF0aW9ucyIsImNvbnNvbGUiLCJsb2ciLCJjb25maWdEb2MiLCJleGlzdHMiLCJjb25maWciLCJkYXRhIiwiYXBhcmVuY2lhIiwiZW5kcG9pbnRzIiwiZW5kcG9pbnRzQXJyYXkiLCJPYmplY3QiLCJ2YWx1ZXMiLCJtZW1vcmlhcyIsIm1lbW9yaWFzQXJyYXkiLCJjYXRlZ29yaWFzIiwiY2F0ZWdvcmlhc0FycmF5IiwiZXJyb3IiLCJsb2FkQ2hhdHMiLCJjaGF0c1JlZiIsImNoYXRzUXVlcnkiLCJjaGF0c1NuYXBzaG90IiwibG9hZGVkQ2hhdHMiLCJmb3JFYWNoIiwicHVzaCIsImlkIiwibmFtZSIsImxhc3RNZXNzYWdlIiwidWx0aW1hTWVuc2FnZW0iLCJsYXN0TWVzc2FnZVRpbWUiLCJ1bHRpbWFNZW5zYWdlbUVtIiwiY3JlYXRlZEF0IiwiZGVsZXRlVXNlclN0b3JhZ2VEYXRhIiwidXNlclN0b3JhZ2VSZWYiLCJ1c2VyU3RvcmFnZUxpc3QiLCJkZWxldGVSZWN1cnNpdmVseSIsImZvbGRlclJlZiIsImZvbGRlckxpc3QiLCJmaWxlRGVsZXRlUHJvbWlzZXMiLCJpdGVtcyIsIm1hcCIsIml0ZW0iLCJQcm9taXNlIiwiYWxsIiwiZm9sZGVyRGVsZXRlUHJvbWlzZXMiLCJwcmVmaXhlcyIsInByZWZpeCIsImRlbGV0ZVVzZXJEb2N1bWVudHMiLCJjb25maWdTbmFwc2hvdCIsImNoYXRzQ29sbGVjdGlvbiIsImRlbGV0ZVByb21pc2VzIiwiZG9jcyIsInVzZXJEb2NSZWYiLCJ1cGRhdGVVc2VybmFtZSIsIm5ld1VzZXJuYW1lIiwic2hvd1N1Y2Nlc3NBbGVydCIsImFsZXJ0IiwibGVuZ3RoIiwibmV3VXNlckNyZWF0ZWQiLCJuZXdVc2VyRG9jIiwib2xkVXNlckRvY1JlZiIsIm9sZFVzZXJEb2MiLCJjdXJyZW50RGF0YSIsInVwZGF0ZWRBdCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImNoYXREb2MiLCJjaGF0RGF0YSIsImNoYXRzRXJyb3IiLCJjb25maWdFcnJvciIsInJvbGxiYWNrRXJyb3IiLCJFcnJvciIsIm1lc3NhZ2UiLCJzYXZlQ29uZmlndXJhdGlvbnMiLCJ1c2VybmFtZVVwZGF0ZWQiLCJjdXJyZW50VXNlcm5hbWUiLCJjb25maWdEYXRhIiwiZW5kcG9pbnQiLCJpbmRleCIsIm1lbW9yeSIsImNhdGVnb3J5IiwiZG9jUmVmIiwiaGFuZGxlUHJvZmlsZUltYWdlVXBsb2FkIiwiZmlsZSIsImltYWdlUmVmIiwiZG93bmxvYWRVUkwiLCJwcmV2IiwiaGFuZGxlUGFzc3dvcmRDaGFuZ2UiLCJjcmVkZW50aWFsIiwiZW1haWwiLCJoYW5kbGVMb2dvdXQiLCJjb25maXJtIiwiaGFuZGxlQWRkRW5kcG9pbnQiLCJoYW5kbGVUb2dnbGVFbmRwb2ludCIsImkiLCJoYW5kbGVEZWxldGVFbmRwb2ludCIsImZpbHRlciIsIl8iLCJoYW5kbGVFZGl0RW5kcG9pbnQiLCJoYW5kbGVTYXZlRWRpdEVuZHBvaW50IiwiaGFuZGxlQ2FuY2VsRWRpdEVuZHBvaW50IiwiaGFuZGxlVGVzdEVuZHBvaW50IiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIm1vZGVsIiwibWVzc2FnZXMiLCJyb2xlIiwiY29udGVudCIsIm1heF90b2tlbnMiLCJvayIsImhhbmRsZUFkZENhdGVnb3J5IiwiaGFuZGxlQWRkTWVtb3J5IiwiaGFuZGxlRGVsZXRlTWVtb3J5IiwiaGFuZGxlRGVsZXRlQ2F0ZWdvcnkiLCJjb2xvcnMiLCJkaXYiLCJpbml0aWFsIiwib3BhY2l0eSIsImFuaW1hdGUiLCJleGl0IiwiY2xhc3NOYW1lIiwic2NhbGUiLCJoMiIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwibmF2Iiwic3BhbiIsIm1vZGUiLCJ4IiwiaDMiLCJoNCIsImltZyIsInNyYyIsImFsdCIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwiY3VycmVudCIsImNsaWNrIiwiaW5wdXQiLCJ0eXBlIiwiYWNjZXB0Iiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwiZmlsZXMiLCJ2YWx1ZSIsInBsYWNlaG9sZGVyIiwibGFiZWwiLCJkaXNhYmxlZCIsInNlbGVjdCIsIm9wdGlvbiIsInN0eWxlIiwiZm9udEZhbWlseSIsImZvbnRTaXplIiwibWluIiwibWF4IiwicGFyc2VJbnQiLCJoNSIsInRvTG9jYWxlU3RyaW5nIiwic3RlcCIsInN0cm9uZyIsImhlaWdodCIsInNsaWNlIiwibmV3S2V5IiwiZXAiLCJ0ZXh0YXJlYSIsInJvd3MiLCJjb2xvciIsImJhY2tncm91bmRDb2xvciIsIm9wdGdyb3VwIiwiY2hhdCIsImZpbmQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/SettingsModal.tsx\n"));

/***/ })

});