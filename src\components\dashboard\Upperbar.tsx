'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from '@/contexts/SessionContext';

interface UpperbarProps {
  currentChat: string | null;
  chatName?: string;
  aiModel: string;
  onDownload?: () => void;
  onAttachments?: () => void;
  onStatistics?: () => void;
  isLoading?: boolean;
  attachmentsCount?: number;
  aiMetadata?: {
    usedCoT?: boolean;
  };
}

// Hook para tempo de sessão
const useSessionTime = (chatId: string | null) => {
  const [sessionTime, setSessionTime] = useState('0s');
  const [startTime] = useState(Date.now());
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  // Função para formatar tempo de forma inteligente
  const formatTime = (timeInMs: number): string => {
    const totalSeconds = Math.floor(timeInMs / 1000);
    const totalMinutes = Math.floor(totalSeconds / 60);
    const totalHours = Math.floor(totalMinutes / 60);
    const totalDays = Math.floor(totalHours / 24);
    const totalMonths = Math.floor(totalDays / 30);

    const seconds = totalSeconds % 60;
    const minutes = totalMinutes % 60;
    const hours = totalHours % 24;
    const days = totalDays % 30;

    // Menos de 1 minuto: apenas segundos
    if (totalMinutes === 0) {
      return `${totalSeconds}s`;
    }

    // Menos de 1 hora: MM:SS
    if (totalHours === 0) {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    // Menos de 1 dia: HH:MM:SS
    if (totalDays === 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    // Menos de 1 mês: Xd HH:MM
    if (totalMonths === 0) {
      return `${days}d ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    }

    // 1 mês ou mais: Xm Xd HH:MM
    return `${totalMonths}m ${days}d ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  };

  useEffect(() => {
    if (chatId) {
      intervalRef.current = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const formattedTime = formatTime(elapsed);
        setSessionTime(formattedTime);
      }, 1000);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [chatId, startTime]);

  return sessionTime;
};

export default function Upperbar({
  currentChat,
  chatName,
  aiModel,
  onDownload,
  onAttachments,
  onStatistics,
  isLoading = false,
  attachmentsCount = 0,
  aiMetadata
}: UpperbarProps) {
  const { sessionTime, currentSessionWords, isSessionActive } = useSession();

  const handleAttachments = () => {
    if (onAttachments) {
      onAttachments();
    } else {
      console.log('Anexos clicado');
    }
  };

  const handleStats = () => {
    if (onStatistics) {
      onStatistics();
    } else {
      console.log('Estatísticas clicado');
    }
  };

  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else {
      console.log('Download clicado');
    }
  };



  const currentModel = aiModel || 'GPT-4.1 Nano';
  const displayChatName = chatName || (currentChat ? `Chat ${currentChat}` : 'Nova Conversa');

  return (
    <div className="h-14 sm:h-16 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border-b border-blue-700/30 shadow-xl relative">
      {/* Efeito de brilho sutil */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none"></div>

      <div className="h-full flex items-center justify-between px-3 sm:px-4 lg:px-6 relative z-10">
        {/* Left Section - Model and Session Time */}
        <div className="flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0">
          {/* Indicador do Modelo */}
          <div className="flex items-center space-x-2 sm:space-x-3 bg-blue-900/30 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 min-w-0">
            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-400 rounded-full shadow-lg shadow-blue-400/50 flex-shrink-0"></div>
            <span className="text-xs sm:text-sm font-medium text-blue-100 truncate">{currentModel}</span>
            {aiMetadata?.usedCoT && (
              <div className="px-1.5 sm:px-2 py-0.5 sm:py-1 bg-cyan-600/20 text-cyan-300 text-xs rounded-full border border-cyan-500/30 hidden sm:block">
                CoT
              </div>
            )}
          </div>

          {/* Divisor */}
          <div className="w-px h-4 sm:h-6 bg-blue-600/30 hidden sm:block"></div>

          {/* Tempo de Sessão */}
          <div className="flex items-center space-x-1.5 sm:space-x-2 bg-blue-900/20 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 hidden sm:flex">
            <svg className="w-3 h-3 sm:w-4 sm:h-4 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-xs sm:text-sm text-blue-200 font-mono">
              {isSessionActive ? sessionTime : '0s'}
            </span>
            {isSessionActive && (
              <>
                <div className="w-px h-3 bg-blue-600/30"></div>
                <span className="text-xs text-blue-300">
                  {currentSessionWords} palavras
                </span>
              </>
            )}
          </div>
        </div>

        {/* Center Section - Chat Name */}
        <div className="absolute left-1/2 transform -translate-x-1/2 hidden sm:block">
          <div className="flex items-center space-x-2 sm:space-x-3 bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2 shadow-lg max-w-xs lg:max-w-sm">
            <div className={`w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full shadow-lg flex-shrink-0 ${
              isLoading
                ? 'bg-yellow-400 shadow-yellow-400/50 animate-pulse'
                : 'bg-cyan-400 shadow-cyan-400/50'
            }`}></div>
            <h1 className="text-sm sm:text-lg font-semibold text-white truncate">
              {displayChatName}
            </h1>
          </div>
        </div>

        {/* Right Section - Action Buttons */}
        <div className="flex items-center space-x-1 sm:space-x-2">
          {/* Botão Anexos */}
          <button
            onClick={handleAttachments}
            className="p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105 relative"
            title="Anexos"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
            </svg>
            {attachmentsCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-cyan-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium shadow-lg">
                {attachmentsCount > 99 ? '99+' : attachmentsCount}
              </span>
            )}
          </button>

          {/* Botão Estatísticas */}
          <button
            onClick={handleStats}
            className="p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105"
            title="Estatísticas"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </button>

          {/* Botão Download */}
          <button
            onClick={handleDownload}
            className="p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/30 hover:scale-105 border border-blue-500/30"
            title="Download"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}
