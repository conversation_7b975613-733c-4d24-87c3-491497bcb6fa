"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/contexts/SessionContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/SessionContext.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: function() { return /* binding */ SessionProvider; },\n/* harmony export */   useSession: function() { return /* binding */ useSession; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AppearanceContext */ \"(app-pages-browser)/./src/contexts/AppearanceContext.tsx\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ useSession,SessionProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst SessionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    currentSessionWords: 0,\n    sessionStartTime: null,\n    sessionTime: \"0s\",\n    isSessionActive: false,\n    startSession: ()=>{},\n    endSession: ()=>{},\n    addWordsToSession: ()=>{},\n    shouldCreateNewSession: ()=>false,\n    getVisibleMessages: ()=>[],\n    getTotalWordsInMessages: ()=>0\n});\nconst useSession = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SessionContext);\n    if (!context) {\n        throw new Error(\"useSession must be used within a SessionProvider\");\n    }\n    return context;\n};\n_s(useSession, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst SessionProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { settings } = (0,_contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__.useAppearance)();\n    const [currentSessionWords, setCurrentSessionWords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [sessionStartTime, setSessionStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessionTime, setSessionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0s\");\n    const [isSessionActive, setIsSessionActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentChatId, setCurrentChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const formatTime = (milliseconds)=>{\n        const seconds = Math.floor(milliseconds / 1000);\n        const minutes = Math.floor(seconds / 60);\n        const hours = Math.floor(minutes / 60);\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes % 60, \"m \").concat(seconds % 60, \"s\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"m \").concat(seconds % 60, \"s\");\n        } else {\n            return \"\".concat(seconds, \"s\");\n        }\n    };\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) throw new Error(\"Usu\\xe1rio n\\xe3o autenticado\");\n        const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n        const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.db, \"usuarios\");\n        const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n        const querySnapshot = await getDocs(q);\n        if (querySnapshot.empty) {\n            throw new Error(\"Usu\\xe1rio n\\xe3o encontrado\");\n        }\n        const userDoc = querySnapshot.docs[0];\n        const userData = userDoc.data();\n        return userData.username;\n    };\n    const updateChatSessionTime = async (chatId, totalTime)=>{\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.db, \"usuarios\", username, \"conversas\", chatId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)(chatRef, {\n                \"sessionTime.totalTime\": totalTime,\n                \"sessionTime.lastUpdated\": new Date().toISOString()\n            });\n        } catch (error) {\n            console.error(\"Erro ao atualizar tempo de sess\\xe3o:\", error);\n        }\n    };\n    const startSession = (chatId)=>{\n        const now = new Date();\n        setSessionStartTime(now);\n        setCurrentSessionWords(0);\n        setIsSessionActive(true);\n        setCurrentChatId(chatId);\n        // Iniciar timer\n        intervalRef.current = setInterval(()=>{\n            if (sessionStartTime) {\n                const elapsed = Date.now() - sessionStartTime.getTime();\n                setSessionTime(formatTime(elapsed));\n            }\n        }, 1000);\n    };\n    const endSession = async ()=>{\n        if (sessionStartTime && currentChatId) {\n            const totalTime = Date.now() - sessionStartTime.getTime();\n            await updateChatSessionTime(currentChatId, totalTime);\n        }\n        setSessionStartTime(null);\n        setCurrentSessionWords(0);\n        setIsSessionActive(false);\n        setCurrentChatId(null);\n        setSessionTime(\"0s\");\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n            intervalRef.current = null;\n        }\n    };\n    const addWordsToSession = (words)=>{\n        setCurrentSessionWords((prev)=>prev + words);\n    };\n    const shouldCreateNewSession = ()=>{\n        return currentSessionWords >= ((settings === null || settings === void 0 ? void 0 : settings.palavrasPorSessao) || 5000);\n    };\n    // Limpar timer quando o componente for desmontado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n            }\n        };\n    }, []);\n    // Atualizar timer quando a sessão iniciar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sessionStartTime && isSessionActive) {\n            const updateTimer = ()=>{\n                const elapsed = Date.now() - sessionStartTime.getTime();\n                setSessionTime(formatTime(elapsed));\n            };\n            intervalRef.current = setInterval(updateTimer, 1000);\n            updateTimer(); // Atualizar imediatamente\n            return ()=>{\n                if (intervalRef.current) {\n                    clearInterval(intervalRef.current);\n                }\n            };\n        }\n    }, [\n        sessionStartTime,\n        isSessionActive\n    ]);\n    const value = {\n        currentSessionWords,\n        sessionStartTime,\n        sessionTime,\n        isSessionActive,\n        startSession,\n        endSession,\n        addWordsToSession,\n        shouldCreateNewSession\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\contexts\\\\SessionContext.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(SessionProvider, \"KK8L80HbLM3aCo/wAS+QGnmZNJE=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__.useAppearance\n    ];\n});\n_c = SessionProvider;\nvar _c;\n$RefreshReg$(_c, \"SessionProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/SessionContext.tsx\n"));

/***/ })

});