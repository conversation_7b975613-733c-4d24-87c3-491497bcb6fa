/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZub3QtZm91bmQmcGFnZT0lMkZub3QtZm91bmQmYXBwUGF0aHM9JnBhZ2VQYXRoPS4uJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbm90LWZvdW5kLWVycm9yLmpzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNIZW5yaSU1Q0Rlc2t0b3AlNUNTaXRlUmFmdGhvciU1Q3JhZnRob3IlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q0hlbnJpJTVDRGVza3RvcCU1Q1NpdGVSYWZ0aG9yJTVDcmFmdGhvciZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFvRztBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0EsZ0NBQWdDLHdPQUF1RjtBQUN2SDtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUEwRztBQUNuSSxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmFmdGhvci8/MGNkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAgICdfX0RFRkFVTFRfXycsXG4gICAgICAgICAge30sXG4gICAgICAgICAge1xuICAgICAgICAgICAgZGVmYXVsdFBhZ2U6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9wYXJhbGxlbC1yb3V0ZS1kZWZhdWx0XCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9wYXJhbGxlbC1yb3V0ZS1kZWZhdWx0XCJdLFxuICAgICAgICAgIH1cbiAgICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSGVucmlcXFxcRGVza3RvcFxcXFxTaXRlUmFmdGhvclxcXFxyYWZ0aG9yXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKSwgXCJDOlxcXFxVc2Vyc1xcXFxIZW5yaVxcXFxEZXNrdG9wXFxcXFNpdGVSYWZ0aG9yXFxcXHJhZnRob3JcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiO1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL25vdC1mb3VuZFwiO1xuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCI7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9ub3QtZm91bmRcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL25vdC1mb3VuZFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CAppearanceContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CSessionContext.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CAppearanceContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CSessionContext.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AppearanceContext.tsx */ \"(ssr)/./src/contexts/AppearanceContext.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/SessionContext.tsx */ \"(ssr)/./src/contexts/SessionContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDSGVucmklNUNEZXNrdG9wJTVDU2l0ZVJhZnRob3IlNUNyYWZ0aG9yJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDSGVucmklNUNEZXNrdG9wJTVDU2l0ZVJhZnRob3IlNUNyYWZ0aG9yJTVDc3JjJTVDYXBwJTVDZ2xvYmFscy5jc3MmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNIZW5yaSU1Q0Rlc2t0b3AlNUNTaXRlUmFmdGhvciU1Q3JhZnRob3IlNUNzcmMlNUNjb250ZXh0cyU1Q0FwcGVhcmFuY2VDb250ZXh0LnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0hlbnJpJTVDRGVza3RvcCU1Q1NpdGVSYWZ0aG9yJTVDcmFmdGhvciU1Q3NyYyU1Q2NvbnRleHRzJTVDQXV0aENvbnRleHQudHN4Jm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDSGVucmklNUNEZXNrdG9wJTVDU2l0ZVJhZnRob3IlNUNyYWZ0aG9yJTVDc3JjJTVDY29udGV4dHMlNUNTZXNzaW9uQ29udGV4dC50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUEwSDtBQUMxSCx3S0FBb0g7QUFDcEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yYWZ0aG9yLz8zMTViIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSGVucmlcXFxcRGVza3RvcFxcXFxTaXRlUmFmdGhvclxcXFxyYWZ0aG9yXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxBcHBlYXJhbmNlQ29udGV4dC50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEhlbnJpXFxcXERlc2t0b3BcXFxcU2l0ZVJhZnRob3JcXFxccmFmdGhvclxcXFxzcmNcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxIZW5yaVxcXFxEZXNrdG9wXFxcXFNpdGVSYWZ0aG9yXFxcXHJhZnRob3JcXFxcc3JjXFxcXGNvbnRleHRzXFxcXFNlc3Npb25Db250ZXh0LnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CAppearanceContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Ccontexts%5CSessionContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AppearanceContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/AppearanceContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppearanceProvider: () => (/* binding */ AppearanceProvider),\n/* harmony export */   useAppearance: () => (/* binding */ useAppearance)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAppearance,AppearanceProvider auto */ \n\n\n\n\nconst defaultSettings = {\n    fonte: \"Inter\",\n    tamanhoFonte: 14,\n    palavrasPorSessao: 5000\n};\nconst AppearanceContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    settings: defaultSettings,\n    loading: true,\n    refreshSettings: async ()=>{}\n});\nconst useAppearance = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppearanceContext);\n    if (!context) {\n        throw new Error(\"useAppearance must be used within an AppearanceProvider\");\n    }\n    return context;\n};\nconst AppearanceProvider = ({ children })=>{\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSettings);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const getUsernameFromFirestore = async ()=>{\n        if (!user?.email) throw new Error(\"Usu\\xe1rio n\\xe3o autenticado\");\n        const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\"));\n        const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n        const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n        const querySnapshot = await getDocs(q);\n        if (querySnapshot.empty) {\n            throw new Error(\"Usu\\xe1rio n\\xe3o encontrado\");\n        }\n        const userDoc = querySnapshot.docs[0];\n        const userData = userDoc.data();\n        return userData.username;\n    };\n    const loadSettings = async ()=>{\n        if (!user?.email) {\n            setLoading(false);\n            return;\n        }\n        try {\n            const username = await getUsernameFromFirestore();\n            const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(configRef);\n            if (configDoc.exists()) {\n                const config = configDoc.data();\n                if (config.aparencia) {\n                    setSettings(config.aparencia);\n                } else {\n                    setSettings(defaultSettings);\n                }\n            } else {\n                setSettings(defaultSettings);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar configura\\xe7\\xf5es de apar\\xeancia:\", error);\n            setSettings(defaultSettings);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const refreshSettings = async ()=>{\n        setLoading(true);\n        await loadSettings();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSettings();\n    }, [\n        user\n    ]);\n    const value = {\n        settings,\n        loading,\n        refreshSettings\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppearanceContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\contexts\\\\AppearanceContext.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AppearanceContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    loading: true,\n    logout: async ()=>{}\n});\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, (user)=>{\n            setUser(user);\n            setLoading(false);\n        });\n        return ()=>unsubscribe();\n    }, []);\n    const logout = async ()=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth);\n        } catch (error) {\n            console.error(\"Erro ao fazer logout:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        logout\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/SessionContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/SessionContext.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AppearanceContext */ \"(ssr)/./src/contexts/AppearanceContext.tsx\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ useSession,SessionProvider auto */ \n\n\n\n\n\nconst SessionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    currentSessionWords: 0,\n    sessionStartTime: null,\n    sessionTime: \"0s\",\n    isSessionActive: false,\n    startSession: ()=>{},\n    endSession: ()=>{},\n    addWordsToSession: ()=>{},\n    shouldCreateNewSession: ()=>false,\n    getVisibleMessages: ()=>[],\n    getTotalWordsInMessages: ()=>0\n});\nconst useSession = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SessionContext);\n    if (!context) {\n        throw new Error(\"useSession must be used within a SessionProvider\");\n    }\n    return context;\n};\nconst SessionProvider = ({ children })=>{\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { settings } = (0,_contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__.useAppearance)();\n    const [currentSessionWords, setCurrentSessionWords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [sessionStartTime, setSessionStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessionTime, setSessionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0s\");\n    const [isSessionActive, setIsSessionActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentChatId, setCurrentChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const formatTime = (milliseconds)=>{\n        const seconds = Math.floor(milliseconds / 1000);\n        const minutes = Math.floor(seconds / 60);\n        const hours = Math.floor(minutes / 60);\n        if (hours > 0) {\n            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;\n        } else if (minutes > 0) {\n            return `${minutes}m ${seconds % 60}s`;\n        } else {\n            return `${seconds}s`;\n        }\n    };\n    const getUsernameFromFirestore = async ()=>{\n        if (!user?.email) throw new Error(\"Usu\\xe1rio n\\xe3o autenticado\");\n        const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\"));\n        const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.db, \"usuarios\");\n        const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n        const querySnapshot = await getDocs(q);\n        if (querySnapshot.empty) {\n            throw new Error(\"Usu\\xe1rio n\\xe3o encontrado\");\n        }\n        const userDoc = querySnapshot.docs[0];\n        const userData = userDoc.data();\n        return userData.username;\n    };\n    const updateChatSessionTime = async (chatId, totalTime)=>{\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.db, \"usuarios\", username, \"conversas\", chatId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)(chatRef, {\n                \"sessionTime.totalTime\": totalTime,\n                \"sessionTime.lastUpdated\": new Date().toISOString()\n            });\n        } catch (error) {\n            console.error(\"Erro ao atualizar tempo de sess\\xe3o:\", error);\n        }\n    };\n    const startSession = (chatId)=>{\n        const now = new Date();\n        setSessionStartTime(now);\n        setCurrentSessionWords(0);\n        setIsSessionActive(true);\n        setCurrentChatId(chatId);\n        // Iniciar timer\n        intervalRef.current = setInterval(()=>{\n            if (sessionStartTime) {\n                const elapsed = Date.now() - sessionStartTime.getTime();\n                setSessionTime(formatTime(elapsed));\n            }\n        }, 1000);\n    };\n    const endSession = async ()=>{\n        if (sessionStartTime && currentChatId) {\n            const totalTime = Date.now() - sessionStartTime.getTime();\n            await updateChatSessionTime(currentChatId, totalTime);\n        }\n        setSessionStartTime(null);\n        setCurrentSessionWords(0);\n        setIsSessionActive(false);\n        setCurrentChatId(null);\n        setSessionTime(\"0s\");\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n            intervalRef.current = null;\n        }\n    };\n    const addWordsToSession = (words)=>{\n        setCurrentSessionWords((prev)=>prev + words);\n    };\n    const shouldCreateNewSession = ()=>{\n        return currentSessionWords >= (settings?.palavrasPorSessao || 5000);\n    };\n    const getTotalWordsInMessages = (messages)=>{\n        return messages.reduce((total, message)=>{\n            if (message.content && typeof message.content === \"string\") {\n                const words = message.content.split(/\\s+/).filter((word)=>word.length > 0).length;\n                return total + words;\n            }\n            return total;\n        }, 0);\n    };\n    const getVisibleMessages = (allMessages)=>{\n        if (!allMessages || allMessages.length === 0) return [];\n        const wordLimit = settings?.palavrasPorSessao || 5000;\n        const visibleMessages = [];\n        let totalWords = 0;\n        // Percorrer mensagens de trás para frente para pegar as mais recentes\n        for(let i = allMessages.length - 1; i >= 0; i--){\n            const message = allMessages[i];\n            const messageWords = message.content && typeof message.content === \"string\" ? message.content.split(/\\s+/).filter((word)=>word.length > 0).length : 0;\n            // Se adicionar esta mensagem ultrapassar o limite, parar\n            if (totalWords + messageWords > wordLimit && visibleMessages.length > 0) {\n                break;\n            }\n            visibleMessages.unshift(message);\n            totalWords += messageWords;\n        }\n        return visibleMessages;\n    };\n    // Limpar timer quando o componente for desmontado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n            }\n        };\n    }, []);\n    // Atualizar timer quando a sessão iniciar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sessionStartTime && isSessionActive) {\n            const updateTimer = ()=>{\n                const elapsed = Date.now() - sessionStartTime.getTime();\n                setSessionTime(formatTime(elapsed));\n            };\n            intervalRef.current = setInterval(updateTimer, 1000);\n            updateTimer(); // Atualizar imediatamente\n            return ()=>{\n                if (intervalRef.current) {\n                    clearInterval(intervalRef.current);\n                }\n            };\n        }\n    }, [\n        sessionStartTime,\n        isSessionActive\n    ]);\n    const value = {\n        currentSessionWords,\n        sessionStartTime,\n        sessionTime,\n        isSessionActive,\n        startSession,\n        endSession,\n        addWordsToSession,\n        shouldCreateNewSession,\n        getVisibleMessages,\n        getTotalWordsInMessages\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\contexts\\\\SessionContext.tsx\",\n        lineNumber: 218,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/SessionContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   functions: () => (/* binding */ functions),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n/* harmony import */ var firebase_functions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/functions */ \"(ssr)/./node_modules/firebase/functions/dist/index.mjs\");\n\n\n\n\n\n// Configuração do Firebase - novo app criado - ATUALIZADO\nconst firebaseConfig = {\n    apiKey: \"AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8\",\n    authDomain: \"rafthor-0001.firebaseapp.com\",\n    projectId: \"rafthor-0001\",\n    storageBucket: \"rafthor-0001.firebasestorage.app\",\n    messagingSenderId: \"863587500028\",\n    appId: \"1:863587500028:web:ea161ddd3a1a024a7f3c79\"\n};\n// Verificar se a configuração está correta\nif (!firebaseConfig.apiKey || firebaseConfig.apiKey.length < 30) {\n    throw new Error(\"Firebase API Key inv\\xe1lida ou n\\xe3o configurada\");\n}\n// Inicializar Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Inicializar serviços\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\nconst functions = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_4__.getFunctions)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0e44538b930d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmFmdGhvci8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MDJiZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBlNDQ1MzhiOTMwZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AppearanceContext */ \"(rsc)/./src/contexts/AppearanceContext.tsx\");\n/* harmony import */ var _contexts_SessionContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/SessionContext */ \"(rsc)/./src/contexts/SessionContext.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Rafthor - AI Chatbot Platform\",\n    description: \"Uma plataforma de chatbot com m\\xfaltiplas IAs\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pt-BR\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__.AppearanceProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_SessionContext__WEBPACK_IMPORTED_MODULE_4__.SessionProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AppearanceContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/AppearanceContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppearanceProvider: () => (/* binding */ e1),
/* harmony export */   useAppearance: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\SiteRafthor\rafthor\src\contexts\AppearanceContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = proxy["useAppearance"];

const e1 = proxy["AppearanceProvider"];


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\SiteRafthor\rafthor\src\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = proxy["useAuth"];

const e1 = proxy["AuthProvider"];


/***/ }),

/***/ "(rsc)/./src/contexts/SessionContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/SessionContext.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SessionProvider: () => (/* binding */ e1),
/* harmony export */   useSession: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\SiteRafthor\rafthor\src\contexts\SessionContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = proxy["useSession"];

const e1 = proxy["SessionProvider"];


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/idb","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CSiteRafthor%5Crafthor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();