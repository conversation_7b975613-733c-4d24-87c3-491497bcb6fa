'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface AppearanceSettings {
  fonte: string;
  tamanhoFonte: number;
  palavrasPorSessao: number;
}

interface AppearanceContextType {
  settings: AppearanceSettings;
  loading: boolean;
  refreshSettings: () => Promise<void>;
}

const defaultSettings: AppearanceSettings = {
  fonte: 'Inter',
  tamanhoFonte: 14,
  palavrasPorSessao: 5000
};

const AppearanceContext = createContext<AppearanceContextType>({
  settings: defaultSettings,
  loading: true,
  refreshSettings: async () => {}
});

export const useAppearance = () => {
  const context = useContext(AppearanceContext);
  if (!context) {
    throw new Error('useAppearance must be used within an AppearanceProvider');
  }
  return context;
};

export const AppearanceProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [settings, setSettings] = useState<AppearanceSettings>(defaultSettings);
  const [loading, setLoading] = useState(true);

  const getUsernameFromFirestore = async (): Promise<string> => {
    if (!user?.email) throw new Error('Usuário não autenticado');

    const { collection, query, where, getDocs } = await import('firebase/firestore');
    const usuariosRef = collection(db, 'usuarios');
    const q = query(usuariosRef, where('email', '==', user.email));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      throw new Error('Usuário não encontrado');
    }

    const userDoc = querySnapshot.docs[0];
    const userData = userDoc.data();
    return userData.username;
  };

  const loadSettings = async () => {
    if (!user?.email) {
      setLoading(false);
      return;
    }

    try {
      const username = await getUsernameFromFirestore();
      const configRef = doc(db, 'usuarios', username, 'configuracoes', 'settings');
      const configDoc = await getDoc(configRef);

      if (configDoc.exists()) {
        const config = configDoc.data();
        if (config.aparencia) {
          setSettings(config.aparencia);
        } else {
          setSettings(defaultSettings);
        }
      } else {
        setSettings(defaultSettings);
      }
    } catch (error) {
      console.error('Erro ao carregar configurações de aparência:', error);
      setSettings(defaultSettings);
    } finally {
      setLoading(false);
    }
  };

  const refreshSettings = async () => {
    setLoading(true);
    await loadSettings();
  };

  useEffect(() => {
    loadSettings();
  }, [user]);

  const value = {
    settings,
    loading,
    refreshSettings
  };

  return (
    <AppearanceContext.Provider value={value}>
      {children}
    </AppearanceContext.Provider>
  );
};
