"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/MarkdownRenderer.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-math */ \"(app-pages-browser)/./node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rehype-katex */ \"(app-pages-browser)/./node_modules/rehype-katex/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rehype-highlight */ \"(app-pages-browser)/./node_modules/rehype-highlight/lib/index.js\");\n/* harmony import */ var _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AppearanceContext */ \"(app-pages-browser)/./src/contexts/AppearanceContext.tsx\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _styles_markdown_minimal_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/markdown-minimal.css */ \"(app-pages-browser)/./src/styles/markdown-minimal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Importar estilos do KaTeX e Markdown\n\n\n// Componente otimizado para renderização de markdown durante streaming\nconst OptimizedMarkdown = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { content, isStreaming } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_5__.Markdown, {\n        remarkPlugins: [\n            remark_gfm__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            remark_math__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        ],\n        rehypePlugins: [\n            rehype_katex__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            [\n                rehype_highlight__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                {\n                    detect: true,\n                    ignoreMissing: true\n                }\n            ]\n        ],\n        components: {\n            // Customizar renderização de código\n            code (param) {\n                let { node, inline, className, children, ...props } = param;\n                const match = /language-(\\w+)/.exec(className || \"\");\n                const language = match ? match[1] : \"text\";\n                return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"code-block-container group\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"code-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full bg-red-400/80\"\n                                                }, void 0, false, void 0, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full bg-yellow-400/80\"\n                                                }, void 0, false, void 0, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full bg-green-400/80\"\n                                                }, void 0, false, void 0, void 0)\n                                            ]\n                                        }, void 0, true, void 0, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-slate-300 capitalize\",\n                                            children: language\n                                        }, void 0, false, void 0, void 0)\n                                    ]\n                                }, void 0, true, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"copy-button opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                    onClick: ()=>navigator.clipboard.writeText(String(children)),\n                                    title: \"Copiar c\\xf3digo\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                        }, void 0, false, void 0, void 0)\n                                    }, void 0, false, void 0, void 0)\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"code-content\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: className,\n                                ...props,\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0)\n                    ]\n                }, void 0, true, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                    className: \"inline-code\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de links\n            a (param) {\n                let { children, href, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: href,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"markdown-link\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de tabelas\n            table (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"table-wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"markdown-table\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            th (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                    className: \"table-header\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            td (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                    className: \"table-cell\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de blockquotes\n            blockquote (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                    className: \"markdown-blockquote\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"quote-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de listas\n            ul (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"markdown-list unordered\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            ol (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                    className: \"markdown-list ordered\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            li (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"list-item\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de títulos\n            h1 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"markdown-heading h1\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h2 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"markdown-heading h2\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h3 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"markdown-heading h3\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h4 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                    className: \"markdown-heading h4\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h5 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                    className: \"markdown-heading h5\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h6 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                    className: \"markdown-heading h6\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de parágrafos\n            p (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"markdown-paragraph\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de separadores\n            hr (param) {\n                let { ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                    className: \"markdown-divider\",\n                    ...props\n                }, void 0, false, void 0, void 0);\n            }\n        },\n        children: content\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n}, (prevProps, nextProps)=>{\n    // Durante streaming, só re-renderizar se o conteúdo mudou significativamente\n    if (nextProps.isStreaming) {\n        // Durante streaming, re-renderizar apenas a cada 100 caracteres para melhor performance\n        const prevLength = prevProps.content.length;\n        const nextLength = nextProps.content.length;\n        const shouldUpdate = nextLength - prevLength >= 100 || nextLength < prevLength;\n        return !shouldUpdate;\n    }\n    // Fora do streaming, comportamento normal\n    return prevProps.content === nextProps.content;\n});\n_c = OptimizedMarkdown;\nconst MarkdownRenderer = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c1 = _s((param)=>{\n    let { content, className = \"\", hasWebSearch = false, webSearchAnnotations = [], isStreaming = false } = param;\n    _s();\n    const { settings } = (0,_contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_2__.useAppearance)();\n    // Função para detectar se o conteúdo contém citações de web search\n    const detectWebSearch = (text)=>{\n        // Detecta links no formato [dominio.com] que são característicos do web search\n        const webSearchPattern = /\\[[\\w.-]+\\.[\\w]+\\]/g;\n        const matches = text.match(webSearchPattern);\n        return matches !== null && matches.length > 0;\n    };\n    // Função para processar o conteúdo (OpenRouter já retorna links formatados corretamente)\n    const processWebSearchLinks = (text)=>{\n        // Como o OpenRouter já retorna os links no formato markdown correto,\n        // não precisamos processar nada. Apenas retornamos o texto original.\n        return text;\n    };\n    // Função para contar e extrair informações sobre as fontes\n    const getWebSearchInfo = (text, annotations)=>{\n        if (annotations.length > 0) {\n            // Usar annotations se disponíveis\n            const uniqueDomains = new Set(annotations.map((annotation)=>{\n                try {\n                    return new URL(annotation.url).hostname.replace(\"www.\", \"\");\n                } catch (e) {\n                    return annotation.url;\n                }\n            }));\n            return {\n                sourceCount: annotations.length,\n                sources: Array.from(uniqueDomains)\n            };\n        }\n        // Fallback: detectar pelos padrões no texto (formato markdown link)\n        const webSearchPattern = /\\[[\\w.-]+\\.[\\w]+\\]\\([^)]+\\)/g;\n        const matches = text.match(webSearchPattern) || [];\n        const sourceSet = new Set(matches.map((match)=>{\n            // Extrair o domínio do formato [dominio.com](url)\n            const domainMatch = match.match(/\\[([\\w.-]+\\.[\\w]+)\\]/);\n            return domainMatch ? domainMatch[1] : match;\n        }));\n        const uniqueSources = Array.from(sourceSet);\n        return {\n            sourceCount: matches.length,\n            sources: uniqueSources\n        };\n    };\n    // Usar useMemo para otimizar processamento durante streaming\n    const { isWebSearchMessage, webSearchInfo, processedContent } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const isWebSearch = hasWebSearch || detectWebSearch(content);\n        const searchInfo = isWebSearch ? getWebSearchInfo(content, webSearchAnnotations) : {\n            sourceCount: 0,\n            sources: []\n        };\n        const processed = isWebSearch ? processWebSearchLinks(content) : content;\n        return {\n            isWebSearchMessage: isWebSearch,\n            webSearchInfo: searchInfo,\n            processedContent: processed\n        };\n    }, [\n        content,\n        hasWebSearch,\n        webSearchAnnotations\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rafthor-markdown \".concat(className, \" \").concat(isStreaming ? \"streaming\" : \"\"),\n        children: [\n            isWebSearchMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"web-search-indicator\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"search-badge\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"search-icon\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M9 12l2 2 4-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Busca na Web\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"source-count\",\n                                children: webSearchInfo.sourceCount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, undefined),\n                    webSearchInfo.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"source-list\",\n                        children: [\n                            webSearchInfo.sources.slice(0, 3).map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"source-tag\",\n                                    children: source\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 17\n                                }, undefined)),\n                            webSearchInfo.sources.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"source-tag more\",\n                                children: [\n                                    \"+\",\n                                    webSearchInfo.sources.length - 3\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                lineNumber: 317,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OptimizedMarkdown, {\n                content: processedContent,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                lineNumber: 346,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, undefined);\n}, \"uPChLkmfSSs4lsILu6LzRGho8nM=\", false, function() {\n    return [\n        _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_2__.useAppearance\n    ];\n})), \"uPChLkmfSSs4lsILu6LzRGho8nM=\", false, function() {\n    return [\n        _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_2__.useAppearance\n    ];\n});\n_c2 = MarkdownRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MarkdownRenderer);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"OptimizedMarkdown\");\n$RefreshReg$(_c1, \"MarkdownRenderer$React.memo\");\n$RefreshReg$(_c2, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AppearanceContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/AppearanceContext.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppearanceProvider: function() { return /* binding */ AppearanceProvider; },\n/* harmony export */   useAppearance: function() { return /* binding */ useAppearance; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAppearance,AppearanceProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst defaultSettings = {\n    fonte: \"Inter\",\n    tamanhoFonte: 14,\n    palavrasPorSessao: 5000\n};\nconst AppearanceContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    settings: defaultSettings,\n    loading: true,\n    refreshSettings: async ()=>{}\n});\nconst useAppearance = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppearanceContext);\n    if (!context) {\n        throw new Error(\"useAppearance must be used within an AppearanceProvider\");\n    }\n    return context;\n};\n_s(useAppearance, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AppearanceProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSettings);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) throw new Error(\"Usu\\xe1rio n\\xe3o autenticado\");\n        const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n        const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n        const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n        const querySnapshot = await getDocs(q);\n        if (querySnapshot.empty) {\n            throw new Error(\"Usu\\xe1rio n\\xe3o encontrado\");\n        }\n        const userDoc = querySnapshot.docs[0];\n        const userData = userDoc.data();\n        return userData.username;\n    };\n    const loadSettings = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) {\n            setLoading(false);\n            return;\n        }\n        try {\n            const username = await getUsernameFromFirestore();\n            const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(configRef);\n            if (configDoc.exists()) {\n                const config = configDoc.data();\n                if (config.aparencia) {\n                    setSettings(config.aparencia);\n                } else {\n                    setSettings(defaultSettings);\n                }\n            } else {\n                setSettings(defaultSettings);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar configura\\xe7\\xf5es de apar\\xeancia:\", error);\n            setSettings(defaultSettings);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const refreshSettings = async ()=>{\n        setLoading(true);\n        await loadSettings();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSettings();\n    }, [\n        user\n    ]);\n    const value = {\n        settings,\n        loading,\n        refreshSettings\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppearanceContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\contexts\\\\AppearanceContext.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AppearanceProvider, \"r6/9LFxsQKNwixrIUf43J2KqRVM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = AppearanceProvider;\nvar _c;\n$RefreshReg$(_c, \"AppearanceProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AppearanceContext.tsx\n"));

/***/ })

});